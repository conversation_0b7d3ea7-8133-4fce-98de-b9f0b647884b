# syntax=docker/dockerfile:1
# Multi-stage Dockerfile for code-server with VS Code extension
# Security-enhanced version with pre-compiled extension and BuildKit optimization

# ============================================================================
# BUILDER STAGE: Compile the VS Code extension
# ============================================================================
FROM node:20-alpine AS builder

# Set working directory for extension build
WORKDIR /build/extension

# Copy package files first for better Docker layer caching
COPY extensions/ai-chat-extension/package.json ./
COPY extensions/ai-chat-extension/package-lock.json ./

# Install dependencies with BuildKit cache mounting for faster builds
RUN --mount=type=cache,target=/root/.npm \
  npm ci --only=production=false

# Copy extension source code
COPY extensions/ai-chat-extension/ ./

# Build the extension (compile TypeScript and package)
RUN npm run compile && \
  npm run package

# ============================================================================
# FINAL STAGE: code-server with pre-built extension
# ============================================================================
FROM codercom/code-server:latest

# Install Node.js LTS, npm, curl and create necessary directories as root
USER root

# Install Node.js 20 LTS and npm (required at runtime for extension functionality)
# Include security updates and cache mounting for faster builds
RUN --mount=type=cache,target=/var/cache/apt \
  --mount=type=cache,target=/var/lib/apt/lists \
  curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
  apt-get update && \
  apt-get install -y \
  nodejs \
  curl \
  ca-certificates \
  && apt-get upgrade -y \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean

# Verify Node.js and npm installation
RUN node --version && npm --version

# Create extension directory structure with proper permissions
RUN mkdir -p /home/<USER>/.local/share/code-server/extensions/ai-chat-extension && \
  chown -R coder:coder /home/<USER>/.local/share/code-server

# Switch to non-root user for security
USER coder
WORKDIR /home/<USER>

# Copy extension installation script and config files
COPY --chown=coder:coder install-extensions.sh ./install-extensions.sh
COPY --chown=coder:coder extensions.json ./extensions.json
COPY --chown=coder:coder settings.json ./settings.json

# Make extension installation script executable
RUN chmod +x ./install-extensions.sh

# Best Practice: Copy compiled extension files directly to extensions directory
# This approach is more reliable than VSIX installation in Docker builds
RUN mkdir -p /home/<USER>/.local/share/code-server/extensions/ai-chat-extension

# Copy essential extension files from builder stage
COPY --from=builder --chown=coder:coder /build/extension/package.json /home/<USER>/.local/share/code-server/extensions/ai-chat-extension/
COPY --from=builder --chown=coder:coder /build/extension/dist/ /home/<USER>/.local/share/code-server/extensions/ai-chat-extension/dist/

# Copy any additional extension assets if they exist (using conditional copying)
RUN if [ -f "/build/extension/README.md" ]; then \
  cp /build/extension/README.md /home/<USER>/.local/share/code-server/extensions/ai-chat-extension/; \
  fi && \
  if [ -f "/build/extension/CHANGELOG.md" ]; then \
  cp /build/extension/CHANGELOG.md /home/<USER>/.local/share/code-server/extensions/ai-chat-extension/; \
  fi

# Verify extension files are in place
RUN echo "Extension files installed:" && \
  ls -la /home/<USER>/.local/share/code-server/extensions/ai-chat-extension/

# Expose port
EXPOSE 8080

# Health check for code-server service monitoring
# Enhanced health check with better reliability
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/healthz || curl -f http://localhost:8080/login || exit 1

# Note: Removed VOLUME instruction to prevent runtime volume overrides
# The compiled extension is now permanently baked into the image

# Set resource limits as labels for documentation
LABEL org.opencontainers.image.title="AI Coding Agent - Code Server" \
  org.opencontainers.image.description="Browser-based VS Code with AI chat extension" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team"

# Run code-server with optimized configuration
CMD ["code-server", "--bind-addr", "0.0.0.0:8080", "--auth", "password", "--disable-telemetry"]