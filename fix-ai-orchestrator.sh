#!/bin/bash

# AI Orchestrator Container Debug and Recovery Script
# This script helps diagnose and fix ai-orchestrator startup issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if container is running
check_container_status() {
    log_info "Checking ai-orchestrator container status..."

    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "ai-orchestrator"; then
        log_success "Container is running"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep ai-orchestrator
        return 0
    elif docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep -q "ai-orchestrator"; then
        log_warning "Container exists but is not running"
        docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep ai-orchestrator
        return 1
    else
        log_error "Container does not exist"
        return 2
    fi
}

# Function to check container logs
check_container_logs() {
    log_info "Checking container logs for errors..."

    if docker ps -a --format "{{.Names}}" | grep -q "ai-orchestrator"; then
        echo "=== Last 50 lines of container logs ==="
        docker logs --tail 50 ai-orchestrator 2>&1 | head -50
        echo "======================================="

        # Check for specific error patterns
        log_info "Analyzing logs for common issues..."

        if docker logs ai-orchestrator 2>&1 | grep -q "ImportError.*get_chat_manager"; then
            log_error "Found ImportError for get_chat_manager"
            echo "  - This indicates the WebSocket manager function is missing"
            echo "  - Solution: The get_chat_manager function has been added to websocket_manager.py"
        fi

        if docker logs ai-orchestrator 2>&1 | grep -q "ModuleNotFoundError"; then
            log_error "Found missing module errors"
            echo "  - This indicates missing Python dependencies"
            echo "  - Solution: Rebuild the container with updated requirements"
        fi

        if docker logs ai-orchestrator 2>&1 | grep -q "HTTPConnectionPool.*health"; then
            log_error "Found health check connection errors"
            echo "  - This indicates the application is not starting properly"
            echo "  - Solution: Fix the underlying import/startup issues first"
        fi

        if docker logs ai-orchestrator 2>&1 | grep -q "address already in use"; then
            log_error "Found port conflict"
            echo "  - Solution: Check for other services using port 8000"
        fi

    else
        log_error "Container does not exist - cannot check logs"
        return 1
    fi
}

# Function to test application startup
test_application_startup() {
    log_info "Testing application startup with debug script..."

    if docker ps --format "{{.Names}}" | grep -q "ai-orchestrator"; then
        log_info "Running startup diagnostics inside container..."
        docker exec ai-orchestrator python debug_startup.py
    else
        log_warning "Container not running - starting debug container..."
        docker-compose -f docker-compose.yml -f docker-compose.debug.yml run --rm ai-orchestrator python debug_startup.py
    fi
}

# Function to fix common issues
fix_common_issues() {
    log_info "Attempting to fix common issues..."

    # Stop the problematic container
    log_info "Stopping ai-orchestrator container..."
    docker-compose stop ai-orchestrator 2>/dev/null || true

    # Remove the container to force recreation
    log_info "Removing ai-orchestrator container..."
    docker-compose rm -f ai-orchestrator 2>/dev/null || true

    # Rebuild the container with latest fixes
    log_info "Rebuilding ai-orchestrator container..."
    docker-compose build --no-cache ai-orchestrator

    # Start in debug mode first
    log_info "Starting container in debug mode..."
    docker-compose -f docker-compose.yml -f docker-compose.debug.yml up -d ai-orchestrator

    # Wait for startup
    log_info "Waiting for application to start..."
    sleep 30

    # Check if it's working
    if check_health_endpoint; then
        log_success "Application started successfully in debug mode!"
        log_info "Switching to normal mode..."
        docker-compose -f docker-compose.debug.yml down
        docker-compose up -d ai-orchestrator
    else
        log_error "Application still failing in debug mode"
        log_info "Check debug logs for more information"
        return 1
    fi
}

# Function to check health endpoint
check_health_endpoint() {
    log_info "Testing health endpoint..."

    # Wait for the service to be available
    for i in {1..10}; do
        if curl -f -s http://localhost:8000/health >/dev/null 2>&1; then
            log_success "Health endpoint is responding"
            curl -s http://localhost:8000/health | python -m json.tool
            return 0
        else
            log_info "Waiting for health endpoint... (attempt $i/10)"
            sleep 5
        fi
    done

    log_error "Health endpoint is not responding"
    return 1
}

# Function to show recovery suggestions
show_recovery_suggestions() {
    log_info "Recovery suggestions:"
    echo ""
    echo "1. Quick Fix (Recommended):"
    echo "   ./fix-ai-orchestrator.sh --fix"
    echo ""
    echo "2. Manual Debug:"
    echo "   docker-compose -f docker-compose.yml -f docker-compose.debug.yml up ai-orchestrator"
    echo ""
    echo "3. Full Reset:"
    echo "   docker-compose down"
    echo "   docker-compose build --no-cache"
    echo "   docker-compose up -d"
    echo ""
    echo "4. Check Dependencies:"
    echo "   docker-compose exec ai-orchestrator pip list"
    echo ""
    echo "5. Interactive Debug:"
    echo "   docker-compose exec ai-orchestrator bash"
    echo "   python debug_startup.py"
    echo ""
}

# Main function
main() {
    echo "=============================================="
    echo "AI Orchestrator Container Debug & Recovery"
    echo "=============================================="
    echo ""

    case "${1:-}" in
        "--fix"|"-f")
            log_info "Running automatic fix..."
            fix_common_issues
            ;;
        "--logs"|"-l")
            check_container_logs
            ;;
        "--test"|"-t")
            test_application_startup
            ;;
        "--health"|"-h")
            check_health_endpoint
            ;;
        *)
            # Default diagnostic flow
            log_info "Running full diagnostic..."
            echo ""

            # Step 1: Check container status
            container_status=$(check_container_status; echo $?)
            echo ""

            # Step 2: Check logs if container exists
            if [ "$container_status" -ne 2 ]; then
                check_container_logs
                echo ""
            fi

            # Step 3: Test health endpoint if running
            if [ "$container_status" -eq 0 ]; then
                check_health_endpoint
                echo ""
            fi

            # Step 4: Run startup test
            # test_application_startup
            # echo ""

            # Step 5: Show suggestions
            show_recovery_suggestions
            ;;
    esac
}

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "OPTIONS:"
    echo "  --fix, -f     Run automatic fix for common issues"
    echo "  --logs, -l    Show and analyze container logs"
    echo "  --test, -t    Test application startup"
    echo "  --health, -h  Test health endpoint"
    echo "  --help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Run full diagnostic"
    echo "  $0 --fix        # Attempt automatic fix"
    echo "  $0 --logs       # Check container logs"
}

# Check for help flag
if [[ "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# Run main function
main "$@"