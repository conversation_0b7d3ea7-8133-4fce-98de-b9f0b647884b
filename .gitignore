# Environment and Configuration
.env
.env.local
.env.production
.env.development

# Docker Secrets and Sensitive Data
secrets/
*.key
*.pem
*.p12
*.jks

# Database and Cache Data
postgres_data/
redis_data/
*.db
*.sqlite
*.sqlite3

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing and Coverage
.pytest_cache/
.coverage
.coverage.*
htmlcov/
.tox/
.nox/
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Virtual Environments
.venv/
venv/
ENV/
env/
.env/

# IDEs and Editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Node.js
node_modules/
.npm
.eslintcache
.next/
out/
.nuxt
dist

# Temporary files
tmp/
temp/
.tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.docker/
docker-compose.override.yml

# Backup files
*.bak
*.backup
*.old

# AI/ML Models and Data
models/
*.pkl
*.joblib
*.h5
*.onnx

# Monitoring and Analytics
grafana/data/
prometheus/data/
elasticsearch/data/

# Documentation builds
docs/_build/
site/

# Git
*.origsecrets/
