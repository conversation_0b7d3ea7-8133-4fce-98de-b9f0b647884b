# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: Comprehensive tests for enhanced LLM service

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch, ANY
from typing import Dict, Any

from src.services.enhanced_llm_service import EnhancedLLMService
from src.models.llm_models import (
    LLMProvider, GenerateRequest, LLMResponse, LLMModel, ModelPullRequest,
    ProviderStatus, UsageStats, RateLimitExceededError, ProviderUnavailableError,
    InvalidAPIKeyError, GenerationError, ModelNotFoundError
)


class TestEnhancedLLMService:
    """Test suite for EnhancedLLMService."""

    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client."""
        redis_mock = AsyncMock()
        redis_mock.get.return_value = None
        redis_mock.incr.return_value = None
        redis_mock.expire.return_value = None
        redis_mock.incrbyfloat.return_value = None
        return redis_mock

    @pytest.fixture
    def llm_service(self, mock_redis):
        """Create LLM service instance with mocked dependencies."""
        with patch.dict('os.environ', {
            'OLLAMA_BASE_URL': 'http://localhost:11434',
            'OPENROUTER_API_KEY': 'test-openrouter-key',
            'OPENAI_API_KEY': 'test-openai-key',
            'ANTHROPIC_API_KEY': 'test-anthropic-key',
            'DEFAULT_LOCAL_PROVIDER': 'ollama',
            'DEFAULT_CLOUD_PROVIDER': 'openrouter',
            'ENABLE_CLOUD_FALLBACK': 'true'
        }):
            return EnhancedLLMService(redis_client=mock_redis)

    @pytest.mark.asyncio
    async def test_provider_configuration_loading(self, llm_service):
        """Test that provider configurations are loaded correctly."""
        assert LLMProvider.OLLAMA in llm_service.providers
        assert LLMProvider.OPENROUTER in llm_service.providers
        assert LLMProvider.OPENAI in llm_service.providers
        assert LLMProvider.ANTHROPIC in llm_service.providers

        ollama_config = llm_service.providers[LLMProvider.OLLAMA]
        assert ollama_config['base_url'] == 'http://localhost:11434'
        assert ollama_config['enabled'] is True

        openrouter_config = llm_service.providers[LLMProvider.OPENROUTER]
        assert openrouter_config['api_key'] == 'test-openrouter-key'
        assert openrouter_config['enabled'] is True

    @pytest.mark.asyncio
    async def test_api_key_validation_ollama(self, llm_service):
        """Test API key validation for Ollama (no key required)."""
        result = await llm_service.validate_api_key(LLMProvider.OLLAMA)
        assert result is True

    @pytest.mark.asyncio
    async def test_api_key_validation_openrouter_success(self, llm_service):
        """Test successful OpenRouter API key validation."""
        mock_response = AsyncMock()
        mock_response.status = 200

        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response

            result = await llm_service.validate_api_key(LLMProvider.OPENROUTER)
            assert result is True

    @pytest.mark.asyncio
    async def test_api_key_validation_openrouter_failure(self, llm_service):
        """Test failed OpenRouter API key validation."""
        mock_response = AsyncMock()
        mock_response.status = 401

        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response

            result = await llm_service.validate_api_key(LLMProvider.OPENROUTER)
            assert result is False

    @pytest.mark.asyncio
    async def test_rate_limit_check_with_redis(self, llm_service, mock_redis):
        """Test rate limit checking with Redis."""
        # Mock Redis responses
        mock_redis.get.return_value = "5"  # 5 requests used

        rate_limit = await llm_service.check_rate_limit(LLMProvider.OPENROUTER, "test_user")

        assert rate_limit.requests_per_minute == 20  # Default for OpenRouter
        assert rate_limit.requests_remaining == 15   # 20 - 5
        assert rate_limit.reset_time > 0

    @pytest.mark.asyncio
    async def test_rate_limit_check_without_redis(self):
        """Test rate limit checking without Redis (fallback behavior)."""
        service = EnhancedLLMService(redis_client=None)

        rate_limit = await service.check_rate_limit(LLMProvider.OPENROUTER, "test_user")

        assert rate_limit.requests_remaining == 999999  # Unlimited fallback

    @pytest.mark.asyncio
    async def test_rate_limit_increment(self, llm_service, mock_redis):
        """Test rate limit increment functionality."""
        await llm_service.increment_rate_limit(LLMProvider.OPENROUTER, "test_user", cost=0.05)

        # Verify Redis calls
        mock_redis.incr.assert_called()
        mock_redis.expire.assert_called()
        mock_redis.incrbyfloat.assert_called_with(ANY, 0.05)

    @pytest.mark.asyncio
    async def test_ollama_connection_test_success(self, llm_service):
        """Test successful Ollama connection test."""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {"models": [{"name": "llama3.2"}]}

        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response

            status = await llm_service.test_provider_connection(LLMProvider.OLLAMA)

            assert status.provider == LLMProvider.OLLAMA
            assert status.available is True
            assert status.api_key_configured is True

    @pytest.mark.asyncio
    async def test_ollama_connection_test_failure(self, llm_service):
        """Test failed Ollama connection test."""
        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.get.side_effect = Exception("Connection failed")

            status = await llm_service.test_provider_connection(LLMProvider.OLLAMA)

            assert status.provider == LLMProvider.OLLAMA
            assert status.available is False
            assert "Connection failed" in status.error_message

    @pytest.mark.asyncio
    async def test_list_ollama_models_success(self, llm_service):
        """Test successful Ollama model listing."""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "models": [
                {
                    "name": "llama3.2:latest",
                    "size": **********,
                    "modified_at": "2024-01-01T00:00:00Z"
                },
                {
                    "name": "codellama:13b",
                    "size": **********,
                    "modified_at": "2024-01-02T00:00:00Z"
                }
            ]
        }

        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response

            models = await llm_service.list_available_models(LLMProvider.OLLAMA)

            assert len(models) == 2
            assert models[0].name == "llama3.2:latest"
            assert models[0].provider == LLMProvider.OLLAMA
            assert models[0].size == **********

    @pytest.mark.asyncio
    async def test_list_cloud_models(self, llm_service):
        """Test cloud provider model listing."""
        models = await llm_service.list_available_models(LLMProvider.OPENROUTER)

        assert len(models) > 0
        assert all(model.provider == LLMProvider.OPENROUTER for model in models)
        assert any("llama" in model.name.lower() for model in models)

    @pytest.mark.asyncio
    async def test_generate_ollama_success(self, llm_service):
        """Test successful text generation with Ollama."""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "message": {"content": "Hello, this is a test response!"},
            "done": True,
            "prompt_eval_count": 10,
            "eval_count": 15,
            "total_duration": **********,
            "done_reason": "stop"
        }

        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response

            request = GenerateRequest(
                prompt="Test prompt",
                provider=LLMProvider.OLLAMA,
                model="llama3.2",
                temperature=0.7,
                max_tokens=100
            )

            response = await llm_service.generate(request)

            assert isinstance(response, LLMResponse)
            assert response.content == "Hello, this is a test response!"
            assert response.provider == LLMProvider.OLLAMA
            assert response.model == "llama3.2"
            assert response.usage.prompt_tokens == 10
            assert response.usage.completion_tokens == 15

    @pytest.mark.asyncio
    async def test_generate_openrouter_success(self, llm_service):
        """Test successful text generation with OpenRouter."""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "choices": [{
                "message": {"content": "OpenRouter response!"},
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 8,
                "completion_tokens": 12,
                "total_tokens": 20
            }
        }

        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response

            request = GenerateRequest(
                prompt="Test prompt",
                provider=LLMProvider.OPENROUTER,
                model="meta-llama/llama-3.1-8b-instruct:free",
                temperature=0.5
            )

            response = await llm_service.generate(request)

            assert isinstance(response, LLMResponse)
            assert response.content == "OpenRouter response!"
            assert response.provider == LLMProvider.OPENROUTER
            assert response.usage.prompt_tokens == 8
            assert response.usage.completion_tokens == 12
            assert response.usage.cost == 0.0  # Free tier model

    @pytest.mark.asyncio
    async def test_generate_rate_limit_exceeded(self, llm_service, mock_redis):
        """Test generation with rate limit exceeded."""
        # Mock rate limit exceeded
        mock_redis.get.return_value = "20"  # At limit

        request = GenerateRequest(
            prompt="Test prompt",
            provider=LLMProvider.OPENROUTER
        )

        with pytest.raises(RateLimitExceededError) as exc_info:
            await llm_service.generate(request)

        assert "Rate limit exceeded" in str(exc_info.value)
        assert exc_info.value.provider == LLMProvider.OPENROUTER

    @pytest.mark.asyncio
    async def test_generate_cost_limit_exceeded(self, llm_service, mock_redis):
        """Test generation with cost limit exceeded."""
        # Mock cost limit exceeded
        mock_redis.get.side_effect = ["5", "15.0"]  # 5 requests, $15 cost (at $10 limit)

        request = GenerateRequest(
            prompt="Test prompt",
            provider=LLMProvider.OPENROUTER
        )

        with pytest.raises(RateLimitExceededError) as exc_info:
            await llm_service.generate(request)

        assert "Cost limit exceeded" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_with_fallback(self, llm_service):
        """Test generation with fallback from Ollama to cloud."""
        # Mock Ollama failure
        mock_ollama_response = AsyncMock()
        mock_ollama_response.status = 500
        mock_ollama_response.text.return_value = "Server error"

        # Mock OpenRouter success
        mock_openrouter_response = AsyncMock()
        mock_openrouter_response.status = 200
        mock_openrouter_response.json.return_value = {
            "choices": [{
                "message": {"content": "Fallback response!"},
                "finish_reason": "stop"
            }],
            "usage": {"prompt_tokens": 5, "completion_tokens": 8, "total_tokens": 13}
        }

        with patch('aiohttp.ClientSession') as mock_session:
            # First call (Ollama) fails, second call (OpenRouter) succeeds
            mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.side_effect = [
                mock_ollama_response,
                mock_openrouter_response
            ]

            request = GenerateRequest(
                prompt="Test prompt",
                provider=LLMProvider.OLLAMA  # Will fallback to OpenRouter
            )

            response = await llm_service.generate(request)

            assert response.content == "Fallback response!"
            assert response.provider == LLMProvider.OPENROUTER

    @pytest.mark.asyncio
    async def test_pull_model_success(self, llm_service):
        """Test successful model pulling."""
        mock_response = AsyncMock()
        mock_response.status = 200

        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response

            request = ModelPullRequest(
                model_name="llama3.2:latest",
                provider=LLMProvider.OLLAMA
            )

            result = await llm_service.pull_model(request)

            assert result.success is True
            assert "Successfully pulled" in result.message
            assert result.model_name == "llama3.2:latest"

    @pytest.mark.asyncio
    async def test_pull_model_unsupported_provider(self, llm_service):
        """Test model pulling with unsupported provider."""
        request = ModelPullRequest(
            model_name="gpt-4",
            provider=LLMProvider.OPENAI
        )

        with pytest.raises(ProviderUnavailableError) as exc_info:
            await llm_service.pull_model(request)

        assert "only supported for Ollama" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_health_check_all_healthy(self, llm_service):
        """Test health check with all providers healthy."""
        # Mock all providers as healthy
        with patch.object(llm_service, 'test_provider_connection') as mock_test:
            mock_test.return_value = ProviderStatus(
                provider=LLMProvider.OLLAMA,
                available=True,
                api_key_configured=True,
                response_time_ms=50
            )

            health = await llm_service.health_check()

            assert health.status == "healthy"
            assert len(health.providers) == 4  # All providers tested

    @pytest.mark.asyncio
    async def test_health_check_degraded(self, llm_service):
        """Test health check with some providers unhealthy."""
        def mock_connection_test(provider):
            if provider == LLMProvider.OLLAMA:
                return ProviderStatus(provider=provider, available=True)
            else:
                return ProviderStatus(provider=provider, available=False, error_message="API key invalid")

        with patch.object(llm_service, 'test_provider_connection', side_effect=mock_connection_test):
            health = await llm_service.health_check()

            assert health.status == "degraded"

    @pytest.mark.asyncio
    async def test_circuit_breaker_pattern(self, llm_service):
        """Test circuit breaker functionality."""
        # Test failure tracking
        llm_service._update_circuit_breaker(LLMProvider.OLLAMA, success=False)
        breaker = llm_service.circuit_breakers[LLMProvider.OLLAMA]
        assert breaker['failure_count'] == 1
        assert breaker['state'] == 'closed'

        # Test circuit opening after multiple failures
        for _ in range(5):
            llm_service._update_circuit_breaker(LLMProvider.OLLAMA, success=False)

        breaker = llm_service.circuit_breakers[LLMProvider.OLLAMA]
        assert breaker['state'] == 'open'

        # Test recovery after success
        llm_service._update_circuit_breaker(LLMProvider.OLLAMA, success=True)
        breaker = llm_service.circuit_breakers[LLMProvider.OLLAMA]
        assert breaker['failure_count'] == 0

    @pytest.mark.asyncio
    async def test_cost_calculation(self, llm_service):
        """Test cost calculation for different providers and models."""
        # Test free model
        cost = llm_service._calculate_cost(
            "meta-llama/llama-3.1-8b-instruct:free",
            {"total_tokens": 1000},
            LLMProvider.OPENROUTER
        )
        assert cost == 0.0

        # Test paid model
        cost = llm_service._calculate_cost(
            "meta-llama/llama-3.1-70b-instruct:nitro",
            {"total_tokens": 1000},
            LLMProvider.OPENROUTER
        )
        assert cost == 0.9  # 1000 tokens * $0.9 per 1k tokens

        # Test Ollama (should be None)
        cost = llm_service._calculate_cost(
            "llama3.2",
            {"total_tokens": 1000},
            LLMProvider.OLLAMA
        )
        assert cost is None

    @pytest.mark.asyncio
    async def test_usage_statistics(self, llm_service):
        """Test usage statistics collection."""
        # Add some mock data
        llm_service.request_stats[LLMProvider.OLLAMA]['total'] = 100
        llm_service.request_stats[LLMProvider.OLLAMA]['success'] = 95
        llm_service.request_stats[LLMProvider.OLLAMA]['failed'] = 5

        stats = await llm_service.get_usage_statistics()

        assert stats['total_requests'] == 100
        assert stats['providers'][LLMProvider.OLLAMA]['requests'] == 100
        assert stats['providers'][LLMProvider.OLLAMA]['success_rate'] == 0.95

    def test_request_validation(self):
        """Test request model validation."""
        # Valid request
        request = GenerateRequest(prompt="Test prompt")
        assert request.prompt == "Test prompt"
        assert request.temperature == 0.7  # Default

        # Invalid temperature
        with pytest.raises(ValueError):
            GenerateRequest(prompt="Test", temperature=3.0)

        # Invalid max_tokens
        with pytest.raises(ValueError):
            GenerateRequest(prompt="Test", max_tokens=0)

        # Empty prompt
        with pytest.raises(ValueError):
            GenerateRequest(prompt="")

    def test_model_validation(self):
        """Test model validation."""
        model = LLMModel(
            name="test-model",
            provider=LLMProvider.OLLAMA,
            size=1000000,
            context_length=4096
        )
        assert model.name == "test-model"
        assert model.provider == LLMProvider.OLLAMA
        assert model.status.value == "available"  # Default

if __name__ == "__main__":
    pytest.main([__file__])