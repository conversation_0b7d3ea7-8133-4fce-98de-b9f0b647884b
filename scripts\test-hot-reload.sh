#!/bin/bash
# Hot-Reload Test Script for AI Coding Agent
# Tests the success metric: "You can change a Python file in the code-server UI, and the ai-orchestrator service automatically restarts"

set -e

echo "🚀 AI Coding Agent Hot-Reload Test"
echo "=================================="

# Check if Docker Compose is running
echo "📋 Checking Docker Compose status..."
if ! docker-compose ps | grep -q "ai-orchestrator.*Up"; then
    echo "❌ ai-orchestrator is not running. Please start development environment first:"
    echo "   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d"
    exit 1
fi

echo "✅ ai-orchestrator is running"

# Check if code-server is accessible
echo "📋 Checking code-server accessibility..."
if ! curl -s http://localhost:8080 > /dev/null; then
    echo "❌ code-server is not accessible at http://localhost:8080"
    echo "   Please check the code-server container status"
    exit 1
fi

echo "✅ code-server is accessible"

# Check if ai-orchestrator API is responding
echo "📋 Checking ai-orchestrator API..."
if ! curl -s http://localhost:8000/health > /dev/null; then
    echo "❌ ai-orchestrator API is not responding at http://localhost:8000"
    echo "   Please check the ai-orchestrator container logs"
    exit 1
fi

echo "✅ ai-orchestrator API is responding"

# Test hot-reload functionality
echo "🔥 Testing Hot-Reload Functionality"
echo "==================================="

echo "📝 Step 1: Creating a test endpoint in main.py..."

# Backup original main.py
cp containers/ai-orchestrator/src/main.py containers/ai-orchestrator/src/main.py.backup

# Add a test endpoint
cat >> containers/ai-orchestrator/src/main.py << 'EOF'

@app.get("/test/hot-reload")
async def test_hot_reload():
    """Test endpoint to verify hot-reload functionality."""
    return {
        "message": "Hot-reload is working!",
        "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "test_id": "hot-reload-test"
    }
EOF

echo "✅ Test endpoint added to main.py"

echo "⏳ Waiting for auto-reload (10 seconds)..."
sleep 10

echo "📋 Testing new endpoint..."
if curl -s http://localhost:8000/test/hot-reload | grep -q "Hot-reload is working"; then
    echo "🎉 SUCCESS! Hot-reload is working correctly!"
    echo "   ✅ The new endpoint is accessible"
    echo "   ✅ Changes were automatically detected"
    echo "   ✅ Server restarted automatically"
else
    echo "❌ FAILED! Hot-reload is not working"
    echo "   The new endpoint is not accessible"
    echo "   Please check the ai-orchestrator logs:"
    echo "   docker-compose logs ai-orchestrator"
fi

# Restore original main.py
echo "🔄 Restoring original main.py..."
mv containers/ai-orchestrator/src/main.py.backup containers/ai-orchestrator/src/main.py

echo "⏳ Waiting for restore reload (5 seconds)..."
sleep 5

# Final verification
echo "📋 Final verification..."
if curl -s http://localhost:8000/test/hot-reload 2>/dev/null | grep -q "404\|Not Found" || ! curl -s http://localhost:8000/test/hot-reload >/dev/null 2>&1; then
    echo "✅ Cleanup successful - test endpoint removed"
else
    echo "⚠️  Test endpoint still accessible (may need manual cleanup)"
fi

echo ""
echo "🎯 Manual Testing Instructions:"
echo "==============================="
echo "1. Open code-server: http://localhost:8080"
echo "2. Navigate to: ai-orchestrator/src/main.py"
echo "3. Make a small change (add a comment or modify a docstring)"
echo "4. Save the file (Ctrl+S)"
echo "5. Watch the terminal for ai-orchestrator restart messages"
echo "6. Verify changes at: http://localhost:8000"
echo ""
echo "📊 Monitoring Commands:"
echo "======================"
echo "# Watch ai-orchestrator logs in real-time"
echo "docker-compose logs -f ai-orchestrator"
echo ""
echo "# Check container status"
echo "docker-compose ps"
echo ""
echo "# Test API health"
echo "curl http://localhost:8000/health"