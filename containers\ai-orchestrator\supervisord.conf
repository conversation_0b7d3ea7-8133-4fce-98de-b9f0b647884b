[supervisord]
nodaemon=true
user=coder
pidfile=/home/<USER>/supervisord.pid
logfile=/var/log/supervisor/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info

[program:code-server]
command=/home/<USER>/.local/bin/code-server --bind-addr 0.0.0.0:8080 --auth password --password %(ENV_CODE_SERVER_PASSWORD)s /app
directory=/app
user=coder
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/code-server.log
stderr_logfile=/home/<USER>/code-server.err.log
environment=HOME="/home/<USER>"

[program:ai-orchestrator]
command=/home/<USER>/.local/bin/uvicorn containers.ai-orchestrator.src.main:app --host 0.0.0.0 --port 8000 --reload
directory=/app
user=coder
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/ai-orchestrator.log
stderr_logfile=/home/<USER>/ai-orchestrator.err.log

[program:admin-dashboard]
command=/home/<USER>/.local/bin/uvicorn containers.admin-dashboard.src.main:app --host 0.0.0.0 --port 3000 --reload
directory=/app/containers/admin-dashboard/src
user=coder
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/admin-dashboard.log
stderr_logfile=/home/<USER>/admin-dashboard.err.log
