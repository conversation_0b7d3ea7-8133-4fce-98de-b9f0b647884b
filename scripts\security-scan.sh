#!/bin/bash
# Docker Security Scanning Script for AI Coding Agent Project
# Uses Docker Scout for vulnerability scanning

set -e

echo "🔍 Docker Security Scanning with Docker Scout..."

# Check if Docker Scout is available
if ! docker scout version &> /dev/null; then
    echo "❌ Docker Scout is not available"
    echo "Please install Docker Scout plugin or use Docker Desktop"
    echo "  - Install: docker scout install"
    echo "  - Or use CLI: curl -sSfL https://raw.githubusercontent.com/docker/scout-cli/main/install.sh | sh -s --"
    exit 1
fi

# Define images to scan
IMAGES=(
    "ai-orchestrator"
    "admin-dashboard"
    "code-server"
    "postgresql"
    "ollama"
)

SCAN_FAILED=false

echo "📦 Building images for scanning..."

# Build all images first
for image in "${IMAGES[@]}"; do
    echo "🔨 Building $image..."
    if ! docker build -t "codingagenttwo-$image:scan" "./containers/$image/" > /dev/null 2>&1; then
        echo "❌ Failed to build $image"
        SCAN_FAILED=true
        continue
    fi
    echo "✅ Built $image successfully"
done

echo ""
echo "🔍 Scanning images for vulnerabilities..."

# Scan each image
for image in "${IMAGES[@]}"; do
    echo "📊 Scanning $image..."

    # Quick scan
    if docker scout quickview "codingagenttwo-$image:scan" 2>/dev/null; then
        echo "✅ Quick scan completed for $image"
    else
        echo "⚠️ Quick scan failed for $image"
    fi

    # CVE scan
    echo "🔎 Running CVE scan for $image..."
    if docker scout cves "codingagenttwo-$image:scan" --format sarif --output "./security-reports/$image-cves.sarif" 2>/dev/null; then
        echo "✅ CVE scan completed for $image"
    else
        echo "⚠️ CVE scan failed for $image"
        SCAN_FAILED=true
    fi

    echo ""
done

# Generate summary report
echo "📋 Generating security summary..."
mkdir -p ./security-reports

cat > ./security-reports/scan-summary.md << EOF
# Docker Security Scan Summary

Generated: $(date)

## Scanned Images

EOF

for image in "${IMAGES[@]}"; do
    echo "- $image" >> ./security-reports/scan-summary.md
done

cat >> ./security-reports/scan-summary.md << EOF

## Reports

- Individual SARIF reports: \`./security-reports/{image}-cves.sarif\`
- Summary: This file

## Recommendations

1. **Regular Scanning**: Run this script in CI/CD pipeline
2. **Base Image Updates**: Keep base images updated
3. **Minimal Dependencies**: Remove unnecessary packages
4. **Security Patches**: Apply security updates promptly

## Next Steps

- Review SARIF reports in your IDE or security dashboard
- Update base images if vulnerabilities found
- Consider using distroless images for production
EOF

if [ "$SCAN_FAILED" = true ]; then
    echo "❌ Some scans failed. Check individual reports for details."
    exit 1
else
    echo "✅ All security scans completed successfully!"
    echo "📄 Reports saved in ./security-reports/"
    exit 0
fi