# Project: AI Coding Agent
# Purpose: Services module exports with comprehensive package initialization

"""
Services Package for AI Orchestrator

This package contains all service classes for the AI Orchestrator,
including LLM services, validation, error recovery, and checkpoint management.
"""

import logging
from functools import lru_cache

from fastapi import Depends

logger = logging.getLogger(__name__)

# Import all service classes with error handling for optional dependencies
try:
    from .task_validator import TaskValidator
except ImportError as e:
    logger.error(f"Failed to import TaskValidator: {e}")
    TaskValidator = None

try:
    from .error_recovery import ErrorRecoverySystem
except ImportError as e:
    logger.error(f"Failed to import ErrorRecoverySystem: {e}")
    ErrorRecoverySystem = None

try:
    from .checkpoint_manager import CheckpointManager
except ImportError as e:
    logger.error(f"Failed to import CheckpointManager: {e}")
    CheckpointManager = None

try:
    from .enhanced_llm_service import EnhancedLLMService
except ImportError as e:
    logger.error(f"Failed to import EnhancedLLMService: {e}")
    EnhancedLLMService = None

# LockManager and Dispatcher DI factories
try:
    from .lock_manager import LockManager
    from .redis_service import get_redis_client  # async dependency
    from .dispatcher import Dispatcher
except Exception as e:
    logger.error(f"Dispatcher/LockManager dependencies unavailable: {e}")
    LockManager = None  # type: ignore
    Dispatcher = None  # type: ignore
    get_redis_client = None  # type: ignore


async def get_lock_manager(redis_client=Depends(get_redis_client)) -> "LockManager":  # type: ignore[name-defined]
    """FastAPI dependency to provide a LockManager instance.

    Uses the shared async Redis client for consistent connection handling.
    """
    return LockManager(redis_client)  # type: ignore[misc]


async def get_dispatcher(lock_manager: "LockManager" = Depends(get_lock_manager)) -> "Dispatcher":  # type: ignore[name-defined]
    """FastAPI dependency that constructs and returns a Dispatcher instance."""
    return Dispatcher(lock_manager)


# Define comprehensive exports for clean imports and proper IDE support
__all__ = [
    # Core service classes
    "TaskValidator",
    "ErrorRecoverySystem",
    "CheckpointManager",
    "EnhancedLLMService",
    # DI factories
    "get_lock_manager",
    "get_dispatcher",
]