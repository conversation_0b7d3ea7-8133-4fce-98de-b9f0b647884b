from fastapi import APIRouter, HTTPException, Depends, status, Request
from fastapi.responses import RedirectResponse
from pydantic import BaseModel
from typing import Optional
import logging
from ..utils.auth import get_supabase, create_access_token
from ..core.config import settings
from datetime import timedelta

logger = logging.getLogger(__name__)

# GitHub OAuth configuration from centralized settings

router = APIRouter(prefix="/auth/github", tags=["github-authentication"])

class GitHubAuthResponse(BaseModel):
    url: str

class GitHubAuthStatus(BaseModel):
    configured: bool
    client_id_set: bool
    redirect_uri: str

@router.get("/status", response_model=GitHubAuthStatus)
async def github_auth_status():
    """Check GitHub OAuth configuration status"""
    return GitHubAuthStatus(
        configured=settings.github_oauth_available,
        client_id_set=bool(settings.GITHUB_CLIENT_ID),
        redirect_uri=settings.GITHUB_REDIRECT_URI
    )

@router.get("/url", response_model=GitHubAuthResponse)
async def github_auth_url():
    """Get GitHub OAuth URL"""
    if not settings.GITHUB_CLIENT_ID:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="GitHub OAuth not configured. Please set GITHUB_CLIENT_ID environment variable."
        )

    # Get Supabase client
    supabase = get_supabase()
    if not supabase:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service not available"
        )

    try:
        # Generate GitHub OAuth URL via Supabase
        auth_response = supabase.auth.get_url_for_provider(
            "github",
            {
                "redirect_to": settings.GITHUB_REDIRECT_URI
            }
        )

        # Extract URL from response
        auth_url = auth_response.get('url') if isinstance(auth_response, dict) else str(auth_response)

        return {"url": auth_url}
    except Exception as e:
        logger.error(f"Failed to generate GitHub auth URL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate GitHub auth URL: {str(e)}"
        )

@router.get("/callback")
async def github_callback(code: str, request: Request):
    """Handle GitHub OAuth callback"""
    supabase = get_supabase()
    if not supabase:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service not available"
        )

    try:
        # Exchange code for session via Supabase
        session_response = supabase.auth.exchange_code_for_session({
            "auth_code": code
        })

        if not session_response or not hasattr(session_response, 'user') or not session_response.user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to authenticate with GitHub"
            )

        user = session_response.user

        # Create access token
        access_token_expires = timedelta(hours=24)  # Longer expiry for GitHub auth
        access_token = create_access_token(
            data={
                "sub": user.id,
                "email": user.email,
                "provider": "github",
                "github_username": user.user_metadata.get("user_name") if user.user_metadata else None
            },
            expires_delta=access_token_expires
        )

        # Redirect to code-server with token
        redirect_url = f"{settings.CODE_SERVER_URL}/?token={access_token}"
        return RedirectResponse(url=redirect_url)

    except Exception as e:
        logger.error(f"GitHub callback failed: {e}")
        # Redirect to code-server with error
        error_url = f"{settings.CODE_SERVER_URL}/?auth_error={str(e)}"
        return RedirectResponse(url=error_url)

@router.post("/logout")
async def github_logout():
    """Handle GitHub logout"""
    try:
        supabase = get_supabase()
        if supabase:
            supabase.auth.sign_out()

        return {"message": "Successfully logged out"}
    except Exception as e:
        logger.error(f"GitHub logout failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Logout failed: {str(e)}"
        )