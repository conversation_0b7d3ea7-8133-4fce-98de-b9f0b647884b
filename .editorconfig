# EditorConfig for AI Coding Agent Project
# https://editorconfig.org

root = true

[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

[*.{py,js,ts,jsx,tsx,css,scss,html,json,yaml,yml,md,sql}]
indent_style = space
indent_size = 4

[*.{py}]
trim_trailing_whitespace = true

[*.md]
trim_trailing_whitespace = false

[Makefile]
indent_style = tab

[*.{sql,html}]
indent_size = 2

# Python-specific settings
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88

# JavaScript/TypeScript settings
[*.{js,ts,jsx,tsx}]
indent_style = space
indent_size = 2

# Configuration files
[*.{json,yaml,yml}]
indent_style = space
indent_size = 2
