# Project: AI Coding Agent
# Purpose: State management package initialization - exports state serialization and rollback components

"""
State Management Package

This package provides comprehensive state management capabilities for the AI coding agent system,
including state serialization, checkpoint management, and rollback operations.

Components:
- StateSerializer: Advanced state serialization with multiple formats and compression
- RollbackEngine: Comprehensive rollback engine with safety checks and verification

Usage:
    from state import StateSerializer, RollbackEngine

    # Initialize serializer
    serializer = StateSerializer()

    # Initialize rollback engine
    rollback_engine = RollbackEngine()
"""

from .state_serializer import StateSerializer
from .rollback_engine import RollbackEngine

__all__ = [
    "StateSerializer",
    "RollbackEngine",
]

__version__ = "1.0.0"