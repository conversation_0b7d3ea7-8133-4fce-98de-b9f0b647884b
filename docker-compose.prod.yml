# Production Override Configuration
# For AI Coding Agent - Optimized production deployment
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

name: ai-coding-agent-prod

services:
  postgresql:
    restart: always
    environment:
      POSTGRES_DB: ai_coding_agent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
      # Production PostgreSQL optimizations
      POSTGRES_SHARED_PRELOAD_LIBRARIES: "pg_stat_statements,auto_explain"
      POSTGRES_MAX_CONNECTIONS: "200"
      POSTGRES_SHARED_BUFFERS: "256MB"
      POSTGRES_EFFECTIVE_CACHE_SIZE: "1GB"
      POSTGRES_MAINTENANCE_WORK_MEM: "64MB"
      POSTGRES_CHECKPOINT_COMPLETION_TARGET: "0.9"
      POSTGRES_WAL_BUFFERS: "16MB"
      POSTGRES_DEFAULT_STATISTICS_TARGET: "100"
    command: |
      postgres
        -c max_connections=200
        -c shared_buffers=256MB
        -c effective_cache_size=1GB
        -c maintenance_work_mem=64MB
        -c checkpoint_completion_target=0.9
        -c wal_buffers=16MB
        -c default_statistics_target=100
        -c random_page_cost=1.1
        -c effective_io_concurrency=200
        -c work_mem=4MB
        -c min_wal_size=1GB
        -c max_wal_size=4GB
        -c max_worker_processes=8
        -c max_parallel_workers_per_gather=4
        -c max_parallel_workers=8
        -c max_parallel_maintenance_workers=4
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ai_coding_agent"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  redis:
    restart: always
    command: >
      redis-server
        --maxmemory 256mb
        --maxmemory-policy allkeys-lru
        --tcp-keepalive 60
        --timeout 300
        --tcp-backlog 511
        --databases 16
        --save 900 1
        --save 300 10
        --save 60 10000
        --rdbcompression yes
        --rdbchecksum yes
        --stop-writes-on-bgsave-error yes
        --appendonly yes
        --appendfsync everysec
        --no-appendfsync-on-rewrite no
        --auto-aof-rewrite-percentage 100
        --auto-aof-rewrite-min-size 64mb
    healthcheck:
      test: ["CMD", "redis-cli", "--latency-history", "-i", "1", "-c", "1"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.1'

  ai-orchestrator:
    restart: always
    build:
      context: ./containers/ai-orchestrator
      dockerfile: Dockerfile
      args:
        BUILDKIT_INLINE_CACHE: 1
    environment:
      # Production database configuration
      DATABASE_URL: postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/ai_coding_agent
      REDIS_URL: redis://redis:6379/0

      # Production API configuration
      ENVIRONMENT: production
      DEBUG: "false"
      LOG_LEVEL: info

      # External service URLs
      OLLAMA_BASE_URL: ${OLLAMA_BASE_URL:-http://host.docker.internal:11434}

      # API Keys (from environment or secrets)
      OPENROUTER_API_KEY: ${OPENROUTER_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}

      # Authentication configuration
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_KEY: ${SUPABASE_KEY}
      SUPABASE_SERVICE_KEY: ${SUPABASE_SERVICE_KEY}
      JWT_SECRET: ${JWT_SECRET}
      JWT_ALGORITHM: HS256
      JWT_ACCESS_TOKEN_EXPIRE_MINUTES: 30

      # Security configuration
      ALLOWED_HOSTS: ${ALLOWED_HOSTS:-localhost,127.0.0.1,ai-orchestrator}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,https://admin.your-domain.com}
      TRUSTED_PROXIES: ${TRUSTED_PROXIES}

      # Performance configuration
      WORKERS: ${WORKERS:-4}
      MAX_CONNECTIONS: 100
      KEEPALIVE_TIMEOUT: 5

      # Monitoring configuration
      ENABLE_METRICS: "true"
      METRICS_PORT: 9090
      ENABLE_TRACING: ${ENABLE_TRACING:-false}
      JAEGER_AGENT_HOST: ${JAEGER_AGENT_HOST}

      # Rate limiting
      RATE_LIMIT_ENABLED: "true"
      RATE_LIMIT_REQUESTS_PER_MINUTE: 60
      RATE_LIMIT_BURST: 10

    ports:
      - "${API_PORT:-8000}:8000"
      - "${METRICS_PORT:-9090}:9090"

    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'

    # Production health check (more frequent)
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=3).raise_for_status()"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s

    # Production logging
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  admin-dashboard:
    restart: always
    build:
      context: ./containers/admin-dashboard
      dockerfile: Dockerfile
      args:
        BUILDKIT_INLINE_CACHE: 1
        NODE_ENV: production

    environment:
      # Production environment
      NODE_ENV: production
      NEXT_TELEMETRY_DISABLED: "1"

      # API configuration
      API_BASE_URL: http://ai-orchestrator:8000
      NEXT_PUBLIC_API_BASE_URL: ${NEXT_PUBLIC_API_BASE_URL:-https://api.your-domain.com}

      # Security configuration
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NEXTAUTH_URL: ${NEXTAUTH_URL:-https://admin.your-domain.com}

      # Performance configuration
      NEXT_CACHE: "force-cache"

      # Monitoring
      ENABLE_ANALYTICS: ${ENABLE_ANALYTICS:-false}
      ANALYTICS_ID: ${ANALYTICS_ID}

      # Error reporting
      SENTRY_DSN: ${SENTRY_DSN}
      SENTRY_ENVIRONMENT: production

    ports:
      - "${DASHBOARD_PORT:-3000}:3000"

    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.25'

    # Production health check
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/api/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s

    # Production logging
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Nginx reverse proxy for production
  nginx:
    image: nginx:1.25-alpine
    container_name: nginx-proxy
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./containers/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./containers/nginx/sites-available:/etc/nginx/sites-available:ro
      - ./containers/nginx/ssl:/etc/nginx/ssl:ro
      - ./containers/nginx/logs:/var/log/nginx
    depends_on:
      - ai-orchestrator
      - admin-dashboard
    networks:
      - ai-coding-agent-network
    environment:
      - NGINX_ENTRYPOINT_QUIET_LOGS=1
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Production monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: always
    ports:
      - "${PROMETHEUS_PORT:-9091}:9090"
    volumes:
      - ./containers/monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./containers/monitoring/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - ai-coding-agent-network
    depends_on:
      - ai-orchestrator
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Production monitoring with Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: always
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./containers/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./containers/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_ADMIN_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: "false"
      GF_SECURITY_SECRET_KEY: ${GRAFANA_SECRET_KEY}
      GF_DATABASE_TYPE: postgres
      GF_DATABASE_HOST: postgresql:5432
      GF_DATABASE_NAME: grafana
      GF_DATABASE_USER: postgres
      GF_DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
    networks:
      - ai-coding-agent-network
    depends_on:
      - prometheus
      - postgresql
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

# Production volumes with explicit configuration
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${POSTGRES_DATA_PATH:-./volumes/postgres-prod-data}

  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${REDIS_DATA_PATH:-./volumes/redis-prod-data}

  code_server_data:
    driver: local

  ai_orchestrator_data:
    driver: local

  prometheus_data:
    driver: local

  grafana_data:
    driver: local

# Production network configuration
networks:
  ai-coding-agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24