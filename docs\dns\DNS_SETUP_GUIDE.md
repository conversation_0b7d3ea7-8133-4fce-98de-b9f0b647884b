# DNS Setup Guide for AI Coding Agent Multi-Tenant Platform

## Overview

This guide explains how to set up local DNS resolution for the AI Coding Agent multi-tenant platform, including `.localhost` domains and proper hosts file configuration for development and testing.

## Architecture Overview

The platform uses <PERSON><PERSON><PERSON>k as a reverse proxy to route traffic to different services based on subdomain routing:

```
┌─────────────────────────────────────────┐
│           Traefik (Port 80/443)         │
├─────────────────────────────────────────┤
│ portal.localhost     → User Portal      │
│ api.localhost        → AI Orchestrator  │
│ traefik.localhost    → Traefik Dashboard│
│ user-{id}.localhost  → User Workspaces  │
└─────────────────────────────────────────┘
```

## DNS Resolution Methods

### Method 1: Browser .localhost Support (Recommended)

Modern browsers automatically resolve `.localhost` domains to `127.0.0.1` (localhost). This is the simplest method and works out of the box for:

- **Chrome/Chromium**: Full `.localhost` support
- **Firefox**: Full `.localhost` support
- **Safari**: Full `.localhost` support
- **Edge**: Full `.localhost` support

**No additional configuration required** for most development scenarios.

### Method 2: Hosts File Configuration (Fallback)

If you need explicit DNS resolution or are using tools that don't support automatic `.localhost` resolution, add entries to your hosts file.

#### Windows Hosts File

Location: `C:\Windows\System32\drivers\etc\hosts`

1. **Open Command Prompt as Administrator**
2. **Edit the hosts file:**
   ```cmd
   notepad C:\Windows\System32\drivers\etc\hosts
   ```

3. **Add the following entries:**
   ```
   # AI Coding Agent - Multi-tenant Platform
   127.0.0.1 portal.localhost
   127.0.0.1 api.localhost
   127.0.0.1 traefik.localhost

   # Example user workspaces (add as needed)
   127.0.0.1 user-john.localhost
   127.0.0.1 user-jane.localhost
   127.0.0.1 user-test.localhost
   ```

#### macOS/Linux Hosts File

Location: `/etc/hosts`

1. **Edit the hosts file:**
   ```bash
   sudo nano /etc/hosts
   ```

2. **Add the following entries:**
   ```
   # AI Coding Agent - Multi-tenant Platform
   127.0.0.1 portal.localhost
   127.0.0.1 api.localhost
   127.0.0.1 traefik.localhost

   # Example user workspaces (add as needed)
   127.0.0.1 user-john.localhost
   127.0.0.1 user-jane.localhost
   127.0.0.1 user-test.localhost
   ```

3. **Flush DNS cache:**

   **macOS:**
   ```bash
   sudo dscacheutil -flushcache
   sudo killall -HUP mDNSResponder
   ```

   **Linux:**
   ```bash
   sudo systemctl restart systemd-resolved  # For systemd-resolved
   # OR
   sudo service nscd restart  # For nscd
   ```

### Method 3: Local DNS Server (Advanced)

For development teams or complex setups, you can run a local DNS server.

#### Using dnsmasq (macOS/Linux)

1. **Install dnsmasq:**
   ```bash
   # macOS
   brew install dnsmasq

   # Ubuntu/Debian
   sudo apt-get install dnsmasq

   # CentOS/RHEL
   sudo yum install dnsmasq
   ```

2. **Configure dnsmasq:**
   ```bash
   echo 'address=/.localhost/127.0.0.1' | sudo tee -a /etc/dnsmasq.conf
   ```

3. **Start dnsmasq:**
   ```bash
   sudo systemctl start dnsmasq
   sudo systemctl enable dnsmasq
   ```

4. **Update system DNS:**
   ```bash
   # Add 127.0.0.1 as first DNS server in /etc/resolv.conf
   echo 'nameserver 127.0.0.1' | sudo tee /etc/resolv.conf.temp
   cat /etc/resolv.conf >> /etc/resolv.conf.temp
   sudo mv /etc/resolv.conf.temp /etc/resolv.conf
   ```

## Service URLs

Once DNS is configured, you can access the following services:

### Core Platform Services

| Service | URL | Description |
|---------|-----|-------------|
| **User Portal** | http://portal.localhost | Main user interface for workspace management |
| **API Orchestrator** | http://api.localhost | Backend API and documentation |
| **Traefik Dashboard** | http://traefik.localhost:8090 | Reverse proxy management |

### Dynamic User Workspaces

User workspaces are dynamically created with the pattern: `http://user-{userid}.localhost`

Examples:
- `http://user-john.localhost` - John's code-server workspace
- `http://user-jane.localhost` - Jane's code-server workspace
- `http://user-test123.localhost` - Test user workspace

## Testing DNS Resolution

### Command Line Testing

**Windows:**
```cmd
nslookup portal.localhost
ping portal.localhost
```

**macOS/Linux:**
```bash
nslookup portal.localhost
dig portal.localhost
ping portal.localhost
```

**Expected Output:**
```
portal.localhost has address 127.0.0.1
```

### Browser Testing

1. **Start the platform:**
   ```bash
   docker-compose up -d
   ```

2. **Test core services:**
   - http://portal.localhost - Should show user portal
   - http://api.localhost - Should show API documentation
   - http://traefik.localhost:8090 - Should show Traefik dashboard

3. **Check Traefik routing:**
   - Visit http://traefik.localhost:8090
   - Verify all services are registered and healthy

## Troubleshooting

### DNS Resolution Issues

1. **Clear browser DNS cache:**
   - **Chrome:** chrome://net-internals/#dns → Clear host cache
   - **Firefox:** about:networking#dns → Clear DNS Cache
   - **Safari:** Safari → Develop → Empty Caches

2. **Verify hosts file syntax:**
   - No trailing spaces
   - Use tabs or single spaces
   - Entries on separate lines

3. **Check file permissions:**
   ```bash
   # Windows (as Administrator)
   icacls C:\Windows\System32\drivers\etc\hosts

   # macOS/Linux
   ls -la /etc/hosts
   sudo chown root:root /etc/hosts
   sudo chmod 644 /etc/hosts
   ```

### Service Connection Issues

1. **Verify Docker services are running:**
   ```bash
   docker-compose ps
   ```

2. **Check Traefik logs:**
   ```bash
   docker-compose logs traefik
   ```

3. **Verify port binding:**
   ```bash
   netstat -tlnp | grep :80
   # OR
   ss -tlnp | grep :80
   ```

4. **Test direct container access:**
   ```bash
   curl http://localhost:3000  # user-portal
   curl http://localhost:8000  # ai-orchestrator
   ```

### Firewall Issues

**Windows:**
```cmd
# Allow Docker Desktop through Windows Firewall
netsh advfirewall firewall add rule name="Docker Desktop" dir=in action=allow protocol=TCP localport=80,443,8090
```

**macOS:**
```bash
# Check if firewall is blocking connections
sudo pfctl -sr | grep 80
```

**Linux:**
```bash
# UFW
sudo ufw allow 80
sudo ufw allow 443

# iptables
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

## Production Considerations

### SSL/TLS Configuration

For production deployments, configure SSL certificates:

1. **Let's Encrypt with Traefik:**
   ```yaml
   # docker-compose.prod.yml
   command:
     - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
     - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
     - "--certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web"
   ```

2. **Custom domain setup:**
   ```yaml
   labels:
     - "traefik.http.routers.user-portal.rule=Host(`portal.yourdomain.com`)"
     - "traefik.http.routers.user-portal.tls.certresolver=letsencrypt"
   ```

### DNS Server Configuration

For production, use proper DNS records:

```
portal.yourdomain.com.     A    YOUR_SERVER_IP
api.yourdomain.com.        A    YOUR_SERVER_IP
*.users.yourdomain.com.    A    YOUR_SERVER_IP
```

## Automation Scripts

### Windows PowerShell Script

```powershell
# setup-dns.ps1
Write-Host "Setting up AI Coding Agent DNS..."

$hostsFile = "$env:SystemRoot\System32\drivers\etc\hosts"
$entries = @(
    "127.0.0.1 portal.localhost",
    "127.0.0.1 api.localhost",
    "127.0.0.1 traefik.localhost"
)

foreach ($entry in $entries) {
    if (!(Get-Content $hostsFile | Select-String $entry)) {
        Add-Content $hostsFile "`n$entry"
        Write-Host "Added: $entry"
    }
}

Write-Host "DNS setup completed!"
```

### Bash Script (macOS/Linux)

```bash
#!/bin/bash
# setup-dns.sh

echo "Setting up AI Coding Agent DNS..."

HOSTS_FILE="/etc/hosts"
ENTRIES=(
    "127.0.0.1 portal.localhost"
    "127.0.0.1 api.localhost"
    "127.0.0.1 traefik.localhost"
)

for entry in "${ENTRIES[@]}"; do
    if ! grep -q "$entry" "$HOSTS_FILE"; then
        echo "$entry" | sudo tee -a "$HOSTS_FILE" > /dev/null
        echo "Added: $entry"
    fi
done

# Flush DNS cache
if [[ "$OSTYPE" == "darwin"* ]]; then
    sudo dscacheutil -flushcache
    sudo killall -HUP mDNSResponder
elif command -v systemctl &> /dev/null; then
    sudo systemctl restart systemd-resolved
fi

echo "DNS setup completed!"
```

## Summary

The AI Coding Agent platform uses `.localhost` domains for local development, which work automatically in modern browsers. For additional reliability or legacy tool support, you can:

1. **Use browser automatic resolution** (recommended)
2. **Add hosts file entries** (fallback)
3. **Set up local DNS server** (advanced)

The platform provides these key endpoints:
- **http://portal.localhost** - User portal
- **http://api.localhost** - API backend
- **http://traefik.localhost:8090** - Traefik dashboard
- **http://user-{id}.localhost** - User workspaces

For production deployments, configure proper DNS records and SSL certificates using Traefik's automatic certificate management features.