# Grafana datasource configuration for AI Coding Agent
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true

  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://elasticsearch:9200
    database: "[logs-]YYYY.MM.DD"
    jsonData:
      esVersion: 8.11.0
      timeField: "@timestamp"
      interval: "Daily"
      logMessageField: "message"
      logLevelField: "level"
    editable: true
