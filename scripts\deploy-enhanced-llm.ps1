# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: PowerShell script to deploy the enhanced LLM service

param(
    [string]$Environment = "dev",
    [switch]$Force,
    [switch]$SkipTests
)

Write-Host "🚀 AI Coding Agent - Enhanced LLM Service Deployment" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green

# Set error handling
$ErrorActionPreference = "Stop"

# Navigate to project root
$ProjectRoot = Split-Path -Path $PSScriptRoot -Parent
Set-Location $ProjectRoot

Write-Host "📁 Project root: $ProjectRoot" -ForegroundColor Cyan

# Check Docker is running
Write-Host "🐳 Checking Docker status..." -ForegroundColor Yellow
try {
    $dockerInfo = docker info 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running"
    }
    Write-Host "✅ Docker is running" -ForegroundColor Green
}
catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Stop existing containers if force flag is used
if ($Force) {
    Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
    Write-Host "✅ Containers stopped" -ForegroundColor Green
}

# Build and start containers
Write-Host "🔨 Building and starting enhanced LLM service..." -ForegroundColor Yellow
try {
    if ($Environment -eq "dev") {
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d --build
    } else {
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
    }
    Write-Host "✅ Containers built and started" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to build/start containers" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
}

# Wait for service to be ready
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
$maxAttempts = 30
$attempt = 0

do {
    $attempt++
    Start-Sleep -Seconds 2

    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8000/health" -Method GET -TimeoutSec 5
        if ($response.status -eq "ok") {
            Write-Host "✅ AI Orchestrator is ready" -ForegroundColor Green
            break
        }
    }
    catch {
        # Service not ready yet
        if ($attempt -eq $maxAttempts) {
            Write-Host "❌ Service failed to start within $($maxAttempts * 2) seconds" -ForegroundColor Red
            Write-Host "Check logs with: docker-compose logs ai-orchestrator" -ForegroundColor Yellow
            exit 1
        }
    }

    Write-Host "   Attempt $attempt/$maxAttempts - waiting..." -ForegroundColor Gray
} while ($attempt -lt $maxAttempts)

# Check container health
Write-Host "🏥 Checking container health..." -ForegroundColor Yellow
$containers = docker-compose ps --format json | ConvertFrom-Json

foreach ($container in $containers) {
    $name = $container.Service
    $status = $container.State
    $health = $container.Health

    if ($status -eq "running") {
        if ($health -eq "healthy" -or $health -eq "") {
            Write-Host "✅ $name is running and healthy" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $name is running but health check: $health" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ $name is not running (status: $status)" -ForegroundColor Red
    }
}

# Run tests if not skipped
if (-not $SkipTests) {
    Write-Host ""
    Write-Host "🧪 Running enhanced LLM service tests..." -ForegroundColor Yellow
    try {
        python "scripts/test-enhanced-llm-service.py"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Tests passed" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Some tests failed, but service is running" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "⚠️  Could not run tests, but service is running" -ForegroundColor Yellow
        Write-Host "   You can run tests manually with: python scripts/test-enhanced-llm-service.py" -ForegroundColor Gray
    }
}

# Display service information
Write-Host ""
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green
Write-Host "Service URLs:" -ForegroundColor Cyan
Write-Host "  🌐 Main Service: http://localhost:8000" -ForegroundColor White
Write-Host "  💓 Health Check: http://localhost:8000/health" -ForegroundColor White
Write-Host "  🤖 LLM Health: http://localhost:8000/api/llm/health" -ForegroundColor White
Write-Host "  📊 System Info: http://localhost:8000/api/system/info" -ForegroundColor White
Write-Host "  🔧 Code Server: http://localhost:8080" -ForegroundColor White
Write-Host "  📱 Admin Dashboard: http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "Useful Commands:" -ForegroundColor Cyan
Write-Host "  📋 View logs: docker-compose logs -f ai-orchestrator" -ForegroundColor White
Write-Host "  🛑 Stop services: docker-compose down" -ForegroundColor White
Write-Host "  🧪 Run tests: python scripts/test-enhanced-llm-service.py" -ForegroundColor White
Write-Host "  🔄 Restart: ./scripts/deploy-enhanced-llm.ps1 -Force" -ForegroundColor White
Write-Host ""

# Environment-specific notes
if ($Environment -eq "dev") {
    Write-Host "Development Mode Active:" -ForegroundColor Yellow
    Write-Host "  🔄 Auto-reload enabled" -ForegroundColor White
    Write-Host "  🐛 Debug mode enabled" -ForegroundColor White
    Write-Host "  📁 Volume mounts active" -ForegroundColor White
} else {
    Write-Host "Production Mode Active:" -ForegroundColor Yellow
    Write-Host "  🚀 Optimized builds" -ForegroundColor White
    Write-Host "  🔒 Security enabled" -ForegroundColor White
    Write-Host "  📊 Monitoring enabled" -ForegroundColor White
}

Write-Host ""
Write-Host "🎯 Your enhanced LLM service is now ready!" -ForegroundColor Green
Write-Host "Configure your API keys in .env file for full functionality." -ForegroundColor Cyan