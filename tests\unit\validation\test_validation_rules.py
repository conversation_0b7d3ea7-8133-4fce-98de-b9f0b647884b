# Project: AI Coding Agent - Unit Tests
# Purpose: Comprehensive unit tests for validation rules engine

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, mock_open
from typing import List, Dict, Any

# Import the classes to test
from containers.ai_orchestrator.src.validation.validation_rules import ValidationRuleEngine
from containers.ai_orchestrator.src.models.validation_models import (
    Task, TaskType, AgentType, ExecutionStatus, ValidationResult
)


class TestValidationRuleEngine:
    """Comprehensive test suite for ValidationRuleEngine"""

    @pytest.fixture
    def engine(self):
        """Create a ValidationRuleEngine instance for testing"""
        return ValidationRuleEngine(project_root="/test/workspace")

    @pytest.fixture
    def sample_task(self):
        """Create a sample task for testing"""
        return Task(
            title="Test Task",
            description="A test task",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND,
            expected_files=["src/component.tsx"],
            code_files=["src/component.tsx"],
            test_command="npm test",
            integration_checks=["http://localhost:3000/health"]
        )

    @pytest.fixture
    def sample_step(self):
        """Create a sample step for testing"""
        from containers.ai_orchestrator.src.models.validation_models import Step

        task = Task(
            title="Test Task",
            description="A test task",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND
        )

        return Step(
            title="Test Step",
            description="A test step",
            tasks=[task]
        )

    @pytest.fixture
    def sample_phase(self):
        """Create a sample phase for testing"""
        from containers.ai_orchestrator.src.models.validation_models import Phase, Step

        task = Task(
            title="Test Task",
            description="A test task",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND
        )

        step = Step(
            title="Test Step",
            description="A test step",
            tasks=[task]
        )

        return Phase(
            title="Test Phase",
            description="A test phase",
            steps=[step]
        )

    # Basic validation rule tests

    @pytest.mark.asyncio
    async def test_validate_task_success(self, engine, sample_task):
        """Test successful task validation"""
        # Mock all validation methods to return success
        with patch.object(engine, '_validate_file_existence', return_value=ValidationResult.success()):
            with patch.object(engine, '_validate_code_syntax', return_value=ValidationResult.success()):
                with patch.object(engine, '_validate_functional_tests', return_value=ValidationResult.success()):
                    with patch.object(engine, '_validate_integration_checks', return_value=ValidationResult.success()):

                        result = await engine.validate_task(sample_task)

                        assert result.is_valid
                        assert "Task validation passed" in result.details
                        assert result.error is None

    @pytest.mark.asyncio
    async def test_validate_task_failure(self, engine, sample_task):
        """Test task validation failure"""
        # Mock file existence check to fail
        with patch.object(engine, '_validate_file_existence', return_value=ValidationResult.failure("Files not found")):
            with patch.object(engine, '_validate_code_syntax', return_value=ValidationResult.success()):
                with patch.object(engine, '_validate_functional_tests', return_value=ValidationResult.success()):
                    with patch.object(engine, '_validate_integration_checks', return_value=ValidationResult.success()):

                        result = await engine.validate_task(sample_task)

                        assert not result.is_valid
                        assert "Files not found" in result.error

    @pytest.mark.asyncio
    async def test_validate_task_warnings(self, engine, sample_task):
        """Test task validation with warnings"""
        warning_result = ValidationResult(
            is_valid=True,
            details="Validation passed",
            warnings=["Some non-critical issues found"]
        )

        # Mock one validation to return warnings
        with patch.object(engine, '_validate_file_existence', return_value=ValidationResult.success()):
            with patch.object(engine, '_validate_code_syntax', return_value=warning_result):
                with patch.object(engine, '_validate_functional_tests', return_value=ValidationResult.success()):
                    with patch.object(engine, '_validate_integration_checks', return_value=ValidationResult.success()):

                        result = await engine.validate_task(sample_task)

                        assert result.is_valid
                        assert len(result.warnings) > 0
                        assert "Some non-critical issues found" in result.warnings

    # File existence validation tests

    @pytest.mark.asyncio
    async def test_validate_file_existence_success(self, engine):
        """Test successful file existence validation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files
            test_file = Path(temp_dir) / "test.txt"
            test_file.write_text("test content")

            engine.project_root = Path(temp_dir)

            result = await engine._validate_file_existence(["test.txt"])

            assert result.is_valid
            assert "files exist" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_file_existence_missing_files(self, engine):
        """Test file existence validation with missing files"""
        with tempfile.TemporaryDirectory() as temp_dir:
            engine.project_root = Path(temp_dir)

            result = await engine._validate_file_existence(["nonexistent.txt"])

            assert not result.is_valid
            assert "does not exist" in result.error.lower()

    @pytest.mark.asyncio
    async def test_validate_file_existence_mixed_files(self, engine):
        """Test file existence validation with mix of existing and missing files"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create one file but not the other
            existing_file = Path(temp_dir) / "exists.txt"
            existing_file.write_text("content")

            engine.project_root = Path(temp_dir)

            result = await engine._validate_file_existence(["exists.txt", "missing.txt"])

            assert not result.is_valid
            assert "missing.txt" in result.error

    # Code syntax validation tests

    @pytest.mark.asyncio
    async def test_validate_code_syntax_python_success(self, engine):
        """Test successful Python code syntax validation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create valid Python file
            py_file = Path(temp_dir) / "valid.py"
            py_file.write_text("def hello():\n    return 'world'\n")

            engine.project_root = Path(temp_dir)

            result = await engine._validate_code_syntax(["valid.py"])

            assert result.is_valid
            assert "syntax validation passed" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_code_syntax_python_failure(self, engine):
        """Test Python code syntax validation failure"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create invalid Python file
            py_file = Path(temp_dir) / "invalid.py"
            py_file.write_text("def hello(\n    return 'world'\n")  # Missing closing parenthesis

            engine.project_root = Path(temp_dir)

            result = await engine._validate_code_syntax(["invalid.py"])

            assert not result.is_valid
            assert "syntax error" in result.error.lower()

    @pytest.mark.asyncio
    async def test_validate_code_syntax_javascript_success(self, engine):
        """Test JavaScript syntax validation success (mocked)"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create valid JavaScript file
            js_file = Path(temp_dir) / "valid.js"
            js_file.write_text("function hello() { return 'world'; }")

            engine.project_root = Path(temp_dir)

            # Mock subprocess to simulate successful Node.js validation
            with patch('subprocess.run') as mock_run:
                mock_run.return_value.returncode = 0

                result = await engine._validate_code_syntax(["valid.js"])

                assert result.is_valid

    @pytest.mark.asyncio
    async def test_validate_code_syntax_javascript_failure(self, engine):
        """Test JavaScript syntax validation failure (mocked)"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create invalid JavaScript file
            js_file = Path(temp_dir) / "invalid.js"
            js_file.write_text("function hello() { return 'world' ")  # Missing closing brace

            engine.project_root = Path(temp_dir)

            # Mock subprocess to simulate failed Node.js validation
            with patch('subprocess.run') as mock_run:
                mock_run.return_value.returncode = 1
                mock_run.return_value.stderr = "SyntaxError: Unexpected end of input"

                result = await engine._validate_code_syntax(["invalid.js"])

                assert not result.is_valid
                assert "syntax error" in result.error.lower()

    # Functional testing validation tests

    @pytest.mark.asyncio
    async def test_validate_functional_tests_success(self, engine):
        """Test successful functional test validation"""
        # Mock successful subprocess run
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "All tests passed"

            result = await engine._validate_functional_tests("npm test")

            assert result.is_valid
            assert "tests passed" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_functional_tests_failure(self, engine):
        """Test functional test validation failure"""
        # Mock failed subprocess run
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 1
            mock_run.return_value.stderr = "Tests failed: 2 failing"

            result = await engine._validate_functional_tests("npm test")

            assert not result.is_valid
            assert "tests failed" in result.error.lower()

    @pytest.mark.asyncio
    async def test_validate_functional_tests_timeout(self, engine):
        """Test functional test validation timeout"""
        # Mock subprocess timeout
        with patch('subprocess.run') as mock_run:
            mock_run.side_effect = asyncio.TimeoutError("Command timed out")

            result = await engine._validate_functional_tests("npm test")

            assert not result.is_valid
            assert "timeout" in result.error.lower()

    # Integration checks validation tests

    @pytest.mark.asyncio
    async def test_validate_integration_checks_success(self, engine):
        """Test successful integration checks validation"""
        # Mock successful HTTP check
        with patch.object(engine, '_validate_http_endpoint', return_value=ValidationResult.success("HTTP endpoint accessible")):

            result = await engine._validate_integration_checks(["http://localhost:3000/health"])

            assert result.is_valid

    @pytest.mark.asyncio
    async def test_validate_integration_checks_failure(self, engine):
        """Test integration checks validation failure"""
        # Mock failed HTTP check
        with patch.object(engine, '_validate_http_endpoint', return_value=ValidationResult.failure("HTTP endpoint not accessible")):

            result = await engine._validate_integration_checks(["http://localhost:3000/health"])

            assert not result.is_valid

    @pytest.mark.asyncio
    async def test_validate_integration_checks_mixed(self, engine):
        """Test integration checks with mixed results"""
        checks = ["http://localhost:3000/health", "db:connection_string"]

        # Mock different results for different checks
        def mock_http_check(url):
            if "3000" in url:
                return ValidationResult.success("HTTP OK")
            return ValidationResult.failure("HTTP failed")

        def mock_db_check(conn_str):
            return ValidationResult.success("DB OK")

        with patch.object(engine, '_validate_http_endpoint', side_effect=mock_http_check):
            with patch.object(engine, '_validate_database_connection', side_effect=mock_db_check):

                result = await engine._validate_integration_checks(checks)

                assert result.is_valid  # Both should pass in this case

    # HTTP endpoint validation tests

    @pytest.mark.asyncio
    async def test_validate_http_endpoint_success(self, engine):
        """Test successful HTTP endpoint validation"""
        # Mock aiohttp session
        mock_response = Mock()
        mock_response.status = 200

        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response

        with patch('aiohttp.ClientSession', return_value=mock_session):
            result = await engine._validate_http_endpoint("http://example.com/health")

            assert result.is_valid
            assert "accessible" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_http_endpoint_failure(self, engine):
        """Test HTTP endpoint validation failure"""
        # Mock aiohttp session with 500 error
        mock_response = Mock()
        mock_response.status = 500

        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response

        with patch('aiohttp.ClientSession', return_value=mock_session):
            result = await engine._validate_http_endpoint("http://example.com/health")

            assert not result.is_valid
            assert "500" in result.error

    @pytest.mark.asyncio
    async def test_validate_http_endpoint_connection_error(self, engine):
        """Test HTTP endpoint validation with connection error"""
        # Mock aiohttp session to raise connection error
        mock_session = AsyncMock()
        mock_session.get.side_effect = Exception("Connection refused")

        with patch('aiohttp.ClientSession', return_value=mock_session):
            result = await engine._validate_http_endpoint("http://example.com/health")

            assert not result.is_valid
            assert "connection" in result.error.lower()

    # Step validation tests

    @pytest.mark.asyncio
    async def test_validate_step_success(self, engine, sample_step):
        """Test successful step validation"""
        # Mock task validation to succeed
        with patch.object(engine, 'validate_task', return_value=ValidationResult.success()):
            result = await engine.validate_step(sample_step)

            assert result.is_valid
            assert "step validation passed" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_step_failure(self, engine, sample_step):
        """Test step validation failure"""
        # Mock task validation to fail
        with patch.object(engine, 'validate_task', return_value=ValidationResult.failure("Task failed")):
            result = await engine.validate_step(sample_step)

            assert not result.is_valid
            assert "task failed" in result.error.lower()

    # Phase validation tests

    @pytest.mark.asyncio
    async def test_validate_phase_success(self, engine, sample_phase):
        """Test successful phase validation"""
        # Mock step validation to succeed
        with patch.object(engine, 'validate_step', return_value=ValidationResult.success()):
            result = await engine.validate_phase(sample_phase)

            assert result.is_valid
            assert "phase validation passed" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_phase_failure(self, engine, sample_phase):
        """Test phase validation failure"""
        # Mock step validation to fail
        with patch.object(engine, 'validate_step', return_value=ValidationResult.failure("Step failed")):
            result = await engine.validate_phase(sample_phase)

            assert not result.is_valid
            assert "step failed" in result.error.lower()

    # Rule management tests

    def test_add_custom_rule(self, engine):
        """Test adding custom validation rule"""
        def custom_rule(task):
            return ValidationResult.success("Custom rule passed")

        engine.add_custom_rule(TaskType.CREATE_COMPONENT, custom_rule)

        assert TaskType.CREATE_COMPONENT in engine.custom_rules
        assert custom_rule in engine.custom_rules[TaskType.CREATE_COMPONENT]

    @pytest.mark.asyncio
    async def test_apply_custom_rules(self, engine, sample_task):
        """Test applying custom validation rules"""
        def custom_rule(task):
            return ValidationResult.success("Custom rule applied")

        engine.add_custom_rule(TaskType.CREATE_COMPONENT, custom_rule)

        result = await engine._apply_custom_rules(sample_task)

        assert result.is_valid
        assert "custom rule applied" in result.details.lower()

    # Error handling tests

    @pytest.mark.asyncio
    async def test_validate_task_exception_handling(self, engine, sample_task):
        """Test task validation exception handling"""
        # Mock a method to raise an exception
        with patch.object(engine, '_validate_file_existence', side_effect=Exception("Unexpected error")):
            result = await engine.validate_task(sample_task)

            assert not result.is_valid
            assert "validation system error" in result.error.lower()

    @pytest.mark.asyncio
    async def test_validate_step_exception_handling(self, engine, sample_step):
        """Test step validation exception handling"""
        # Mock task validation to raise an exception
        with patch.object(engine, 'validate_task', side_effect=Exception("Unexpected error")):
            result = await engine.validate_step(sample_step)

            assert not result.is_valid
            assert "validation system error" in result.error.lower()

    # Configuration tests

    def test_engine_configuration(self, engine):
        """Test validation engine configuration"""
        assert engine.project_root == Path("/test/workspace")
        assert engine.strict_mode is False  # Default value
        assert isinstance(engine.validation_cache, dict)

    def test_engine_strict_mode(self):
        """Test validation engine in strict mode"""
        engine = ValidationRuleEngine(project_root="/test", strict_mode=True)

        assert engine.strict_mode is True

    # Cache tests

    @pytest.mark.asyncio
    async def test_validation_cache(self, engine, sample_task):
        """Test validation result caching"""
        # First validation
        with patch.object(engine, '_validate_file_existence', return_value=ValidationResult.success()) as mock_validate:
            result1 = await engine.validate_task(sample_task)

            # Second validation should use cache (mock should only be called once)
            result2 = await engine.validate_task(sample_task)

            assert result1.is_valid
            assert result2.is_valid
            # In a real implementation with caching, mock would be called only once

    # Performance tests

    @pytest.mark.asyncio
    async def test_validation_performance_metrics(self, engine, sample_task):
        """Test that validation includes performance metrics"""
        with patch.object(engine, '_validate_file_existence', return_value=ValidationResult.success()):
            with patch.object(engine, '_validate_code_syntax', return_value=ValidationResult.success()):
                with patch.object(engine, '_validate_functional_tests', return_value=ValidationResult.success()):
                    with patch.object(engine, '_validate_integration_checks', return_value=ValidationResult.success()):

                        result = await engine.validate_task(sample_task)

                        assert result.is_valid
                        assert 'validation_time_seconds' in result.metrics
                        assert 'checks_performed' in result.metrics
                        assert result.metrics['validation_time_seconds'] >= 0

    # Edge cases

    @pytest.mark.asyncio
    async def test_validate_task_empty_requirements(self, engine):
        """Test validating task with no requirements"""
        minimal_task = Task(
            title="Minimal Task",
            description="Task with no validation requirements",
            type=TaskType.CONFIGURATION,
            agent_type=AgentType.ARCHITECT
        )

        result = await engine.validate_task(minimal_task)

        assert result.is_valid
        assert "no validation requirements" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_empty_step(self, engine):
        """Test validating step with no tasks"""
        from containers.ai_orchestrator.src.models.validation_models import Step

        empty_step = Step(
            title="Empty Step",
            description="Step with no tasks",
            tasks=[]
        )

        result = await engine.validate_step(empty_step)

        assert result.is_valid
        assert "no tasks to validate" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_empty_phase(self, engine):
        """Test validating phase with no steps"""
        from containers.ai_orchestrator.src.models.validation_models import Phase

        empty_phase = Phase(
            title="Empty Phase",
            description="Phase with no steps",
            steps=[]
        )

        result = await engine.validate_phase(empty_phase)

        assert result.is_valid
        assert "no steps to validate" in result.details.lower()