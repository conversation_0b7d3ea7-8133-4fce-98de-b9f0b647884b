This document outlines the essential development practices for the AI Coding Agent, covering the entire software lifecycle from Python code to deployment and monitoring.
1. Python Development
Style: Strictly follow PEP 8 and Black (88-char line length). Use standard naming: snake_case for functions/variables, PascalCase for classes.
Structure: Write small, single-responsibility functions. Prefer composition over inheritance.
Typing & Docs: Mandate type hints and Google-style docstrings for all public code.
Error Handling: Use a custom exception hierarchy, log specific errors with context, and never ignore exceptions.
Security: Validate all inputs at system boundaries using Pydantic. Manage secrets via environment variables, never hard-coding them.
Testing: Use pytest with the AAA pattern, mock external services, and aim for 90%+ code coverage. Tests must be fast, independent, and cover both happy paths and edge cases.
Performance: Profile before optimizing. Use efficient data structures and consider async/await for I/O-bound tasks.
2. Containerization (Docker)
Dockerfiles: Use minimal, pinned, and secure base images (e.g., python-slim, distroless). Run containers as a non-root user.
Optimization: Structure layers logically (least to most frequent changes) and use multi-stage builds.
Configuration: Manage all configuration through environment variables.
Data: Use named volumes for all persistent data.
3. AI Agent & LLM Integration
Architecture: Design for clear state management, error handling, and communication between agents, potentially using message queues.
LLM Interaction: Abstract LLM services to support both local (Ollama) and cloud models. Use structured prompt templates, manage token limits, and log all requests/responses.
Cost Control: Implement rate limiting, exponential backoff, and cost monitoring for all API calls.
4. API Development (FastAPI)
Design: Use routers for organization, Pydantic for validation, and dependency injection.
Best Practices: Auto-generate OpenAPI documentation, use async endpoints for I/O, and implement security (auth, CORS, rate limiting).
Error Handling: Return consistent, meaningful error responses with proper HTTP status codes.
5. Database (SQLAlchemy)
Schema: Use proper indexing, relationships, and constraints for data integrity.
Operations: Use Alembic for migrations, optimize queries to avoid N+1 problems, and manage connections via a pool.
6. Operations & Process
Monitoring: Implement structured JSON logging with correlation IDs, track key metrics, and expose a /health endpoint.
Code Review: Use a checklist (tests pass, docs updated). Reviews should be specific, actionable, and respectful.
Automation (CI/CD): Use pre-commit hooks for formatting/linting. The CI/CD pipeline must run tests, security scans, and build/deploy images automatically.
Version Control (Git): Use Conventional Commits (feat:, fix:) and a feature-branch workflow with Pull Requests for code review. Protect the main branch.
Tooling: Standardize on Black/isort for formatting, flake8/mypy/bandit for linting, and pytest for testing.

