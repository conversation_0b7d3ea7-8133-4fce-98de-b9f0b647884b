"""
Health Check Router for AI Orchestrator Service.

This module provides comprehensive health check endpoints for monitoring
the ai-orchestrator service and its dependencies.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import logging
import os
import time
from typing import Dict, Any, List
from datetime import datetime, timezone

import redis
import docker
from fastapi import APIRouter, HTTPException, status
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from ..models.database import get_db
from ..services.docker_service import get_docker_service
from ..services.redis_service import get_redis_client
from ..models.workspace import Workspace, WorkspaceStatus
from pydantic import BaseModel

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/health", tags=["health"])


class HealthStatus(BaseModel):
    """Health status model."""
    status: str  # "healthy", "degraded", "unhealthy"
    timestamp: datetime
    details: Dict[str, Any]


class ComponentHealth(BaseModel):
    """Individual component health model."""
    name: str
    status: str
    response_time_ms: float
    details: Dict[str, Any]


class SystemHealth(BaseModel):
    """System-wide health model."""
    status: str
    timestamp: datetime
    uptime_seconds: float
    components: List[ComponentHealth]
    summary: Dict[str, Any]


# Service startup time for uptime calculation
SERVICE_START_TIME = time.time()


async def check_database_health() -> ComponentHealth:
    """
    Check PostgreSQL database health.

    Returns:
        ComponentHealth for database
    """
    start_time = time.time()
    try:
        # Test database connection and query
        db = next(get_db())

        # Simple query to test connection
        result = db.execute(text("SELECT 1 as test"))
        test_value = result.scalar()

        # Test workspace table access
        workspace_count = db.query(Workspace).count()

        response_time = (time.time() - start_time) * 1000

        return ComponentHealth(
            name="database",
            status="healthy",
            response_time_ms=response_time,
            details={
                "connection": "active",
                "test_query": "passed" if test_value == 1 else "failed",
                "workspace_count": workspace_count,
                "database_url": os.getenv("DATABASE_URL", "").split("@")[-1] if "@" in os.getenv("DATABASE_URL", "") else "unknown"
            }
        )

    except SQLAlchemyError as e:
        response_time = (time.time() - start_time) * 1000
        return ComponentHealth(
            name="database",
            status="unhealthy",
            response_time_ms=response_time,
            details={
                "error": str(e),
                "type": "database_error"
            }
        )
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        return ComponentHealth(
            name="database",
            status="unhealthy",
            response_time_ms=response_time,
            details={
                "error": str(e),
                "type": "connection_error"
            }
        )


async def check_redis_health() -> ComponentHealth:
    """
    Check Redis cache health.

    Returns:
        ComponentHealth for Redis
    """
    start_time = time.time()
    try:
        redis_client = await get_redis_client()

        # Test Redis connection with ping
        await redis_client.ping()

        # Test basic operations
        test_key = "health_check_test"
        await redis_client.set(test_key, "test_value", ex=60)
        test_value = await redis_client.get(test_key)
        await redis_client.delete(test_key)

        # Get Redis info
        info = await redis_client.info()

        response_time = (time.time() - start_time) * 1000

        return ComponentHealth(
            name="redis",
            status="healthy",
            response_time_ms=response_time,
            details={
                "connection": "active",
                "ping": "successful",
                "test_operation": "passed" if test_value == b"test_value" else "failed",
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "unknown"),
                "redis_version": info.get("redis_version", "unknown")
            }
        )

    except redis.RedisError as e:
        response_time = (time.time() - start_time) * 1000
        return ComponentHealth(
            name="redis",
            status="unhealthy",
            response_time_ms=response_time,
            details={
                "error": str(e),
                "type": "redis_error"
            }
        )
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        return ComponentHealth(
            name="redis",
            status="unhealthy",
            response_time_ms=response_time,
            details={
                "error": str(e),
                "type": "connection_error"
            }
        )


async def check_docker_health() -> ComponentHealth:
    """
    Check Docker service health.

    Returns:
        ComponentHealth for Docker
    """
    start_time = time.time()
    try:
        docker_service = get_docker_service()
        health_result = await docker_service.health_check()

        response_time = (time.time() - start_time) * 1000

        if health_result.get("status") == "healthy":
            return ComponentHealth(
                name="docker",
                status="healthy",
                response_time_ms=response_time,
                details=health_result
            )
        else:
            return ComponentHealth(
                name="docker",
                status="unhealthy",
                response_time_ms=response_time,
                details=health_result
            )

    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        return ComponentHealth(
            name="docker",
            status="unhealthy",
            response_time_ms=response_time,
            details={
                "error": str(e),
                "type": "docker_error"
            }
        )


async def check_workspace_service_health() -> ComponentHealth:
    """
    Check workspace management service health.

    Returns:
        ComponentHealth for workspace service
    """
    start_time = time.time()
    try:
        db = next(get_db())

        # Count workspaces by status
        status_counts = {}
        for status in WorkspaceStatus:
            count = db.query(Workspace).filter(Workspace.status == status).count()
            status_counts[status.value] = count

        # Get recent workspace activity
        recent_workspaces = db.query(Workspace).order_by(
            Workspace.updated_at.desc()
        ).limit(5).all()

        response_time = (time.time() - start_time) * 1000

        return ComponentHealth(
            name="workspace_service",
            status="healthy",
            response_time_ms=response_time,
            details={
                "workspace_counts": status_counts,
                "total_workspaces": sum(status_counts.values()),
                "recent_activity": len(recent_workspaces),
                "max_user_containers": int(os.getenv("MAX_USER_CONTAINERS", "10"))
            }
        )

    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        return ComponentHealth(
            name="workspace_service",
            status="unhealthy",
            response_time_ms=response_time,
            details={
                "error": str(e),
                "type": "workspace_service_error"
            }
        )


async def check_external_services_health() -> ComponentHealth:
    """
    Check external service connectivity (LLM providers).

    Returns:
        ComponentHealth for external services
    """
    start_time = time.time()

    external_services = {
        "ollama": os.getenv("OLLAMA_BASE_URL", "http://host.docker.internal:11434"),
        "openrouter": "https://openrouter.ai" if os.getenv("OPENROUTER_API_KEY") else None,
        "openai": "https://api.openai.com" if os.getenv("OPENAI_API_KEY") else None,
        "anthropic": "https://api.anthropic.com" if os.getenv("ANTHROPIC_API_KEY") else None
    }

    service_status = {}
    overall_status = "healthy"

    for service_name, service_url in external_services.items():
        if service_url:
            try:
                # Simple connectivity check (you might want to implement more specific checks)
                import aiohttp
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                    async with session.get(service_url.split("/v1")[0] if "/v1" in service_url else service_url) as response:
                        service_status[service_name] = {
                            "status": "reachable" if response.status < 500 else "unreachable",
                            "status_code": response.status
                        }
            except Exception as e:
                service_status[service_name] = {
                    "status": "unreachable",
                    "error": str(e)
                }
                if service_name == "ollama":  # Critical service
                    overall_status = "degraded"
        else:
            service_status[service_name] = {"status": "not_configured"}

    response_time = (time.time() - start_time) * 1000

    return ComponentHealth(
        name="external_services",
        status=overall_status,
        response_time_ms=response_time,
        details=service_status
    )


@router.get("/", response_model=HealthStatus)
async def basic_health_check():
    """
    Basic health check endpoint.

    Returns simple health status for load balancers and basic monitoring.
    """
    return HealthStatus(
        status="healthy",
        timestamp=datetime.now(timezone.utc),
        details={
            "service": "ai-orchestrator",
            "version": "1.0.0",
            "uptime_seconds": time.time() - SERVICE_START_TIME
        }
    )


@router.get("/detailed", response_model=SystemHealth)
async def detailed_health_check():
    """
    Comprehensive health check endpoint.

    Checks all system components and returns detailed health information.
    """
    start_time = time.time()

    # Run all health checks concurrently
    health_checks = await asyncio.gather(
        check_database_health(),
        check_redis_health(),
        check_docker_health(),
        check_workspace_service_health(),
        check_external_services_health(),
        return_exceptions=True
    )

    components = []
    healthy_count = 0
    total_count = 0

    for health_check in health_checks:
        if isinstance(health_check, ComponentHealth):
            components.append(health_check)
            total_count += 1
            if health_check.status == "healthy":
                healthy_count += 1
        else:
            # Handle exceptions
            components.append(ComponentHealth(
                name="unknown_component",
                status="unhealthy",
                response_time_ms=0,
                details={"error": str(health_check)}
            ))
            total_count += 1

    # Determine overall system health
    if healthy_count == total_count:
        overall_status = "healthy"
    elif healthy_count > total_count / 2:
        overall_status = "degraded"
    else:
        overall_status = "unhealthy"

    total_check_time = (time.time() - start_time) * 1000

    return SystemHealth(
        status=overall_status,
        timestamp=datetime.now(timezone.utc),
        uptime_seconds=time.time() - SERVICE_START_TIME,
        components=components,
        summary={
            "healthy_components": healthy_count,
            "total_components": total_count,
            "health_percentage": (healthy_count / total_count * 100) if total_count > 0 else 0,
            "total_check_time_ms": total_check_time
        }
    )


@router.get("/ready")
async def readiness_check():
    """
    Kubernetes-style readiness probe.

    Returns 200 if service is ready to accept traffic.
    """
    try:
        # Check critical dependencies
        db_health = await check_database_health()
        redis_health = await check_redis_health()

        if db_health.status == "healthy" and redis_health.status == "healthy":
            return {"status": "ready"}
        else:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service not ready"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service not ready: {str(e)}"
        )


@router.get("/live")
async def liveness_check():
    """
    Kubernetes-style liveness probe.

    Returns 200 if service is alive (for restart decisions).
    """
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc),
        "uptime_seconds": time.time() - SERVICE_START_TIME
    }


@router.get("/database", response_model=ComponentHealth)
async def database_health_check():
    """
    Database health check endpoint.

    Performs a simple, fast query (SELECT 1) to verify database connection.
    """
    return await check_database_health()


@router.get("/redis", response_model=ComponentHealth)
async def redis_health_check():
    """
    Redis health check endpoint.

    Performs a PING command to verify Redis connection.
    """
    return await check_redis_health()


@router.get("/llm/local", response_model=ComponentHealth)
async def local_llm_health_check():
    """
    Local LLM (Ollama) health check endpoint.

    Checks the availability of the local Ollama service.
    """
    start_time = time.time()
    try:
        ollama_url = os.getenv("OLLAMA_BASE_URL", "http://host.docker.internal:11434")

        import aiohttp
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
            # Check if Ollama is running
            async with session.get(f"{ollama_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = data.get('models', [])

                    response_time = (time.time() - start_time) * 1000

                    return ComponentHealth(
                        name="ollama",
                        status="healthy",
                        response_time_ms=response_time,
                        details={
                            "connection": "active",
                            "url": ollama_url,
                            "models_available": len(models),
                            "models": [model.get('name', 'unknown') for model in models[:5]]  # First 5 models
                        }
                    )
                else:
                    response_time = (time.time() - start_time) * 1000
                    return ComponentHealth(
                        name="ollama",
                        status="unhealthy",
                        response_time_ms=response_time,
                        details={
                            "error": f"HTTP {response.status}",
                            "url": ollama_url,
                            "type": "http_error"
                        }
                    )

    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        return ComponentHealth(
            name="ollama",
            status="unhealthy",
            response_time_ms=response_time,
            details={
                "error": str(e),
                "url": ollama_url,
                "type": "connection_error"
            }
        )


@router.get("/metrics")
async def metrics_endpoint():
    """
    Prometheus-style metrics endpoint.

    Returns basic metrics for monitoring systems.
    """
    try:
        db = next(get_db())

        # Workspace metrics
        workspace_counts = {}
        for status in WorkspaceStatus:
            count = db.query(Workspace).filter(Workspace.status == status).count()
            workspace_counts[status.value] = count

        # System metrics
        uptime = time.time() - SERVICE_START_TIME

        return {
            "ai_orchestrator_uptime_seconds": uptime,
            "ai_orchestrator_workspaces_total": sum(workspace_counts.values()),
            **{f"ai_orchestrator_workspaces_{status}": count for status, count in workspace_counts.items()},
            "ai_orchestrator_health_checks_total": 1,  # This would be incremented in a real metrics system
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to generate metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate metrics"
        )