# Supabase Integration Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Python 3.10+
- Node.js 18+ (for admin dashboard)

### 1-Minute Deployment
```bash
# Clone and setup
git clone <repository>
cd codingagenttwo

# Generate keys and deploy
./scripts/generate-supabase-keys.sh
./scripts/deploy-supabase.sh --environment development

# Access services
# API: http://localhost:8001
# Dashboard: http://localhost:3000
# Kong Gateway: http://localhost:8000
```

## 🏗️ Architecture Overview

```
User Browser → Kong Gateway → Supabase Services → AI Orchestrator
                   ↓              ↓                    ↓
              API Routing    Auth + Vector DB      RAG + LLM
```

### Core Components
- **Supabase Database**: PostgreSQL with pgvector + Row Level Security
- **Supabase Auth**: JWT-based authentication
- **Kong Gateway**: API routing and rate limiting
- **AI Orchestrator**: Enhanced with vector search and RAG
- **Vector Service**: Permission-aware semantic search
- **RAG Service**: Context-aware code generation

## 📋 Deployment Options

### Option 1: New Installation
```bash
./scripts/deploy-supabase.sh --environment development --mode supabase
```

### Option 2: Migration from PostgreSQL
```bash
# Step 1: Deploy both systems
./scripts/deploy-supabase.sh --mode hybrid

# Step 2: Migrate data
python3 scripts/migrate-postgresql-to-supabase.py \
  --source-db postgresql://postgres:pass@localhost:5432/ai_coding_agent \
  --target-db postgresql://postgres:pass@localhost:5433/ai_coding_agent \
  --supabase-url http://localhost:8000 \
  --supabase-key YOUR_SERVICE_KEY

# Step 3: Switch to Supabase only
./scripts/deploy-supabase.sh --mode supabase
```

### Option 3: Production Deployment
```bash
# Generate production secrets
./scripts/generate-supabase-keys.sh

# Deploy with production config
./scripts/deploy-supabase.sh \
  --environment production \
  --mode supabase \
  --no-dry-run
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Core Configuration
USE_SUPABASE=true
ENVIRONMENT=development

# Database
POSTGRES_DB=ai_coding_agent
POSTGRES_PASSWORD=your_secure_password

# JWT & Auth
JWT_SECRET=your-32-char-secret
ANON_KEY=your_anon_jwt_key
SERVICE_ROLE_KEY=your_service_jwt_key

# Services
SUPABASE_URL=http://localhost:8000
KONG_HTTP_PORT=8000
```

### Docker Compose Files
- `docker-compose.yml` - Base services
- `docker-compose.supabase.yml` - Supabase stack
- `docker-compose.dev.yml` - Development overrides
- `docker-compose.prod.yml` - Production settings

## 🔐 Security Features

### Row Level Security (RLS)
- Automatic data isolation per user
- Project-based access control
- Permission-aware vector search

### Authentication Flow
1. User registers/logs in via Supabase Auth
2. Receives JWT with user ID and role
3. All API calls automatically filtered by user permissions
4. Vector search respects data ownership

### API Security
- JWT token validation
- Rate limiting via Kong
- CORS configuration
- Role-based access control

## 📊 API Endpoints

### Authentication
```bash
# Register
POST /api/v1/supabase/auth/register
{
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "username": "testuser"
}

# Login
POST /api/v1/supabase/auth/login
{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

### Document Management
```bash
# Create project
POST /api/v1/supabase/projects
{
  "name": "My Project",
  "description": "AI coding project"
}

# Upload document
POST /api/v1/supabase/projects/{id}/documents
{
  "name": "main.py",
  "content": "def hello(): print('world')",
  "file_type": "python"
}

# Process for vector search
POST /api/v1/supabase/documents/{id}/process
```

### Vector Search & RAG
```bash
# Search documents
POST /api/v1/supabase/search
{
  "query": "authentication function",
  "project_id": "uuid",
  "similarity_threshold": 0.7
}

# Generate code with RAG
POST /api/v1/supabase/rag/generate
{
  "query": "Create a login function",
  "project_id": "uuid",
  "llm_provider": "ollama"
}
```

## 🧪 Testing

### Run Test Suite
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run comprehensive tests
cd containers/ai-orchestrator
python -m pytest tests/test_supabase_integration.py -v

# Run specific test categories
pytest -k "test_auth" -v
pytest -k "test_vector" -v
pytest -k "test_rag" -v
```

### Manual Testing
```bash
# Health checks
curl http://localhost:8001/health
curl http://localhost:8001/api/v1/supabase/health

# Register test user
curl -X POST http://localhost:8001/api/v1/supabase/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123"}'
```

## 🐛 Troubleshooting

### Common Issues

**Services not starting:**
```bash
# Check logs
docker-compose -f docker-compose.yml -f docker-compose.supabase.yml logs

# Check network
docker network ls | grep ai-coding

# Restart services
./scripts/deploy-supabase.sh --no-migrations
```

**Database connection errors:**
```bash
# Check database status
docker-compose exec supabase-db pg_isready -U postgres

# Reset database
docker-compose down -v
./scripts/deploy-supabase.sh --mode supabase
```

**Authentication errors:**
```bash
# Verify JWT secret consistency across services
grep JWT_SECRET .env

# Check auth service logs
docker-compose logs supabase-auth
```

### Performance Optimization

**Vector Search:**
```sql
-- Rebuild vector index
REINDEX INDEX idx_document_sections_embedding;

-- Update statistics
ANALYZE document_sections;
```

**Database:**
- Adjust `shared_buffers` in postgresql.conf
- Monitor connection pool usage
- Enable query logging for slow queries

## 📈 Monitoring

### Health Endpoints
- Main: `GET /health`
- Supabase: `GET /api/v1/supabase/health`
- Database: `docker-compose exec supabase-db pg_isready`

### Metrics
- User statistics: `GET /api/v1/supabase/stats/user`
- Database metrics: Check logs and PostgreSQL stats
- Kong metrics: Available through Kong admin API

## 🔄 Maintenance

### Backup Strategy
```bash
# Automated backup during deployment
./scripts/deploy-supabase.sh --backup

# Manual backup
docker-compose exec supabase-db pg_dump -U postgres ai_coding_agent > backup.sql
```

### Updates
```bash
# Update Supabase services
docker-compose -f docker-compose.supabase.yml pull
./scripts/deploy-supabase.sh --no-migrations

# Update AI Orchestrator
docker-compose build ai-orchestrator
docker-compose up -d ai-orchestrator
```

## 🚀 Production Checklist

- [ ] Generate secure JWT secrets
- [ ] Configure SSL certificates
- [ ] Set up monitoring and alerting
- [ ] Configure backup strategy
- [ ] Review and harden security settings
- [ ] Load test with expected traffic
- [ ] Document disaster recovery procedures

## 📞 Support

For issues and questions:
1. Check logs: `docker-compose logs`
2. Review configuration: `.env` and compose files
3. Test individual components: health endpoints
4. Verify database: `psql` connection and queries

---

**🎉 You now have a fully functional AI Coding Agent with Supabase integration!**

Features included:
✅ JWT Authentication
✅ Vector Storage & Search
✅ RAG Code Generation
✅ Row Level Security
✅ API Gateway
✅ Multi-provider LLM Support