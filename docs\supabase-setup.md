# Supabase Authentication Setup Guide

This guide provides step-by-step instructions for setting up Supabase authentication for the AI Coding Agent project.

## Prerequisites

- Supabase account (free at https://supabase.com)
- Docker and Docker Compose installed
- Basic understanding of environment variables

## 1. Create Supabase Project

1. Go to https://supabase.com and sign up/sign in
2. Click "New Project"
3. Choose your organization and enter a project name
4. Select a region closest to you
5. Set a strong database password
6. Click "Create New Project"

Wait for the project to be provisioned (this may take a few minutes).

## 2. Get Project Credentials

Once your project is ready:

1. Go to your project dashboard
2. Click on "Project Settings" in the sidebar
3. Go to the "API" tab
4. Copy the following values:
   - Project URL (SUPABASE_URL)
   - anon public key (SUPABASE_KEY)
   - service role key (SUPABASE_SERVICE_KEY)

## 3. Configure Environment Variables

Update your `.env` file with the Supabase credentials:

```bash
# Supabase Configuration
SUPABASE_URL=your-supabase-project-url
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key

# Authentication
JWT_SECRET=your-jwt-secret-key-change-this-in-production
AUTH_REDIRECT_URL=http://localhost:3000
```

## 4. Database Schema Setup

The AI Coding Agent uses the following database tables:

### User Profiles Table

```sql
-- Create user profiles table
CREATE TABLE user_profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE,
    username TEXT UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (id)
);

-- Set up Row Level Security (RLS)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own profile"
    ON user_profiles FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
    ON user_profiles FOR UPDATE
    USING (auth.uid() = id);

-- Create a trigger to automatically create user profiles
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, username, full_name)
    VALUES (NEW.id, NEW.email, NEW.email);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### User Roles and Permissions

```sql
-- Create user roles table
CREATE TABLE user_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('admin', 'developer', 'user')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Set up RLS for user roles
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own roles"
    ON user_roles FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Only admins can manage roles"
    ON user_roles FOR ALL
    USING (EXISTS (
        SELECT 1 FROM user_roles
        WHERE user_roles.user_id = auth.uid()
        AND user_roles.role = 'admin'
    ));
```

## 5. Authentication Flow

### User Registration

1. Users register via the `/auth/register` endpoint
2. Supabase creates the user account
3. A JWT token is returned for authenticated access

### User Login

1. Users login via the `/auth/login` endpoint
2. Credentials are verified with Supabase Auth
3. A JWT token is returned for subsequent requests

### Protected Routes

All API endpoints (except `/health`, `/auth/register`, and `/auth/login`) require a valid JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## 6. Testing Authentication

### Register a New User

```bash
curl -X POST http://localhost:8000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "username": "testuser"
  }'
```

### Login Existing User

```bash
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### Access Protected Endpoint

```bash
curl -X GET http://localhost:8000/api/agents \
  -H "Authorization: Bearer <your-jwt-token>"
```

## 7. Docker Compose Integration

The docker-compose.yml file is already configured to pass Supabase environment variables to the ai-orchestrator service:

```yaml
services:
  ai-orchestrator:
    # ... other configuration
    environment:
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_KEY: ${SUPABASE_KEY}
      SUPABASE_SERVICE_KEY: ${SUPABASE_SERVICE_KEY}
      JWT_SECRET: ${JWT_SECRET}
```

## 8. Troubleshooting

### Common Issues

1. **"Invalid authentication credentials"**
   - Verify your SUPABASE_URL and SUPABASE_KEY are correct
   - Check that the user exists in Supabase Auth

2. **"Failed to create user"**
   - Ensure the email is not already registered
   - Check that the password meets requirements (min 6 characters)

3. **"Connection refused"**
   - Verify Supabase project is active
   - Check network connectivity

### Debugging Steps

1. Check Supabase Auth logs in the dashboard
2. Verify environment variables are loaded correctly
3. Test Supabase connection with a simple script
4. Check Docker container logs: `docker-compose logs ai-orchestrator`

## 9. Security Best Practices

1. **Environment Variables**: Never commit `.env` files to version control
2. **JWT Secrets**: Use strong, random secrets in production
3. **Row Level Security**: Always enable RLS on user data tables
4. **Service Keys**: Only use SUPABASE_SERVICE_KEY for server-side operations
5. **Token Expiration**: JWT tokens expire after 30 minutes by default

## 10. Next Steps

1. Set up email confirmation in Supabase Auth settings
2. Configure password reset flows
3. Implement role-based access control
4. Add multi-factor authentication (MFA)
5. Set up audit logging for authentication events

For more information, refer to the [Supabase Auth documentation](https://supabase.com/docs/guides/auth).
