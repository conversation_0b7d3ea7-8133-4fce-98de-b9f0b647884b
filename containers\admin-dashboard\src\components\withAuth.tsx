// c:\Users\<USER>\Desktop\codingagenttwo\containers\admin-dashboard\src\components\withAuth.tsx
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { supabase, type User, type Session } from '@/lib/supabaseClient';
import type { AuthChangeEvent } from '@supabase/supabase-js';

export type WithAuthInjectedProps = {
  user: User;
  logout: () => Promise<void>;
};

/**
 * HOC to enforce authentication on pages.
 * - Redirects to /login if there is no active session
 * - Injects { user, logout } into the wrapped component
 */
export function withAuth<P extends object>(WrappedComponent: React.ComponentType<P & WithAuthInjectedProps>) {
  const ComponentWithAuth: React.FC<P> = (props) => {
    const router = useRouter();
    const [loading, setLoading] = useState(true);
    const [user, setUser] = useState<User | null>(null);

    useEffect(() => {
      let mounted = true;
      const init = async () => {
        // Check current session
        const { data } = await supabase.auth.getSession();
        const currentUser = data.session?.user ?? null;
        if (!mounted) return;

        if (!currentUser) {
          // No session -> redirect to login
          router.replace('/login');
          return;
        }

        setUser(currentUser);
        setLoading(false);
      };

      init();

      // Subscribe to auth state changes (optional)
      const { data: sub } = supabase.auth.onAuthStateChange((_event: AuthChangeEvent, session: Session | null) => {
        const nextUser = session?.user ?? null;
        setUser(nextUser);
        if (!nextUser) {
          router.replace('/login');
        }
      });

      return () => {
        mounted = false;
        sub.subscription.unsubscribe();
      };
    }, [router]);

    const logout = async () => {
      await supabase.auth.signOut();
      router.replace('/login');
    };

    if (loading) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
            <p className="mt-4 text-gray-600">Checking session...</p>
          </div>
        </div>
      );
    }

    if (!user) return null; // Redirecting

    return <WrappedComponent {...(props as P)} user={user} logout={logout} />;
  };

  ComponentWithAuth.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

  return ComponentWithAuth;
}