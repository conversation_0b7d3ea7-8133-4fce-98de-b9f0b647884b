# FastAPI Reference Guide for AI Coding Agent

## Overview

This reference guide provides FastAPI implementation patterns specifically for the AI Coding Agent's **ai-orchestrator** service. It covers essential concepts, security, async programming, and architectural patterns aligned with the project's requirements.

## Table of Contents

1. [Core FastAPI Concepts](#core-fastapi-concepts)
2. [Asynchronous Programming](#asynchronous-programming)
3. [Dependency Injection](#dependency-injection)
4. [Security & Authentication](#security--authentication)
5. [Background Tasks](#background-tasks)
6. [Database Integration](#database-integration)
7. [WebSocket Support](#websocket-support)
8. [Error Handling & Validation](#error-handling--validation)
9. [Testing Patterns](#testing-patterns)
10. [Production Deployment](#production-deployment)

## Core FastAPI Concepts

### Basic Application Structure

```python
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, List, Optional
import asyncio
from datetime import datetime

app = FastAPI(
    title="AI Coding Agent Orchestrator",
    description="Central hub for AI-assisted development workflow",
    version="1.0.0",
    docs_url="/api/v1/docs",
    redoc_url="/api/v1/redoc"
)

# CORS configuration for admin dashboard
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Admin dashboard
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Health Check Endpoint

```python
@app.get("/api/v1/health")
async def health_check() -> Dict[str, str]:
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "ai-orchestrator"
    }
```

## Asynchronous Programming

### When to Use `async def` vs `def`

**Use `async def` for:**
- Database operations
- External API calls (LLM providers)
- File I/O operations
- Network requests

**Use `def` for:**
- CPU-bound operations
- Libraries without async support
- Simple data transformations

### Database Operations

```python
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends

async def get_db() -> AsyncSession:
    """Database dependency."""
    async with async_session() as session:
        yield session

@app.get("/api/v1/tasks/{task_id}")
async def get_task(
    task_id: str,
    db: AsyncSession = Depends(get_db)
) -> TaskResponse:
    """Retrieve task by ID."""
    result = await db.execute(
        select(Task).where(Task.id == task_id)
    )
    task = result.scalar_one_or_none()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return TaskResponse.from_orm(task)
```

### LLM Integration

```python
import httpx
from typing import AsyncGenerator

async def stream_llm_response(
    prompt: str,
    model: str = "llama3.1"
) -> AsyncGenerator[str, None]:
    """Stream LLM response for real-time feedback."""
    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST",
            "http://ollama:11434/api/generate",
            json={"model": model, "prompt": prompt, "stream": True}
        ) as response:
            async for chunk in response.aiter_text():
                if chunk.strip():
                    yield chunk

@app.post("/api/v1/code/generate")
async def generate_code(
    request: CodeGenerationRequest,
    background_tasks: BackgroundTasks
) -> StreamingResponse:
    """Generate code with streaming response."""

    async def generate():
        async for chunk in stream_llm_response(request.prompt):
            yield f"data: {chunk}\n\n"

    # Add validation task in background
    background_tasks.add_task(
        validate_generated_code,
        request.prompt,
        request.language
    )

    return StreamingResponse(
        generate(),
        media_type="text/event-stream"
    )
```

## Dependency Injection

### Service Layer Dependencies

```python
from abc import ABC, abstractmethod
from typing import Protocol

class ValidationService(Protocol):
    async def validate_syntax(self, code: str, language: str) -> bool: ...
    async def validate_security(self, code: str) -> List[str]: ...

class ApprovalService(Protocol):
    async def request_approval(self, operation: str, context: Dict) -> str: ...
    async def check_approval_status(self, approval_id: str) -> bool: ...

# Dependency providers
async def get_validation_service() -> ValidationService:
    return ValidationServiceImpl()

async def get_approval_service() -> ApprovalService:
    return ApprovalServiceImpl()

# Usage in endpoints
@app.post("/api/v1/code/validate")
async def validate_code(
    request: CodeValidationRequest,
    validator: ValidationService = Depends(get_validation_service),
    db: AsyncSession = Depends(get_db)
) -> ValidationResponse:
    """Validate code through multi-layer validation."""

    # Syntax validation
    syntax_valid = await validator.validate_syntax(
        request.code,
        request.language
    )

    # Security validation
    security_issues = await validator.validate_security(request.code)

    # Store validation results
    validation = ValidationResult(
        code_hash=hash(request.code),
        syntax_valid=syntax_valid,
        security_issues=security_issues,
        timestamp=datetime.utcnow()
    )

    db.add(validation)
    await db.commit()

    return ValidationResponse(
        valid=syntax_valid and not security_issues,
        issues=security_issues
    )
```

### Configuration Dependencies

```python
from functools import lru_cache
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    database_url: str
    redis_url: str
    ollama_url: str = "http://ollama:11434"
    secret_key: str
    admin_password_hash: str

    class Config:
        env_file = ".env"

@lru_cache()
def get_settings() -> Settings:
    return Settings()

# Usage
@app.get("/api/v1/config")
async def get_config(
    settings: Settings = Depends(get_settings)
) -> Dict[str, str]:
    """Get non-sensitive configuration."""
    return {
        "ollama_url": settings.ollama_url,
        "version": "1.0.0"
    }
```

## Security & Authentication

### JWT Authentication

```python
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: Dict[str, Any]) -> str:
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=30)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm="HS256")

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Validate JWT token and return user."""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=["HS256"])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    if user is None:
        raise HTTPException(status_code=401, detail="User not found")
    return user

# Protected endpoint
@app.post("/api/v1/admin/reset-validation")
async def reset_validation(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, str]:
    """Reset validation state (admin only)."""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    await db.execute(delete(ValidationResult))
    await db.commit()

    return {"message": "Validation state reset successfully"}
```

### API Key Authentication

```python
from fastapi.security import APIKeyHeader

api_key_header = APIKeyHeader(name="X-API-Key")

async def get_api_key(api_key: str = Depends(api_key_header)) -> str:
    """Validate API key."""
    if api_key != API_KEY:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return api_key

@app.post("/api/v1/webhook/github")
async def github_webhook(
    payload: GitHubWebhookPayload,
    api_key: str = Depends(get_api_key)
) -> Dict[str, str]:
    """Handle GitHub webhook events."""
    # Process webhook
    return {"status": "processed"}
```

## Background Tasks

### Task Processing

```python
from fastapi import BackgroundTasks
import asyncio
from enum import Enum

class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

async def execute_validation_pipeline(
    task_id: str,
    code: str,
    language: str
) -> None:
    """Execute comprehensive validation pipeline."""
    try:
        # Update task status
        await update_task_status(task_id, TaskStatus.RUNNING)

        # Step 1: Syntax validation
        syntax_result = await validate_syntax(code, language)
        await update_task_progress(task_id, 25, "Syntax validation complete")

        # Step 2: Security scan
        security_result = await validate_security(code)
        await update_task_progress(task_id, 50, "Security scan complete")

        # Step 3: Functionality test
        test_result = await run_functionality_tests(code, language)
        await update_task_progress(task_id, 75, "Functionality tests complete")

        # Step 4: Integration validation
        integration_result = await validate_integration(code)
        await update_task_progress(task_id, 100, "Validation pipeline complete")

        # Store results
        await store_validation_results(task_id, {
            "syntax": syntax_result,
            "security": security_result,
            "functionality": test_result,
            "integration": integration_result
        })

        await update_task_status(task_id, TaskStatus.COMPLETED)

    except Exception as e:
        await update_task_status(task_id, TaskStatus.FAILED)
        await log_error(task_id, str(e))

@app.post("/api/v1/validation/start")
async def start_validation(
    request: ValidationRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
) -> TaskResponse:
    """Start validation pipeline in background."""

    # Create task record
    task = Task(
        id=str(uuid4()),
        type="validation",
        status=TaskStatus.PENDING,
        created_at=datetime.utcnow()
    )

    db.add(task)
    await db.commit()

    # Start background validation
    background_tasks.add_task(
        execute_validation_pipeline,
        task.id,
        request.code,
        request.language
    )

    return TaskResponse(
        task_id=task.id,
        status=task.status,
        message="Validation pipeline started"
    )
```

## WebSocket Support

### Real-time Monitoring

```python
from fastapi import WebSocket, WebSocketDisconnect
from typing import List
import json

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: Dict[str, Any]):
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                # Handle disconnected clients
                self.active_connections.remove(connection)

manager = ConnectionManager()

@app.websocket("/api/v1/ws/monitoring")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time monitoring."""
    await manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)

            if message.get("type") == "subscribe":
                # Handle subscription to specific events
                await websocket.send_text(json.dumps({
                    "type": "subscription_confirmed",
                    "events": message.get("events", [])
                }))

    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Broadcast validation progress
async def notify_validation_progress(task_id: str, progress: int, message: str):
    """Send validation progress to connected clients."""
    await manager.broadcast({
        "type": "validation_progress",
        "task_id": task_id,
        "progress": progress,
        "message": message,
        "timestamp": datetime.utcnow().isoformat()
    })
```

## Error Handling & Validation

### Custom Exception Handlers

```python
from fastapi import Request
from fastapi.responses import JSONResponse

class ValidationError(Exception):
    def __init__(self, message: str, code: str = "VALIDATION_ERROR"):
        self.message = message
        self.code = code

class ApprovalRequiredError(Exception):
    def __init__(self, operation: str, approval_id: str):
        self.operation = operation
        self.approval_id = approval_id

@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    return JSONResponse(
        status_code=422,
        content={
            "error": {
                "code": exc.code,
                "message": exc.message,
                "type": "validation_error"
            }
        }
    )

@app.exception_handler(ApprovalRequiredError)
async def approval_exception_handler(request: Request, exc: ApprovalRequiredError):
    return JSONResponse(
        status_code=202,
        content={
            "error": {
                "code": "APPROVAL_REQUIRED",
                "message": f"Operation '{exc.operation}' requires approval",
                "approval_id": exc.approval_id,
                "type": "approval_required"
            }
        }
    )
```

### Request Validation

```python
from pydantic import BaseModel, validator, Field
from typing import Literal

class CodeGenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=10, max_length=5000)
    language: Literal["python", "javascript", "typescript", "java"] = "python"
    max_tokens: int = Field(default=1000, ge=100, le=4000)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)

    @validator('prompt')
    def validate_prompt(cls, v):
        if any(keyword in v.lower() for keyword in ['rm -rf', 'delete', 'drop table']):
            raise ValueError('Potentially dangerous operations detected in prompt')
        return v

class ValidationRequest(BaseModel):
    code: str = Field(..., min_length=1, max_length=50000)
    language: str = Field(..., regex=r'^[a-zA-Z]+$')
    strict_mode: bool = False

    class Config:
        schema_extra = {
            "example": {
                "code": "def hello_world():\n    return 'Hello, World!'",
                "language": "python",
                "strict_mode": True
            }
        }
```

## Testing Patterns

### Unit Tests

```python
import pytest
from httpx import AsyncClient
from fastapi.testclient import TestClient

@pytest.fixture
async def async_client():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

@pytest.mark.asyncio
async def test_health_check(async_client: AsyncClient):
    """Test health check endpoint."""
    response = await async_client.get("/api/v1/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data

@pytest.mark.asyncio
async def test_code_validation(async_client: AsyncClient, mock_db):
    """Test code validation endpoint."""
    payload = {
        "code": "def test(): return True",
        "language": "python"
    }

    response = await async_client.post("/api/v1/code/validate", json=payload)
    assert response.status_code == 200

    data = response.json()
    assert "valid" in data
    assert "issues" in data

# Mock dependencies for testing
@pytest.fixture
def mock_validation_service():
    class MockValidationService:
        async def validate_syntax(self, code: str, language: str) -> bool:
            return True

        async def validate_security(self, code: str) -> List[str]:
            return []

    return MockValidationService()

def test_with_dependency_override(mock_validation_service):
    """Test with dependency override."""
    app.dependency_overrides[get_validation_service] = lambda: mock_validation_service

    with TestClient(app) as client:
        response = client.post("/api/v1/code/validate", json={
            "code": "print('hello')",
            "language": "python"
        })
        assert response.status_code == 200

    app.dependency_overrides.clear()
```

## Production Deployment

### Application Startup

```python
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    # Initialize database
    await init_database()

    # Start background services
    asyncio.create_task(monitor_system_health())
    asyncio.create_task(cleanup_expired_tasks())

    # Initialize LLM connection
    await test_ollama_connection()

    logger.info("AI Orchestrator started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    await cleanup_database_connections()
    await cancel_background_tasks()
    logger.info("AI Orchestrator shutdown complete")

# Production configuration
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True,
        reload=False  # Set to True for development
    )
```

### Docker Integration

```dockerfile
# Use in Dockerfile
FROM python:3.10-slim

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . /app
WORKDIR /app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# Run application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## Key Takeaways

1. **Use async/await** for I/O operations (database, API calls)
2. **Leverage dependency injection** for clean architecture
3. **Implement proper error handling** with custom exceptions
4. **Use background tasks** for long-running operations
5. **Add comprehensive logging** and monitoring
6. **Write thorough tests** with dependency mocking
7. **Follow security best practices** with JWT/API keys
8. **Use WebSockets** for real-time communication
9. **Validate all inputs** with Pydantic models
10. **Design for production** with proper startup/shutdown handling

This guide provides the foundation for building a robust, scalable FastAPI application for the AI Coding Agent's orchestrator service.