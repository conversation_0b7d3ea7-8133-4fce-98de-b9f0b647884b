/**
 * AI Coding Agent Chat Extension
 * Provides real-time chat interface with AI agents via WebSocket
 */

import * as vscode from 'vscode';
import { ChatWebViewProvider } from './chatWebViewProvider';
import { WebSocketManager } from './webSocketManager';

let chatProvider: ChatWebViewProvider;
let webSocketManager: WebSocketManager;

export function activate(context: vscode.ExtensionContext) {
    console.log('AI Chat Extension is now active');

    // Initialize WebSocket manager with container-aware configuration
    const config = vscode.workspace.getConfiguration('aiChat');
    const defaultUrl = process.env.NODE_ENV === 'production'
        ? 'ws://ai-orchestrator:8000'
        : 'ws://localhost:8000';

    webSocketManager = new WebSocketManager({
        serverUrl: config.get('serverUrl', defaultUrl)
    });

    // Initialize chat provider
    chatProvider = new ChatWebViewProvider(context.extensionUri, webSocketManager);

    // Register webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            ChatWebViewProvider.viewType,
            chatProvider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        )
    );

    // Register commands as specified in package.json
    context.subscriptions.push(
        vscode.commands.registerCommand('aiChatExtension.openChat', () => {
            chatProvider.show();
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('aiChatExtension.clearHistory', () => {
            chatProvider.clearHistory();
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('aiChatExtension.reconnect', () => {
            webSocketManager.reconnect();
        })
    );

    // Auto-connect if enabled
    if (config.get('autoConnect', true)) {
        webSocketManager.connect();
    }

    // Show welcome message
    vscode.window.showInformationMessage('AI Chat Extension activated! Open the AI Chat panel to start chatting.');
}

export function deactivate() {
    if (webSocketManager) {
        webSocketManager.dispose();
    }
    console.log('AI Chat Extension deactivated');
}