# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: Universal LLM service wrapper for both local (Ollama) and cloud providers
#
# DEPRECATION NOTICE: This service is deprecated in favor of EnhancedLLMService
# in src/services/enhanced_llm_service.py which provides better error handling,
# monitoring, rate limiting, and optional dependency support.
#
# This module is maintained for backward compatibility only.

import os
import json
import aiohttp
import asyncio
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class LLMProvider(Enum):
    OLLAMA = "ollama"
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"

class LLMResponse:
    def __init__(self, content: str, model: str, provider: str, usage: Optional[Dict] = None):
        self.content = content
        self.model = model
        self.provider = provider
        self.usage = usage or {}

class UniversalLLMService:
    """Universal LLM service that can work with both local (Ollama) and cloud providers"""

    def __init__(self):
        self.ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://host.docker.internal:11434")
        self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")

        self.default_local_provider = os.getenv("DEFAULT_LOCAL_PROVIDER", "ollama")
        self.default_cloud_provider = os.getenv("DEFAULT_CLOUD_PROVIDER", "openrouter")
        self.enable_cloud_fallback = os.getenv("ENABLE_CLOUD_FALLBACK", "true").lower() == "true"

    async def test_ollama_connection(self) -> bool:
        """Test connection to Ollama on host"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.ollama_base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"Successfully connected to Ollama. Available models: {len(data.get('models', []))}")
                        return True
                    else:
                        logger.error(f"Failed to connect to Ollama: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error connecting to Ollama: {str(e)}")
            return False

    async def list_ollama_models(self) -> List[Dict[str, Any]]:
        """List available models in Ollama"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.ollama_base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("models", [])
                    else:
                        logger.error(f"Failed to list Ollama models: HTTP {response.status}")
                        return []
        except Exception as e:
            logger.error(f"Error listing Ollama models: {str(e)}")
            return []

    async def pull_ollama_model(self, model_name: str) -> bool:
        """Pull a model in Ollama"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_base_url}/api/pull",
                    json={"name": model_name}
                ) as response:
                    if response.status == 200:
                        logger.info(f"Successfully pulled model: {model_name}")
                        return True
                    else:
                        logger.error(f"Failed to pull model {model_name}: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {str(e)}")
            return False

    async def generate_ollama(
        self,
        model: str,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> Optional[LLMResponse]:
        """Generate response using Ollama"""
        try:
            # Prepare messages for Ollama chat format
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            payload = {
                "model": model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": temperature
                }
            }

            if max_tokens:
                payload["options"]["num_predict"] = max_tokens

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_base_url}/api/chat",
                    json=payload
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        content = data.get("message", {}).get("content", "")

                        return LLMResponse(
                            content=content,
                            model=model,
                            provider="ollama",
                            usage={
                                "prompt_eval_count": data.get("prompt_eval_count", 0),
                                "eval_count": data.get("eval_count", 0),
                                "total_duration": data.get("total_duration", 0)
                            }
                        )
                    else:
                        logger.error(f"Ollama generation failed: HTTP {response.status}")
                        error_text = await response.text()
                        logger.error(f"Error details: {error_text}")
                        return None

        except Exception as e:
            logger.error(f"Error generating with Ollama: {str(e)}")
            return None

    async def generate_openrouter(
        self,
        model: str,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> Optional[LLMResponse]:
        """Generate response using OpenRouter"""
        if not self.openrouter_api_key:
            logger.error("OpenRouter API key not configured")
            return None

        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            payload = {
                "model": model,
                "messages": messages,
                "temperature": temperature
            }

            if max_tokens:
                payload["max_tokens"] = max_tokens

            headers = {
                "Authorization": f"Bearer {self.openrouter_api_key}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    json=payload,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        content = data["choices"][0]["message"]["content"]

                        return LLMResponse(
                            content=content,
                            model=model,
                            provider="openrouter",
                            usage=data.get("usage", {})
                        )
                    else:
                        logger.error(f"OpenRouter generation failed: HTTP {response.status}")
                        return None

        except Exception as e:
            logger.error(f"Error generating with OpenRouter: {str(e)}")
            return None

    async def generate(
        self,
        prompt: str,
        model: Optional[str] = None,
        provider: Optional[str] = None,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> Optional[LLMResponse]:
        """Universal generate method with fallback support"""

        # Determine provider and model
        if not provider:
            provider = self.default_local_provider

        if not model:
            if provider == "ollama":
                model = "llama3.2"  # Default Ollama model
            elif provider == "openrouter":
                model = "meta-llama/llama-3.1-8b-instruct:free"  # Free tier model

        # Ensure model is not None before proceeding
        if model is None:
            logger.error("No model specified and no default available")
            return None

        # Try primary provider
        if provider == "ollama":
            result = await self.generate_ollama(model, prompt, system_prompt, temperature, max_tokens)
            if result:
                return result
            elif self.enable_cloud_fallback:
                logger.info("Ollama failed, falling back to cloud provider")
                return await self.generate_openrouter(
                    "meta-llama/llama-3.1-8b-instruct:free",
                    prompt, system_prompt, temperature, max_tokens
                )

        elif provider == "openrouter":
            result = await self.generate_openrouter(model, prompt, system_prompt, temperature, max_tokens)
            if result:
                return result

        logger.error(f"All providers failed for generation with model {model}")
        return None

# Global instance
llm_service = UniversalLLMService()

# Backward compatibility alias
LLMService = UniversalLLMService