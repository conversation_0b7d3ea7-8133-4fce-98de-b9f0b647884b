"""
Docker Service for Multi-Tenant Workspace Management.

This module provides Docker container management capabilities for creating,
starting, stopping, and monitoring user-specific code-server workspaces.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import json
import logging
import os
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

import docker
from docker.models.containers import Container
from docker.errors import DockerException, NotFound, APIError

from ..models.workspace import Workspace, WorkspaceStatus

# Configure logging
logger = logging.getLogger(__name__)


class DockerWorkspaceService:
    """
    Service for managing Docker containers for user workspaces.

    Handles the lifecycle of user-specific code-server containers
    with proper isolation, networking, and routing configuration.
    """

    def __init__(self):
        """Initialize Docker workspace service."""
        try:
            self.client = docker.from_env()
            self.client.ping()
            logger.info("Docker client initialized successfully")
        except DockerException as e:
            logger.error(f"Failed to initialize Docker client: {str(e)}")
            raise

    def generate_workspace_config(self, user_id: str) -> Dict[str, Any]:
        """
        Generate workspace configuration for a user.

        Args:
            user_id: User identifier

        Returns:
            Workspace configuration dictionary
        """
        # Generate unique subdomain and container name
        subdomain = f"user-{user_id.lower().replace('_', '-')}"
        container_name = f"workspace-{user_id.lower()}-{uuid.uuid4().hex[:8]}"

        # Get resource limits from environment variables
        cpu_limit = os.getenv("CONTAINER_CPU_LIMIT", "1.0")
        memory_limit = os.getenv("CONTAINER_MEMORY_LIMIT", "2G")
        enable_limits = os.getenv("DOCKER_RESOURCE_LIMITS", "true").lower() == "true"

        return {
            "subdomain": subdomain,
            "container_name": container_name,
            "image": "ai-coding-agent-dev-code-server-template",  # Use our template image
            "networks": ["ai-coding-agent-dev_ai-coding-agent-network", "ai-coding-agent-dev_traefik-network"],
            "environment": {
                "PASSWORD": os.getenv("CODE_SERVER_PASSWORD", "secure-password"),
                "DOCKER_USER": "coder",
                "USER_ID": user_id
            },
            "labels": {
                "traefik.enable": "true",
                f"traefik.http.routers.{subdomain}.rule": f"Host(`{subdomain}.localhost`)",
                f"traefik.http.routers.{subdomain}.entrypoints": "web",
                f"traefik.http.services.{subdomain}.loadbalancer.server.port": "8080",
                "traefik.docker.network": "ai-coding-agent-dev_traefik-network",
                "workspace.user_id": user_id,
                "workspace.type": "code-server",
                "workspace.managed_by": "ai-orchestrator",
                "workspace.created_at": datetime.now(timezone.utc).isoformat()
            },
            "volumes": {
                f"workspace-{user_id}-data": {
                    "bind": "/home/<USER>",
                    "mode": "rw"
                },
                f"workspace-{user_id}-projects": {
                    "bind": "/home/<USER>/workspace",
                    "mode": "rw"
                }
            },
            "resource_limits": {
                "enabled": enable_limits,
                "cpu_limit": cpu_limit,
                "memory_limit": memory_limit,
                "memory_reservation": "512m",
                "cpu_reservation": "0.25"
            },
            "security": {
                "user": "coder",
                "no_new_privileges": True,
                "security_opt": ["no-new-privileges:true"],
                "cap_drop": ["ALL"],
                "cap_add": ["SETUID", "SETGID"],
                "read_only": False,  # Code-server needs write access
                "tmpfs": {
                    "/tmp": "rw,noexec,nosuid,size=200m",
                    "/var/tmp": "rw,noexec,nosuid,size=100m"
                }
            }
        }

    async def create_workspace(self, user_id: str, workspace: Workspace) -> Container:
        """
        Create and start a new workspace container for a user.

        Args:
            user_id: User identifier
            workspace: Workspace database model

        Returns:
            Docker container instance

        Raises:
            DockerException: If container creation fails
        """
        try:
            config = self.generate_workspace_config(user_id)

            logger.info(f"Creating workspace container for user {user_id}")

            # Create volumes first
            for volume_name in config["volumes"]:
                try:
                    self.client.volumes.create(name=volume_name)
                    logger.debug(f"Created volume: {volume_name}")
                except APIError as e:
                    if "already exists" not in str(e):
                        raise

            # Prepare container configuration with security and resource limits
            container_config = {
                "image": config["image"],
                "name": config["container_name"],
                "environment": config["environment"],
                "labels": config["labels"],
                "volumes": {
                    vol_name: vol_config["bind"]
                    for vol_name, vol_config in config["volumes"].items()
                },
                "networks": config["networks"],
                "restart_policy": {"Name": "unless-stopped", "MaximumRetryCount": 3},
                "detach": True,
                # Security configuration
                "user": config["security"]["user"],
                "security_opt": config["security"]["security_opt"],
                "cap_drop": config["security"]["cap_drop"],
                "cap_add": config["security"]["cap_add"],
                "tmpfs": config["security"]["tmpfs"]
            }

            # Add resource limits if enabled
            if config["resource_limits"]["enabled"]:
                # Resource limits using the low-level API approach
                host_config = {
                    "CpuPeriod": 100000,  # 100ms period
                    "CpuQuota": int(float(config["resource_limits"]["cpu_limit"]) * 100000),
                    "Memory": self._parse_memory_limit(config["resource_limits"]["memory_limit"]),
                    "MemoryReservation": self._parse_memory_limit(config["resource_limits"]["memory_reservation"]),
                    "OomKillDisable": False,  # Allow OOM killer for safety
                    "PidsLimit": 1024,  # Limit number of processes
                    "ShmSize": 64 * 1024 * 1024,  # 64MB shared memory
                }
                container_config["host_config"] = self.client.api.create_host_config(**host_config)

                logger.info(f"Applied resource limits: CPU={config['resource_limits']['cpu_limit']}, Memory={config['resource_limits']['memory_limit']}")

            # Add health check
            container_config["healthcheck"] = {
                "test": ["CMD", "curl", "-f", "http://localhost:8080/login"],
                "interval": 30000000000,  # 30 seconds in nanoseconds
                "timeout": 10000000000,   # 10 seconds in nanoseconds
                "retries": 3,
                "start_period": 60000000000  # 60 seconds in nanoseconds
            }

            # Create and start container
            if "host_config" in container_config:
                # Use low-level API for advanced configuration
                container_spec = {
                    "image": container_config["image"],
                    "name": container_config["name"],
                    "environment": container_config["environment"],
                    "labels": container_config["labels"],
                    "volumes": list(container_config["volumes"].keys()),
                    "host_config": container_config["host_config"],
                    "healthcheck": container_config["healthcheck"]
                }
                container_id = self.client.api.create_container(**container_spec)
                container = self.client.containers.get(container_id["Id"])

                # Connect to networks
                for network_name in config["networks"]:
                    try:
                        network = self.client.networks.get(network_name)
                        network.connect(container)
                    except Exception as e:
                        logger.warning(f"Failed to connect to network {network_name}: {e}")

                container.start()
            else:
                # Use high-level API for simple configuration
                container = self.client.containers.run(**container_config)

            logger.info(f"Created container {container.id} for user {user_id}")

            return container

        except DockerException as e:
            logger.error(f"Failed to create workspace for user {user_id}: {str(e)}")
            raise

    async def stop_workspace(self, container_id: str) -> bool:
        """
        Stop a workspace container.

        Args:
            container_id: Container identifier

        Returns:
            True if successful, False otherwise
        """
        try:
            container = self.client.containers.get(container_id)
            container.stop(timeout=30)
            logger.info(f"Stopped container {container_id}")
            return True

        except NotFound:
            logger.warning(f"Container {container_id} not found")
            return False
        except DockerException as e:
            logger.error(f"Failed to stop container {container_id}: {str(e)}")
            return False

    async def remove_workspace(self, container_id: str, remove_volumes: bool = False) -> bool:
        """
        Remove a workspace container and optionally its volumes.

        Args:
            container_id: Container identifier
            remove_volumes: Whether to remove associated volumes

        Returns:
            True if successful, False otherwise
        """
        try:
            container = self.client.containers.get(container_id)

            # Get user_id from container labels for volume cleanup
            user_id = container.labels.get("workspace.user_id")

            # Stop and remove container
            if container.status == "running":
                container.stop(timeout=30)
            container.remove()

            logger.info(f"Removed container {container_id}")

            # Remove volumes if requested
            if remove_volumes and user_id:
                await self._cleanup_user_volumes(user_id)

            return True

        except NotFound:
            logger.warning(f"Container {container_id} not found")
            return False
        except DockerException as e:
            logger.error(f"Failed to remove container {container_id}: {str(e)}")
            return False

    async def get_container_status(self, container_id: str) -> Optional[Dict[str, Any]]:
        """
        Get container status and information.

        Args:
            container_id: Container identifier

        Returns:
            Container status dictionary or None if not found
        """
        try:
            container = self.client.containers.get(container_id)
            container.reload()

            return {
                "id": container.id,
                "name": container.name,
                "status": container.status,
                "created": container.attrs["Created"],
                "started_at": container.attrs["State"].get("StartedAt"),
                "health": container.attrs["State"].get("Health", {}).get("Status", "unknown"),
                "ports": container.attrs["NetworkSettings"]["Ports"]
            }

        except NotFound:
            return None
        except DockerException as e:
            logger.error(f"Failed to get container status {container_id}: {str(e)}")
            return None

    async def list_user_workspaces(self, user_id: str) -> List[Dict[str, Any]]:
        """
        List all containers for a specific user.

        Args:
            user_id: User identifier

        Returns:
            List of container information dictionaries
        """
        try:
            containers = self.client.containers.list(
                all=True,
                filters={"label": f"workspace.user_id={user_id}"}
            )

            result = []
            for container in containers:
                result.append({
                    "id": container.id,
                    "name": container.name,
                    "status": container.status,
                    "labels": container.labels
                })

            return result

        except DockerException as e:
            logger.error(f"Failed to list containers for user {user_id}: {str(e)}")
            return []

    async def _cleanup_user_volumes(self, user_id: str) -> None:
        """
        Clean up volumes associated with a user.

        Args:
            user_id: User identifier
        """
        try:
            volume_patterns = [
                f"workspace-{user_id}-data",
                f"workspace-{user_id}-projects"
            ]

            for pattern in volume_patterns:
                try:
                    volume = self.client.volumes.get(pattern)
                    volume.remove()
                    logger.info(f"Removed volume: {pattern}")
                except NotFound:
                    logger.debug(f"Volume not found: {pattern}")

        except DockerException as e:
            logger.error(f"Failed to cleanup volumes for user {user_id}: {str(e)}")

    def _parse_memory_limit(self, memory_str: str) -> int:
        """
        Parse memory limit string to bytes.

        Args:
            memory_str: Memory limit string (e.g., '2G', '512M', '1024K')

        Returns:
            Memory limit in bytes
        """
        memory_str = memory_str.upper().strip()

        if memory_str.endswith('G'):
            return int(float(memory_str[:-1]) * 1024 * 1024 * 1024)
        elif memory_str.endswith('M'):
            return int(float(memory_str[:-1]) * 1024 * 1024)
        elif memory_str.endswith('K'):
            return int(float(memory_str[:-1]) * 1024)
        else:
            return int(memory_str)  # Assume bytes

    async def validate_user_container_limits(self, user_id: str) -> Dict[str, Any]:
        """
        Validate that user containers are within resource limits.

        Args:
            user_id: User identifier

        Returns:
            Validation result dictionary
        """
        try:
            containers = await self.list_user_workspaces(user_id)
            max_containers = int(os.getenv("MAX_USER_CONTAINERS", "10"))

            running_containers = [
                c for c in containers
                if c.get("status") == "running"
            ]

            return {
                "user_id": user_id,
                "total_containers": len(containers),
                "running_containers": len(running_containers),
                "max_allowed": max_containers,
                "within_limits": len(containers) <= max_containers,
                "can_create_new": len(running_containers) < max_containers
            }

        except Exception as e:
            logger.error(f"Failed to validate container limits for user {user_id}: {e}")
            return {
                "user_id": user_id,
                "error": str(e),
                "within_limits": False,
                "can_create_new": False
            }
        """
        Perform health check on Docker service.

        Returns:
            Health status dictionary
        """
        try:
            # Ping Docker daemon
            self.client.ping()

            # Get system info
            info = self.client.info()

            return {
                "status": "healthy",
                "docker_version": info.get("ServerVersion"),
                "containers_running": info.get("ContainersRunning", 0),
                "containers_total": info.get("Containers", 0),
                "images": info.get("Images", 0)
            }

        except DockerException as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global Docker service instance
_docker_service: Optional[DockerWorkspaceService] = None


def get_docker_service() -> DockerWorkspaceService:
    """
    Get Docker workspace service singleton.

    Returns:
        DockerWorkspaceService instance
    """
    global _docker_service
    if _docker_service is None:
        _docker_service = DockerWorkspaceService()
    return _docker_service