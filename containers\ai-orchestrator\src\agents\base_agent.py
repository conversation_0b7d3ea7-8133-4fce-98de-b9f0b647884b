# Project: AI Coding Agent
# Purpose: Base agent class with validation and error recovery capabilities

import asyncio
import json
import logging
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

from ..models.validation_models import (
    AgentType, Task, TaskResult, ValidationResult, RecoveryResult,
    ExecutionStatus, ErrorType, ApprovalRequest
)
from ..utils.llm_service import LLMService


class BaseAgent(ABC):
    """
    Abstract base class for all AI agents with built-in validation and error recovery.

    Provides common functionality for:
    - Task execution with validation gates
    - Error recovery and retry mechanisms
    - Resource locking for sequential execution
    - Status reporting and logging
    """

    def __init__(self, agent_type: AgentType, max_concurrent_tasks: int = 1):
        self.agent_type = agent_type
        self.agent_id = f"{agent_type.value}_{int(time.time())}"
        self.max_concurrent_tasks = max_concurrent_tasks

        # Resource management
        self._execution_lock = asyncio.Semaphore(max_concurrent_tasks)
        self._is_active = False
        self._current_task: Optional[Task] = None

        # Services
        self.llm_service = LLMService()
        self.logger = logging.getLogger(f"agent.{agent_type.value}")

        # Task tracking
        self._task_history: List[Dict[str, Any]] = []
        self._error_count = 0
        self._success_count = 0

        self.logger.info(f"Initialized {self.__class__.__name__} with ID: {self.agent_id}")

    @property
    def is_active(self) -> bool:
        """Check if agent is currently executing a task"""
        return self._is_active

    @property
    def current_task_id(self) -> Optional[str]:
        """Get current task ID if any"""
        return self._current_task.id if self._current_task else None

    @property
    def success_rate(self) -> float:
        """Calculate agent success rate"""
        total_tasks = self._success_count + self._error_count
        return self._success_count / total_tasks if total_tasks > 0 else 0.0

    async def execute_task_with_validation(self, task: Task) -> TaskResult:
        """
        Execute a task with comprehensive validation and error recovery.

        Args:
            task: The task to execute

        Returns:
            TaskResult: Result of task execution with validation status

        Raises:
            Exception: If task fails after all retry attempts
        """
        async with self._execution_lock:
            self._is_active = True
            self._current_task = task

            task.assigned_agent = self.agent_id
            task.started_at = datetime.now()
            task.status = ExecutionStatus.IN_PROGRESS

            self.logger.info(f"Starting task: {task.title} (ID: {task.id})")

            try:
                # Pre-execution validation
                pre_validation = await self._pre_execution_validation(task)
                if not pre_validation.is_valid:
                    raise ValueError(f"Pre-execution validation failed: {pre_validation.error}")

                # Execute task with retry logic
                result = await self._execute_with_retry(task)

                # Post-execution validation
                post_validation = await self._post_execution_validation(task, result)
                if not post_validation.is_valid:
                    self.logger.warning(f"Post-execution validation issues: {post_validation.error}")
                    result.metadata["validation_warnings"] = post_validation.error

                # Update task status
                task.status = ExecutionStatus.COMPLETED if result.success else ExecutionStatus.FAILED
                task.completed_at = datetime.now()

                # Update statistics
                if result.success:
                    self._success_count += 1
                else:
                    self._error_count += 1

                # Record task history
                self._record_task_execution(task, result, post_validation)

                self.logger.info(f"Task completed: {task.title} - Success: {result.success}")
                return result

            except Exception as e:
                self.logger.error(f"Task execution failed: {task.title} - Error: {str(e)}")
                task.status = ExecutionStatus.FAILED
                task.completed_at = datetime.now()
                self._error_count += 1

                # Create failure result
                result = TaskResult(
                    success=False,
                    error=str(e),
                    metadata={"exception_type": type(e).__name__}
                )
                self._record_task_execution(task, result, ValidationResult.failure(str(e)))

                raise

            finally:
                self._is_active = False
                self._current_task = None

    async def _execute_with_retry(self, task: Task) -> TaskResult:
        """Execute task with retry logic and error recovery"""
        last_error = None

        for attempt in range(task.max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"Retry attempt {attempt} for task: {task.title}")
                    task.retry_count = attempt

                # Core task execution (implemented by subclasses)
                result = await self._execute_core_task(task)

                if result.success:
                    return result

                # Task failed, attempt recovery if not on last attempt
                if attempt < task.max_retries:
                    recovery_result = await self._attempt_error_recovery(task, result.error or "Unknown error")
                    if recovery_result.retry_recommended:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                        continue

                return result  # Return the failed result

            except Exception as e:
                last_error = e
                self.logger.error(f"Task execution attempt {attempt + 1} failed: {str(e)}")

                if attempt < task.max_retries:
                    # Attempt recovery
                    recovery_result = await self._attempt_error_recovery(task, str(e))
                    if recovery_result.retry_recommended:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                        continue

        # All retries exhausted
        raise Exception(f"Task failed after {task.max_retries + 1} attempts. Last error: {last_error}")

    async def _pre_execution_validation(self, task: Task) -> ValidationResult:
        """
        Validate prerequisites before task execution.
        Override in subclasses for specific validation logic.
        """
        checks = []

        # Basic validation
        if not task.title.strip():
            checks.append(ValidationResult.failure("Task title cannot be empty"))

        if task.type not in [t.value for t in task.type.__class__]:
            checks.append(ValidationResult.failure(f"Invalid task type: {task.type}"))

        # Agent-specific validation (override in subclasses)
        agent_validation = await self._validate_agent_specific_prerequisites(task)
        checks.append(agent_validation)

        return ValidationResult.from_checks(checks)

    async def _post_execution_validation(self, task: Task, result: TaskResult) -> ValidationResult:
        """
        Validate task completion and results.
        Override in subclasses for specific validation logic.
        """
        checks = []

        # Basic result validation
        if not result.success and not result.error:
            checks.append(ValidationResult.failure("Failed task must have error message"))

        # File existence validation
        if task.expected_files:
            file_validation = await self._validate_expected_files(task.expected_files)
            checks.append(file_validation)

        # Agent-specific validation (override in subclasses)
        agent_validation = await self._validate_agent_specific_completion(task, result)
        checks.append(agent_validation)

        return ValidationResult.from_checks(checks)

    async def _attempt_error_recovery(self, task: Task, error: str) -> RecoveryResult:
        """
        Attempt to recover from task execution error.
        Override in subclasses for specific recovery logic.
        """
        error_type = self._classify_error(error)

        self.logger.info(f"Attempting error recovery for: {error_type}")

        # Basic recovery strategies
        if error_type == ErrorType.DEPENDENCY_ERROR:
            return await self._recover_dependency_error(task, error)
        elif error_type == ErrorType.CONFIGURATION_ERROR:
            return await self._recover_configuration_error(task, error)
        elif error_type == ErrorType.SYNTAX_ERROR:
            return await self._recover_syntax_error(task, error)
        else:
            # Generic recovery
            return RecoveryResult(
                success=False,
                actions_taken="No specific recovery strategy available",
                retry_recommended=True,
                recovery_suggestions=[
                    "Review task parameters",
                    "Check system resources",
                    "Verify network connectivity"
                ]
            )

    def _classify_error(self, error: str) -> ErrorType:
        """Classify error type based on error message"""
        error_lower = error.lower()

        if any(keyword in error_lower for keyword in ['syntax', 'invalid syntax', 'parsing']):
            return ErrorType.SYNTAX_ERROR
        elif any(keyword in error_lower for keyword in ['import', 'module', 'cannot import']):
            return ErrorType.IMPORT_ERROR
        elif any(keyword in error_lower for keyword in ['config', 'configuration', 'missing']):
            return ErrorType.CONFIGURATION_ERROR
        elif any(keyword in error_lower for keyword in ['dependency', 'package', 'not found']):
            return ErrorType.DEPENDENCY_ERROR
        elif any(keyword in error_lower for keyword in ['network', 'connection', 'timeout']):
            return ErrorType.NETWORK_ERROR
        elif any(keyword in error_lower for keyword in ['database', 'sql', 'connection']):
            return ErrorType.DATABASE_ERROR
        elif any(keyword in error_lower for keyword in ['permission', 'access', 'denied']):
            return ErrorType.PERMISSION_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR

    async def _validate_expected_files(self, expected_files: List[str]) -> ValidationResult:
        """Validate that expected files exist"""
        import os

        missing_files = []
        for file_path in expected_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if missing_files:
            return ValidationResult.failure(f"Missing expected files: {', '.join(missing_files)}")

        return ValidationResult.success(f"All {len(expected_files)} expected files exist")

    def _record_task_execution(self, task: Task, result: TaskResult, validation: ValidationResult):
        """Record task execution for analysis and debugging"""
        execution_record = {
            "task_id": task.id,
            "task_title": task.title,
            "task_type": task.type,
            "agent_type": self.agent_type,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "duration_seconds": ((task.completed_at - task.started_at).total_seconds()
                               if task.started_at and task.completed_at else None),
            "success": result.success,
            "retry_count": task.retry_count,
            "validation_passed": validation.is_valid,
            "error": result.error,
            "validation_error": validation.error
        }

        self._task_history.append(execution_record)

        # Keep only last 100 records
        if len(self._task_history) > 100:
            self._task_history = self._task_history[-100:]

    # Abstract methods to be implemented by subclasses
    @abstractmethod
    async def _execute_core_task(self, task: Task) -> TaskResult:
        """
        Core task execution logic - must be implemented by subclasses.

        Args:
            task: The task to execute

        Returns:
            TaskResult: Result of task execution
        """
        pass

    @abstractmethod
    async def _validate_agent_specific_prerequisites(self, task: Task) -> ValidationResult:
        """Agent-specific prerequisite validation"""
        pass

    @abstractmethod
    async def _validate_agent_specific_completion(self, task: Task, result: TaskResult) -> ValidationResult:
        """Agent-specific completion validation"""
        pass

    # Recovery methods (can be overridden by subclasses)
    async def _recover_dependency_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from dependency-related errors"""
        return RecoveryResult(
            success=False,
            actions_taken="Dependency error recovery not implemented for this agent",
            retry_recommended=False
        )

    async def _recover_configuration_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from configuration-related errors"""
        return RecoveryResult(
            success=False,
            actions_taken="Configuration error recovery not implemented for this agent",
            retry_recommended=False
        )

    async def _recover_syntax_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from syntax-related errors"""
        return RecoveryResult(
            success=False,
            actions_taken="Syntax error recovery not implemented for this agent",
            retry_recommended=False
        )

    # Status and monitoring methods
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "is_active": self.is_active,
            "current_task_id": self.current_task_id,
            "success_count": self._success_count,
            "error_count": self._error_count,
            "success_rate": self.success_rate,
            "task_history_count": len(self._task_history)
        }

    async def get_task_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent task execution history"""
        return self._task_history[-limit:] if self._task_history else []