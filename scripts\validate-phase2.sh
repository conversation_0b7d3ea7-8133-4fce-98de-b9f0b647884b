#!/bin/bash
# Phase 2 Validation Script - Test Workspace Management
# This script validates that workspace management endpoints are working

echo "🚀 Phase 2 Multi-Tenant Workspace Management Validation"
echo "======================================================="

# Function to test API endpoint
test_api() {
    local method=$1
    local url=$2
    local name=$3
    local data=$4
    local headers=$5

    echo -n "Testing $name... "

    if [ -n "$data" ]; then
        if [ -n "$headers" ]; then
            response=$(curl -s -X "$method" -H "$headers" -H "Content-Type: application/json" -d "$data" "$url")
        else
            response=$(curl -s -X "$method" -H "Content-Type: application/json" -d "$data" "$url")
        fi
    else
        if [ -n "$headers" ]; then
            response=$(curl -s -X "$method" -H "$headers" "$url")
        else
            response=$(curl -s -X "$method" "$url")
        fi
    fi

    # Check if response contains error
    if echo "$response" | grep -q '"detail":\|"error":\|"status":"unhealthy"'; then
        echo "❌ Failed"
        echo "   Response: $response"
        return 1
    else
        echo "✅ Success"
        echo "   Response: $response"
        return 0
    fi
}

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

echo ""
echo "🔍 Testing Workspace Management Endpoints:"
echo "------------------------------------------"

# Test workspace health check (no auth required)
test_api "GET" "http://api.localhost/api/v1/workspaces/health" "Workspace Health Check"

echo ""
echo "🔐 Authentication Note:"
echo "----------------------"
echo "The following tests require authentication. In a real scenario, you would:"
echo "1. Login through the User Portal at http://portal.localhost"
echo "2. Get a JWT token from Supabase Auth"
echo "3. Use that token in the Authorization header"
echo ""
echo "For testing purposes, we're showing what the endpoints would return:"

# Test workspace endpoints (these will return 401 without proper auth, which is expected)
echo ""
echo "📊 Expected Endpoint Responses (without auth - will show 401):"
echo "------------------------------------------------------------"

test_api "GET" "http://api.localhost/api/v1/workspaces/me" "Get My Workspace (requires auth)"

test_api "POST" "http://api.localhost/api/v1/workspaces/start" "Start Workspace (requires auth)" '{"workspace_config": null}'

test_api "POST" "http://api.localhost/api/v1/workspaces/stop" "Stop Workspace (requires auth)"

echo ""
echo "🐳 Docker Service Status:"
echo "-------------------------"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(traefik|ai-orchestrator|user-portal)"

echo ""
echo "📚 Available API Endpoints:"
echo "---------------------------"
echo "• Workspace Health: http://api.localhost/api/v1/workspaces/health"
echo "• Get My Workspace: GET http://api.localhost/api/v1/workspaces/me"
echo "• Start Workspace: POST http://api.localhost/api/v1/workspaces/start"
echo "• Stop Workspace: POST http://api.localhost/api/v1/workspaces/stop"
echo "• Delete Workspace: DELETE http://api.localhost/api/v1/workspaces/delete"
echo ""

echo "🎯 Phase 2 Validation Complete!"
echo "Next: Execute Phase 3 to create the User Portal UI"