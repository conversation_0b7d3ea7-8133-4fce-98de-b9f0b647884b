# Comprehensive .dockerignore for AI Orchestrator
# Reduces build context size and prevents sensitive files from being copied

# Version control
.git
.gitignore
.gitattributes
.gitmodules

# Environment files (sensitive)
.env
.env.*
!.env.example

# Python cache and build artifacts
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test coverage
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Documentation
docs/
*.md
!README.md

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*.yml
.dockerignore

# Secrets and certificates
secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Backup files
*.bak
*.backup
*.orig

# Database files
*.db
*.sqlite
*.sqlite3

# Application specific
data/
uploads/
static/
media/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# JetBrains
.idea/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/