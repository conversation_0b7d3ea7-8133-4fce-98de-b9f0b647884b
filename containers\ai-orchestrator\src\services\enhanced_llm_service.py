# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: Enhanced universal LLM service with monitoring, validation, and optimization

import os
import json
import time
import asyncio
import hashlib
import logging
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
from datetime import datetime, timedelta
from collections import defaultdict, deque

import aiohttp

# LLM Provider Clients
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    openai = None

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    anthropic = None

try:
    from prometheus_client import Counter, Histogram, Gauge  # type: ignore[import-untyped]
    PROMETHEUS_AVAILABLE = True
except ImportError:
    # Fallback when prometheus_client is not available (development environments)
    PROMETHEUS_AVAILABLE = False

    # Mock prometheus metrics for development
    class MockMetric:
        def labels(self, *args, **kwargs):
            return self

        def inc(self, amount=1):
            pass

        def dec(self, amount=1):
            pass

        def observe(self, amount):
            pass

        def set(self, value):
            pass

    Counter = Histogram = Gauge = lambda *args, **kwargs: MockMetric()

import redis.asyncio as redis  # type: ignore[import-untyped]

from ..models.llm_models import (
    LLMProvider, GenerateRequest, LLMResponse, LLMModel, ProviderStatus,
    UsageStats, ModelPullRequest, ModelPullResponse, HealthCheckResponse,
    RateLimitInfo, LLMError, ProviderUnavailableError, InvalidAPIKeyError,
    RateLimitExceededError, ModelNotFoundError, GenerationError
)

# Prometheus metrics (with optional support)
if PROMETHEUS_AVAILABLE:
    llm_requests_total = Counter('llm_requests_total', 'Total LLM requests', ['provider', 'model', 'status'])
    llm_request_duration = Histogram('llm_request_duration_seconds', 'LLM request duration', ['provider', 'model'])
    llm_tokens_total = Counter('llm_tokens_total', 'Total tokens processed', ['provider', 'model', 'type'])
    llm_cost_total = Counter('llm_cost_usd_total', 'Total cost in USD', ['provider'])
    llm_active_requests = Gauge('llm_active_requests', 'Active LLM requests', ['provider'])
else:
    # Mock metrics for development environments
    llm_requests_total = MockMetric()
    llm_request_duration = MockMetric()
    llm_tokens_total = MockMetric()
    llm_cost_total = MockMetric()
    llm_active_requests = MockMetric()

logger = logging.getLogger(__name__)

if not PROMETHEUS_AVAILABLE:
    logger.warning("Prometheus client not available - metrics collection disabled")


class EnhancedLLMService:
    """
    Enhanced universal LLM service with comprehensive features:
    - API key validation and management
    - Resource monitoring and optimization
    - Rate limiting and cost control
    - Fallback and retry mechanisms
    - Comprehensive error handling
    """

    def __init__(self, redis_client: Optional[redis.Redis] = None):
        """Initialize the enhanced LLM service.

        Args:
            redis_client: Optional Redis client for caching and rate limiting
        """
        # Provider configuration
        self.providers = self._load_provider_config()
        self.redis_client = redis_client

        # Rate limiting and cost control
        self.rate_limits = self._load_rate_limits()
        self.cost_limits = self._load_cost_limits()

        # Monitoring
        self.request_stats = defaultdict(lambda: defaultdict(int))
        self.response_times = defaultdict(lambda: deque(maxlen=100))
        self.error_counts = defaultdict(int)

        # Circuit breaker pattern
        self.circuit_breakers = defaultdict(lambda: {
            'failure_count': 0,
            'last_failure_time': 0.0,
            'state': 'closed'  # closed, open, half-open
        })

        # Model information cache
        self.model_cache = {}
        self.model_cache_expiry = {}

        # Validate provider configurations at startup
        self._validate_provider_configs()

    def _validate_provider_configs(self) -> None:
        """
        Validate provider configurations for logical consistency and completeness.

        This method performs comprehensive validation of all LLM provider configurations
        to ensure they have the required fields and settings for proper operation.
        Issues are logged as warnings but do not prevent service startup.
        """
        logger.info("Validating LLM provider configurations...")

        # Define provider types and their requirements
        cloud_providers = {
            LLMProvider.OPENAI: "OpenAI",
            LLMProvider.ANTHROPIC: "Anthropic",
            LLMProvider.OPENROUTER: "OpenRouter"
        }

        local_providers = {
            LLMProvider.OLLAMA: "Ollama"
        }

        validation_errors = []
        validation_warnings = []
        valid_providers = []

        # Validate each configured provider
        for provider_key, config in self.providers.items():
            provider_name = provider_key.value if hasattr(provider_key, 'value') else str(provider_key)

            try:
                # Basic configuration validation
                if not isinstance(config, dict):
                    validation_errors.append(f"Provider '{provider_name}': Configuration is not a dictionary")
                    continue

                # Check if provider is enabled
                is_enabled = config.get('enabled', False)
                if not is_enabled:
                    logger.debug(f"Provider '{provider_name}': Disabled, skipping validation")
                    continue

                # Validate cloud providers
                if provider_key in cloud_providers:
                    self._validate_cloud_provider(provider_key, provider_name, config, validation_errors, validation_warnings)

                # Validate local providers
                elif provider_key in local_providers:
                    self._validate_local_provider(provider_key, provider_name, config, validation_errors, validation_warnings)

                else:
                    validation_warnings.append(f"Provider '{provider_name}': Unknown provider type, skipping specific validation")

                # Common validation for all providers
                self._validate_common_config(provider_name, config, validation_warnings)

                # If we get here without errors, provider is valid
                valid_providers.append(provider_name)

            except Exception as e:
                validation_errors.append(f"Provider '{provider_name}': Unexpected validation error - {str(e)}")

        # Report validation results
        self._report_validation_results(valid_providers, validation_warnings, validation_errors)

    def _validate_cloud_provider(self, provider_key: LLMProvider, provider_name: str,
                                config: Dict[str, Any], errors: List[str], warnings: List[str]) -> None:
        """Validate cloud provider specific requirements."""

        # API key is required for cloud providers
        api_key = config.get('api_key')
        if not api_key:
            errors.append(
                f"Provider '{provider_name}': Missing required API key. "
                f"Set the appropriate environment variable (e.g., {provider_name.upper()}_API_KEY)"
            )
        elif not isinstance(api_key, str) or len(api_key.strip()) < 10:
            warnings.append(
                f"Provider '{provider_name}': API key appears to be invalid or too short"
            )

        # Base URL should be present and valid
        base_url = config.get('base_url')
        if not base_url:
            warnings.append(f"Provider '{provider_name}': Missing base_url, using default")
        elif not isinstance(base_url, str) or not base_url.startswith(('http://', 'https://')):
            warnings.append(f"Provider '{provider_name}': base_url '{base_url}' may be invalid")

        # Validate timeout settings
        timeout = config.get('timeout', 60)
        if not isinstance(timeout, int) or timeout <= 0:
            warnings.append(f"Provider '{provider_name}': Invalid timeout value '{timeout}', should be positive integer")
        elif timeout > 300:  # 5 minutes
            warnings.append(f"Provider '{provider_name}': Timeout '{timeout}s' is very high, may cause performance issues")

    def _validate_local_provider(self, provider_key: LLMProvider, provider_name: str,
                                config: Dict[str, Any], errors: List[str], warnings: List[str]) -> None:
        """Validate local provider specific requirements."""

        # Base URL is required for local providers
        base_url = config.get('base_url')
        if not base_url:
            errors.append(
                f"Provider '{provider_name}': Missing required base_url. "
                f"Set the {provider_name.upper()}_BASE_URL environment variable"
            )
        elif not isinstance(base_url, str):
            errors.append(f"Provider '{provider_name}': base_url must be a string")
        elif not base_url.startswith(('http://', 'https://')):
            warnings.append(f"Provider '{provider_name}': base_url '{base_url}' should start with http:// or https://")

        # API key should be None or not required for local providers
        api_key = config.get('api_key')
        if api_key is not None:
            warnings.append(f"Provider '{provider_name}': API key is set but not typically required for local providers")

        # Validate timeout for local providers (can be higher)
        timeout = config.get('timeout', 300)
        if not isinstance(timeout, int) or timeout <= 0:
            warnings.append(f"Provider '{provider_name}': Invalid timeout value '{timeout}', should be positive integer")

    def _validate_common_config(self, provider_name: str, config: Dict[str, Any], warnings: List[str]) -> None:
        """Validate common configuration fields for all providers."""

        # Validate max_retries
        max_retries = config.get('max_retries', 3)
        if not isinstance(max_retries, int) or max_retries < 0:
            warnings.append(f"Provider '{provider_name}': Invalid max_retries '{max_retries}', should be non-negative integer")
        elif max_retries > 10:
            warnings.append(f"Provider '{provider_name}': max_retries '{max_retries}' is very high")

        # Validate default_model
        default_model = config.get('default_model')
        if default_model and not isinstance(default_model, str):
            warnings.append(f"Provider '{provider_name}': default_model should be a string")
        elif not default_model:
            warnings.append(f"Provider '{provider_name}': No default_model specified")

    def _report_validation_results(self, valid_providers: List[str], warnings: List[str], errors: List[str]) -> None:
        """Report the results of provider configuration validation."""

        # Log all warnings
        for warning in warnings:
            logger.warning(f"LLM Configuration Warning: {warning}")

        # Log all errors
        for error in errors:
            logger.warning(f"LLM Configuration Error: {error}")

        # Summary report
        total_providers = len(self.providers)
        enabled_providers = sum(1 for config in self.providers.values() if config.get('enabled', False))

        if errors:
            logger.warning(
                f"LLM Provider Validation Summary: {len(errors)} error(s), {len(warnings)} warning(s) found. "
                f"Some providers may not function correctly."
            )
        elif warnings:
            logger.info(
                f"LLM Provider Validation Summary: {len(warnings)} warning(s) found. "
                f"All providers should function but may have suboptimal configuration."
            )
        else:
            logger.info("LLM Provider Validation Summary: All configurations are valid.")

        logger.info(
            f"LLM Provider Status: {len(valid_providers)} valid, {enabled_providers} enabled, "
            f"{total_providers} total configured providers"
        )

        if valid_providers:
            logger.info(f"Valid providers: {', '.join(valid_providers)}")
        else:
            logger.warning("No valid LLM providers found! Service may not function correctly.")

    def _load_provider_config(self) -> Dict[str, Dict[str, Any]]:
        """Load provider configuration from environment variables.

        Returns:
            Dictionary containing provider configurations
        """
        return {
            LLMProvider.OLLAMA: {
                'base_url': os.getenv('OLLAMA_BASE_URL', 'http://host.docker.internal:11434'),
                'api_key': None,  # Ollama doesn't use API keys
                'enabled': True,
                'timeout': int(os.getenv('OLLAMA_TIMEOUT', '300')),
                'max_retries': int(os.getenv('OLLAMA_MAX_RETRIES', '3')),
                'default_model': os.getenv('OLLAMA_DEFAULT_MODEL', 'llama3.2'),
            },
            LLMProvider.OPENROUTER: {
                'base_url': 'https://openrouter.ai/api/v1',
                'api_key': os.getenv('OPENROUTER_API_KEY'),
                'enabled': bool(os.getenv('OPENROUTER_API_KEY')),
                'timeout': int(os.getenv('OPENROUTER_TIMEOUT', '60')),
                'max_retries': int(os.getenv('OPENROUTER_MAX_RETRIES', '3')),
                'default_model': os.getenv('OPENROUTER_DEFAULT_MODEL', 'meta-llama/llama-3.1-8b-instruct:free'),
            },
            LLMProvider.OPENAI: {
                'base_url': 'https://api.openai.com/v1',
                'api_key': os.getenv('OPENAI_API_KEY'),
                'enabled': bool(os.getenv('OPENAI_API_KEY')),
                'timeout': int(os.getenv('OPENAI_TIMEOUT', '60')),
                'max_retries': int(os.getenv('OPENAI_MAX_RETRIES', '3')),
                'default_model': os.getenv('OPENAI_DEFAULT_MODEL', 'gpt-4o-mini'),
            },
            LLMProvider.ANTHROPIC: {
                'base_url': 'https://api.anthropic.com/v1',
                'api_key': os.getenv('ANTHROPIC_API_KEY'),
                'enabled': bool(os.getenv('ANTHROPIC_API_KEY')),
                'timeout': int(os.getenv('ANTHROPIC_TIMEOUT', '60')),
                'max_retries': int(os.getenv('ANTHROPIC_MAX_RETRIES', '3')),
                'default_model': os.getenv('ANTHROPIC_DEFAULT_MODEL', 'claude-3-haiku-20240307'),
            }
        }

    def _load_rate_limits(self) -> Dict[str, Dict[str, int]]:
        """Load rate limiting configuration.

        Returns:
            Dictionary containing rate limits per provider
        """
        return {
            LLMProvider.OLLAMA: {
                'requests_per_minute': int(os.getenv('OLLAMA_RATE_LIMIT_RPM', '60')),
                'requests_per_hour': int(os.getenv('OLLAMA_RATE_LIMIT_RPH', '1000')),
            },
            LLMProvider.OPENROUTER: {
                'requests_per_minute': int(os.getenv('OPENROUTER_RATE_LIMIT_RPM', '20')),
                'requests_per_hour': int(os.getenv('OPENROUTER_RATE_LIMIT_RPH', '200')),
            },
            LLMProvider.OPENAI: {
                'requests_per_minute': int(os.getenv('OPENAI_RATE_LIMIT_RPM', '50')),
                'requests_per_hour': int(os.getenv('OPENAI_RATE_LIMIT_RPH', '1000')),
            },
            LLMProvider.ANTHROPIC: {
                'requests_per_minute': int(os.getenv('ANTHROPIC_RATE_LIMIT_RPM', '50')),
                'requests_per_hour': int(os.getenv('ANTHROPIC_RATE_LIMIT_RPH', '1000')),
            }
        }

    def _load_cost_limits(self) -> Dict[str, float]:
        """Load cost limiting configuration.

        Returns:
            Dictionary containing cost limits per provider in USD
        """
        return {
            LLMProvider.OPENROUTER: float(os.getenv('OPENROUTER_COST_LIMIT_USD', '10.0')),
            LLMProvider.OPENAI: float(os.getenv('OPENAI_COST_LIMIT_USD', '20.0')),
            LLMProvider.ANTHROPIC: float(os.getenv('ANTHROPIC_COST_LIMIT_USD', '15.0')),
        }

    async def validate_api_key(self, provider: LLMProvider) -> bool:
        """Validate API key for a given provider.

        Args:
            provider: LLM provider to validate

        Returns:
            True if API key is valid, False otherwise
        """
        config = self.providers.get(provider)
        if not config or not config.get('api_key'):
            return provider == LLMProvider.OLLAMA  # Ollama doesn't need API key

        try:
            async with aiohttp.ClientSession() as session:
                headers = self._get_headers(provider)

                if provider == LLMProvider.OPENROUTER:
                    url = f"{config['base_url']}/models"
                elif provider == LLMProvider.OPENAI:
                    url = f"{config['base_url']}/models"
                elif provider == LLMProvider.ANTHROPIC:
                    url = f"{config['base_url']}/messages"
                    # For Anthropic, we'll send a minimal request to test auth
                    data = {
                        "model": "claude-3-haiku-20240307",
                        "max_tokens": 1,
                        "messages": [{"role": "user", "content": "test"}]
                    }
                    async with session.post(url, headers=headers, json=data, timeout=10) as response:
                        return response.status in [200, 400]  # 400 is ok, means auth worked
                else:
                    return False

                async with session.get(url, headers=headers, timeout=10) as response:
                    return response.status == 200

        except Exception as e:
            logger.error(f"API key validation failed for {provider}: {str(e)}")
            return False

    def _get_headers(self, provider: LLMProvider) -> Dict[str, str]:
        """Get headers for API requests.

        Args:
            provider: LLM provider

        Returns:
            Dictionary containing request headers
        """
        config = self.providers.get(provider, {})
        api_key = config.get('api_key')

        headers = {'Content-Type': 'application/json'}

        if provider in [LLMProvider.OPENROUTER, LLMProvider.OPENAI]:
            headers['Authorization'] = f'Bearer {api_key}'
        elif provider == LLMProvider.ANTHROPIC:
            if api_key:
                headers['x-api-key'] = str(api_key)
            headers['anthropic-version'] = '2023-06-01'

        return headers

    async def check_rate_limit(self, provider: LLMProvider, user_id: str = "default") -> RateLimitInfo:
        """Check rate limit status for a provider and user.

        Args:
            provider: LLM provider
            user_id: User identifier for rate limiting

        Returns:
            RateLimitInfo object with current status
        """
        if not self.redis_client:
            # If no Redis, allow all requests (fallback behavior)
            return RateLimitInfo(
                requests_per_minute=999999,
                requests_remaining=999999,
                reset_time=time.time() + 60
            )

        limits = self.rate_limits.get(provider, {})
        rpm_limit = limits.get('requests_per_minute', 60)

        # Create Redis keys
        minute_key = f"ratelimit:{provider}:{user_id}:minute:{int(time.time() // 60)}"

        # Check current usage
        current_minute = await self.redis_client.get(minute_key)
        current_minute = int(current_minute) if current_minute else 0

        remaining = max(0, rpm_limit - current_minute)
        reset_time = (int(time.time() // 60) + 1) * 60

        # Check cost limits for cloud providers
        cost_used = 0.0
        cost_limit = self.cost_limits.get(provider)
        if cost_limit:
            cost_key = f"cost:{provider}:{user_id}:daily:{int(time.time() // 86400)}"
            cost_used_str = await self.redis_client.get(cost_key)
            cost_used = float(cost_used_str) if cost_used_str else 0.0

        return RateLimitInfo(
            requests_per_minute=rpm_limit,
            requests_remaining=remaining,
            reset_time=reset_time,
            cost_limit_usd=cost_limit,
            cost_used_usd=cost_used if cost_limit else None
        )

    async def increment_rate_limit(self, provider: LLMProvider, user_id: str = "default",
                                 cost: float = 0.0) -> None:
        """Increment rate limit counters.

        Args:
            provider: LLM provider
            user_id: User identifier
            cost: Cost of the request in USD
        """
        if not self.redis_client:
            return

        # Increment request counter
        minute_key = f"ratelimit:{provider}:{user_id}:minute:{int(time.time() // 60)}"
        await self.redis_client.incr(minute_key)
        await self.redis_client.expire(minute_key, 120)  # Keep for 2 minutes

        # Increment cost counter for cloud providers
        if cost > 0 and provider in self.cost_limits:
            cost_key = f"cost:{provider}:{user_id}:daily:{int(time.time() // 86400)}"
            await self.redis_client.incrbyfloat(cost_key, cost)
            await self.redis_client.expire(cost_key, 86400 * 2)  # Keep for 2 days

    async def test_provider_connection(self, provider: LLMProvider) -> ProviderStatus:
        """Test connection to a specific provider.

        Args:
            provider: LLM provider to test

        Returns:
            ProviderStatus object with connection information
        """
        start_time = time.time()
        config = self.providers.get(provider)

        if not config or not config.get('enabled'):
            return ProviderStatus(
                provider=provider,
                available=False,
                error_message="Provider not configured or disabled",
                api_key_configured=bool(config and config.get('api_key')) if provider != LLMProvider.OLLAMA else True
            )

        try:
            if provider == LLMProvider.OLLAMA:
                return await self._test_ollama_connection(config, start_time)
            else:
                return await self._test_cloud_provider_connection(provider, config, start_time)

        except Exception as e:
            return ProviderStatus(
                provider=provider,
                available=False,
                error_message=str(e),
                response_time_ms=int((time.time() - start_time) * 1000)
            )

    async def _test_ollama_connection(self, config: Dict[str, Any], start_time: float) -> ProviderStatus:
        """Test Ollama connection specifically."""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{config['base_url']}/api/tags", timeout=10) as response:
                response_time_ms = int((time.time() - start_time) * 1000)

                if response.status == 200:
                    data = await response.json()
                    models_count = len(data.get('models', []))
                    return ProviderStatus(
                        provider=LLMProvider.OLLAMA,
                        available=True,
                        api_key_configured=True,
                        response_time_ms=response_time_ms
                    )
                else:
                    return ProviderStatus(
                        provider=LLMProvider.OLLAMA,
                        available=False,
                        error_message=f"HTTP {response.status}",
                        response_time_ms=response_time_ms
                    )

    async def _test_cloud_provider_connection(self, provider: LLMProvider,
                                            config: Dict[str, Any], start_time: float) -> ProviderStatus:
        """Test cloud provider connection."""
        if not config.get('api_key'):
            return ProviderStatus(
                provider=provider,
                available=False,
                error_message="API key not configured",
                api_key_configured=False
            )

        # Validate API key
        is_valid = await self.validate_api_key(provider)
        response_time_ms = int((time.time() - start_time) * 1000)

        return ProviderStatus(
            provider=provider,
            available=is_valid,
            error_message=None if is_valid else "Invalid API key",
            api_key_configured=True,
            response_time_ms=response_time_ms
        )

    async def list_available_models(self, provider: Optional[LLMProvider] = None) -> List[LLMModel]:
        """List all available models from providers.

        Args:
            provider: Optional specific provider to query

        Returns:
            List of available models
        """
        models = []
        providers_to_check = [provider] if provider else list(LLMProvider)

        for prov in providers_to_check:
            try:
                if prov == LLMProvider.OLLAMA:
                    ollama_models = await self._list_ollama_models()
                    models.extend(ollama_models)
                else:
                    cloud_models = await self._list_cloud_models(prov)
                    models.extend(cloud_models)
            except Exception as e:
                logger.error(f"Failed to list models for {prov}: {str(e)}")

        return models

    async def _list_ollama_models(self) -> List[LLMModel]:
        """List Ollama models."""
        config = self.providers.get(LLMProvider.OLLAMA)
        if not config or not config.get('enabled'):
            return []

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{config['base_url']}/api/tags",
                                     timeout=config.get('timeout', 10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = []
                        for model_info in data.get('models', []):
                            models.append(LLMModel(
                                name=model_info.get('name', 'unknown'),
                                provider=LLMProvider.OLLAMA,
                                size=model_info.get('size'),
                                modified_at=model_info.get('modified_at'),
                                context_length=model_info.get('details', {}).get('parameter_size')
                            ))
                        return models
        except Exception as e:
            logger.error(f"Error listing Ollama models: {str(e)}")

        return []

    async def _list_cloud_models(self, provider: LLMProvider) -> List[LLMModel]:
        """List cloud provider models."""
        config = self.providers.get(provider)
        if not config or not config.get('enabled') or not config.get('api_key'):
            return []

        # For now, return hardcoded popular models
        # In a production system, you'd query the provider APIs
        model_definitions = {
            LLMProvider.OPENROUTER: [
                LLMModel(name="meta-llama/llama-3.1-8b-instruct:free", provider=provider, context_length=8192, cost_per_token=0.0),
                LLMModel(name="meta-llama/llama-3.1-70b-instruct:nitro", provider=provider, context_length=8192, cost_per_token=0.0009),
                LLMModel(name="anthropic/claude-3.5-sonnet", provider=provider, context_length=200000, cost_per_token=0.003),
            ],
            LLMProvider.OPENAI: [
                LLMModel(name="gpt-4o", provider=provider, context_length=128000, cost_per_token=0.005),
                LLMModel(name="gpt-4o-mini", provider=provider, context_length=128000, cost_per_token=0.00015),
                LLMModel(name="gpt-3.5-turbo", provider=provider, context_length=16385, cost_per_token=0.0005),
            ],
            LLMProvider.ANTHROPIC: [
                LLMModel(name="claude-3-5-sonnet-20241022", provider=provider, context_length=200000, cost_per_token=0.003),
                LLMModel(name="claude-3-haiku-20240307", provider=provider, context_length=200000, cost_per_token=0.00025),
            ]
        }

        return model_definitions.get(provider, [])

    async def generate(self, request: GenerateRequest, user_id: str = "default") -> LLMResponse:
        """Generate text using the specified or best available provider.

        Args:
            request: Generation request parameters
            user_id: User identifier for rate limiting

        Returns:
            LLMResponse with generated content

        Raises:
            RateLimitExceededError: If rate limit is exceeded
            ProviderUnavailableError: If all providers are unavailable
            GenerationError: If generation fails
        """
        # Determine provider and model
        provider = request.provider or LLMProvider(os.getenv('DEFAULT_LOCAL_PROVIDER', 'ollama'))
        model = request.model or self.providers[provider]['default_model']

        # Check rate limits
        rate_limit = await self.check_rate_limit(provider, user_id)
        if rate_limit.requests_remaining <= 0:
            raise RateLimitExceededError(
                f"Rate limit exceeded for {provider}. Reset at {rate_limit.reset_time}",
                provider=provider
            )

        # Check cost limits for cloud providers
        if rate_limit.cost_limit_usd and rate_limit.cost_used_usd:
            if rate_limit.cost_used_usd >= rate_limit.cost_limit_usd:
                raise RateLimitExceededError(
                    f"Cost limit exceeded for {provider}. Used: ${rate_limit.cost_used_usd:.4f}",
                    provider=provider
                )

        start_time = time.time()
        request_id = hashlib.md5(f"{user_id}:{start_time}:{request.prompt[:100]}".encode()).hexdigest()

        try:
            # Record active request
            llm_active_requests.labels(provider=provider).inc()

            # Generate response
            if provider == LLMProvider.OLLAMA:
                response = await self._generate_ollama(request, model, request_id)
            elif provider == LLMProvider.OPENROUTER:
                response = await self._generate_openrouter(request, model, request_id)
            elif provider == LLMProvider.OPENAI:
                response = await self._generate_openai(request, model, request_id)
            elif provider == LLMProvider.ANTHROPIC:
                response = await self._generate_anthropic(request, model, request_id)
            else:
                raise ProviderUnavailableError(f"Unsupported provider: {provider}", provider=provider)

            # Record metrics
            duration = time.time() - start_time
            llm_request_duration.labels(provider=provider, model=model).observe(duration)
            llm_requests_total.labels(provider=provider, model=model, status='success').inc()

            # Update usage stats
            response.usage.duration_ms = int(duration * 1000)

            # Record tokens and cost
            llm_tokens_total.labels(provider=provider, model=model, type='prompt').inc(response.usage.prompt_tokens)
            llm_tokens_total.labels(provider=provider, model=model, type='completion').inc(response.usage.completion_tokens)

            if response.usage.cost and response.usage.cost > 0:
                # Convert to cents to avoid float issues with prometheus counters
                cost_cents = int(response.usage.cost * 100)
                llm_cost_total.labels(provider=provider).inc(cost_cents)
                await self.increment_rate_limit(provider, user_id, response.usage.cost)
            else:
                await self.increment_rate_limit(provider, user_id)

            return response

        except Exception as e:
            # Record error metrics
            duration = time.time() - start_time
            llm_request_duration.labels(provider=provider, model=model).observe(duration)
            llm_requests_total.labels(provider=provider, model=model, status='error').inc()

            # Update circuit breaker
            self._update_circuit_breaker(provider, success=False)

            # Try fallback if enabled
            fallback_enabled = os.getenv('ENABLE_CLOUD_FALLBACK', 'true').lower() == 'true'
            if fallback_enabled and provider == LLMProvider.OLLAMA:
                logger.warning(f"Ollama failed, attempting cloud fallback: {str(e)}")
                try:
                    fallback_request = GenerateRequest(
                        prompt=request.prompt,
                        provider=LLMProvider.OPENROUTER,
                        system_prompt=request.system_prompt,
                        temperature=request.temperature,
                        max_tokens=request.max_tokens
                    )
                    return await self.generate(fallback_request, user_id)
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {str(fallback_error)}")

            raise GenerationError(f"Generation failed: {str(e)}", provider=provider) from e

        finally:
            llm_active_requests.labels(provider=provider).dec()

    async def _generate_ollama(self, request: GenerateRequest, model: str, request_id: str) -> LLMResponse:
        """Generate response using Ollama."""
        config = self.providers[LLMProvider.OLLAMA]

        messages = []
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})
        messages.append({"role": "user", "content": request.prompt})

        payload = {
            "model": model,
            "messages": messages,
            "stream": request.stream,
            "options": {
                "temperature": request.temperature
            }
        }

        if request.max_tokens:
            payload["options"]["num_predict"] = request.max_tokens
        if request.top_p is not None:
            payload["options"]["top_p"] = request.top_p

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{config['base_url']}/api/chat",
                json=payload,
                timeout=config.get('timeout', 300)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    content = data.get("message", {}).get("content", "")

                    usage = UsageStats(
                        prompt_eval_count=data.get("prompt_eval_count", 0),
                        eval_count=data.get("eval_count", 0),
                        total_duration=data.get("total_duration", 0),
                        prompt_tokens=data.get("prompt_eval_count", 0),
                        completion_tokens=data.get("eval_count", 0),
                        total_tokens=data.get("prompt_eval_count", 0) + data.get("eval_count", 0)
                    )

                    return LLMResponse(
                        content=content,
                        model=model,
                        provider=LLMProvider.OLLAMA,
                        usage=usage,
                        request_id=request_id,
                        finish_reason=data.get("done_reason", "stop")
                    )
                else:
                    error_text = await response.text()
                    raise GenerationError(f"Ollama API error: {response.status} - {error_text}")

    async def _generate_openrouter(self, request: GenerateRequest, model: str, request_id: str) -> LLMResponse:
        """Generate response using OpenRouter."""
        config = self.providers[LLMProvider.OPENROUTER]

        messages = []
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})
        messages.append({"role": "user", "content": request.prompt})

        payload = {
            "model": model,
            "messages": messages,
            "temperature": request.temperature,
            "stream": request.stream
        }

        if request.max_tokens:
            payload["max_tokens"] = request.max_tokens
        if request.top_p is not None:
            payload["top_p"] = request.top_p
        if request.frequency_penalty is not None:
            payload["frequency_penalty"] = request.frequency_penalty
        if request.presence_penalty is not None:
            payload["presence_penalty"] = request.presence_penalty

        headers = self._get_headers(LLMProvider.OPENROUTER)
        headers["HTTP-Referer"] = "https://github.com/your-repo"  # Required by OpenRouter
        headers["X-Title"] = "AI Coding Agent"

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{config['base_url']}/chat/completions",
                json=payload,
                headers=headers,
                timeout=config.get('timeout', 60)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    choice = data["choices"][0]
                    content = choice["message"]["content"]

                    usage_data = data.get("usage", {})
                    cost = self._calculate_cost(model, usage_data, LLMProvider.OPENROUTER)

                    usage = UsageStats(
                        prompt_tokens=usage_data.get("prompt_tokens", 0),
                        completion_tokens=usage_data.get("completion_tokens", 0),
                        total_tokens=usage_data.get("total_tokens", 0),
                        cost=cost
                    )

                    return LLMResponse(
                        content=content,
                        model=model,
                        provider=LLMProvider.OPENROUTER,
                        usage=usage,
                        request_id=request_id,
                        finish_reason=choice.get("finish_reason", "stop")
                    )
                else:
                    error_data = await response.json() if response.content_type == 'application/json' else await response.text()
                    raise GenerationError(f"OpenRouter API error: {response.status} - {error_data}")

    async def _generate_openai(self, request: GenerateRequest, model: str, request_id: str) -> LLMResponse:
        """Generate response using OpenAI API."""
        if not OPENAI_AVAILABLE:
            raise ProviderUnavailableError("OpenAI client not available. Install with: pip install openai", provider=LLMProvider.OPENAI)

        config = self.providers[LLMProvider.OPENAI]

        if not config.get('api_key'):
            raise InvalidAPIKeyError("OpenAI API key not configured", provider=LLMProvider.OPENAI)

        # Initialize OpenAI client
        client = openai.AsyncOpenAI(
            api_key=config['api_key'],
            base_url=config.get('base_url', 'https://api.openai.com/v1'),
            timeout=config.get('timeout', 60)
        )

        # Prepare messages
        messages = []
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})
        messages.append({"role": "user", "content": request.prompt})

        # Prepare request parameters
        params = {
            "model": model,
            "messages": messages,
            "temperature": request.temperature,
            "stream": request.stream
        }

        if request.max_tokens:
            params["max_tokens"] = request.max_tokens
        if request.top_p is not None:
            params["top_p"] = request.top_p
        if request.frequency_penalty is not None:
            params["frequency_penalty"] = request.frequency_penalty
        if request.presence_penalty is not None:
            params["presence_penalty"] = request.presence_penalty

        try:
            # Make API call
            response = await client.chat.completions.create(**params)

            # Extract response data
            choice = response.choices[0]
            content = choice.message.content or ""

            # Calculate cost
            usage_data = {
                "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                "total_tokens": response.usage.total_tokens if response.usage else 0
            }
            cost = self._calculate_cost(model, usage_data, LLMProvider.OPENAI)

            usage = UsageStats(
                prompt_tokens=usage_data["prompt_tokens"],
                completion_tokens=usage_data["completion_tokens"],
                total_tokens=usage_data["total_tokens"],
                cost=cost
            )

            return LLMResponse(
                content=content,
                model=model,
                provider=LLMProvider.OPENAI,
                usage=usage,
                request_id=request_id,
                finish_reason=choice.finish_reason or "stop"
            )

        except openai.APIError as e:
            if e.status_code == 401:
                raise InvalidAPIKeyError(f"OpenAI API authentication failed: {str(e)}", provider=LLMProvider.OPENAI)
            elif e.status_code == 429:
                raise RateLimitExceededError(f"OpenAI rate limit exceeded: {str(e)}", provider=LLMProvider.OPENAI)
            elif e.status_code == 404:
                raise ModelNotFoundError(f"OpenAI model not found: {model}", provider=LLMProvider.OPENAI)
            else:
                raise GenerationError(f"OpenAI API error: {str(e)}")
        except Exception as e:
            raise GenerationError(f"OpenAI generation failed: {str(e)}")

    async def _generate_anthropic(self, request: GenerateRequest, model: str, request_id: str) -> LLMResponse:
        """Generate response using Anthropic API."""
        if not ANTHROPIC_AVAILABLE:
            raise ProviderUnavailableError("Anthropic client not available. Install with: pip install anthropic", provider=LLMProvider.ANTHROPIC)

        config = self.providers[LLMProvider.ANTHROPIC]

        if not config.get('api_key'):
            raise InvalidAPIKeyError("Anthropic API key not configured", provider=LLMProvider.ANTHROPIC)

        # Initialize Anthropic client
        client = anthropic.AsyncAnthropic(
            api_key=config['api_key'],
            base_url=config.get('base_url', 'https://api.anthropic.com'),
            timeout=config.get('timeout', 60)
        )

        # Prepare messages for Anthropic format
        messages = []
        system_prompt = None

        if request.system_prompt:
            system_prompt = request.system_prompt

        messages.append({"role": "user", "content": request.prompt})

        # Prepare request parameters
        params = {
            "model": model,
            "messages": messages,
            "temperature": request.temperature,
            "max_tokens": request.max_tokens or 4096  # Anthropic requires max_tokens
        }

        if system_prompt:
            params["system"] = system_prompt
        if request.top_p is not None:
            params["top_p"] = request.top_p

        try:
            # Make API call
            response = await client.messages.create(**params)

            # Extract response data
            content = ""
            if response.content and len(response.content) > 0:
                # Anthropic returns content as a list of content blocks
                content = response.content[0].text if hasattr(response.content[0], 'text') else str(response.content[0])

            # Calculate cost
            usage_data = {
                "prompt_tokens": response.usage.input_tokens if response.usage else 0,
                "completion_tokens": response.usage.output_tokens if response.usage else 0,
                "total_tokens": (response.usage.input_tokens + response.usage.output_tokens) if response.usage else 0
            }
            cost = self._calculate_cost(model, usage_data, LLMProvider.ANTHROPIC)

            usage = UsageStats(
                prompt_tokens=usage_data["prompt_tokens"],
                completion_tokens=usage_data["completion_tokens"],
                total_tokens=usage_data["total_tokens"],
                cost=cost
            )

            return LLMResponse(
                content=content,
                model=model,
                provider=LLMProvider.ANTHROPIC,
                usage=usage,
                request_id=request_id,
                finish_reason=response.stop_reason or "stop"
            )

        except anthropic.APIError as e:
            if e.status_code == 401:
                raise InvalidAPIKeyError(f"Anthropic API authentication failed: {str(e)}", provider=LLMProvider.ANTHROPIC)
            elif e.status_code == 429:
                raise RateLimitExceededError(f"Anthropic rate limit exceeded: {str(e)}", provider=LLMProvider.ANTHROPIC)
            elif e.status_code == 404:
                raise ModelNotFoundError(f"Anthropic model not found: {model}", provider=LLMProvider.ANTHROPIC)
            else:
                raise GenerationError(f"Anthropic API error: {str(e)}")
        except Exception as e:
            raise GenerationError(f"Anthropic generation failed: {str(e)}")

    def _calculate_cost(self, model: str, usage: Dict[str, int], provider: LLMProvider) -> Optional[float]:
        """Calculate cost for cloud provider usage."""
        if provider == LLMProvider.OLLAMA:
            return None

        # Simplified cost calculation - in production, use actual provider pricing
        costs_per_1k_tokens = {
            LLMProvider.OPENROUTER: {
                "meta-llama/llama-3.1-8b-instruct:free": 0.0,
                "meta-llama/llama-3.1-70b-instruct:nitro": 0.9,
                "anthropic/claude-3.5-sonnet": 3.0,
            },
            LLMProvider.OPENAI: {
                "gpt-4o": 5.0,
                "gpt-4o-mini": 0.15,
                "gpt-3.5-turbo": 0.5,
            },
            LLMProvider.ANTHROPIC: {
                "claude-3-5-sonnet-20241022": 3.0,
                "claude-3-haiku-20240307": 0.25,
            }
        }

        model_costs = costs_per_1k_tokens.get(provider, {})
        cost_per_1k = model_costs.get(model, 1.0)  # Default fallback cost

        total_tokens = usage.get("total_tokens", 0)
        return (total_tokens / 1000.0) * cost_per_1k

    def _update_circuit_breaker(self, provider: LLMProvider, success: bool):
        """Update circuit breaker state."""
        breaker = self.circuit_breakers[provider]

        if success:
            breaker['failure_count'] = 0
            if breaker['state'] == 'half-open':
                breaker['state'] = 'closed'
        else:
            # Ensure failure_count is always an integer
            current_count = breaker.get('failure_count', 0)
            if isinstance(current_count, (int, float)):
                breaker['failure_count'] = int(current_count) + 1
            else:
                breaker['failure_count'] = 1
            breaker['last_failure_time'] = time.time()

            # Open circuit if too many failures
            if breaker['failure_count'] >= 5:
                breaker['state'] = 'open'
            # Try half-open after 60 seconds
            elif (breaker['state'] == 'open' and
                  time.time() - breaker['last_failure_time'] > 60):
                breaker['state'] = 'half-open'

    async def pull_model(self, request: ModelPullRequest) -> ModelPullResponse:
        """Pull a model from the specified provider.

        Args:
            request: Model pull request

        Returns:
            ModelPullResponse with operation result
        """
        if request.provider != LLMProvider.OLLAMA:
            raise ProviderUnavailableError("Model pulling only supported for Ollama", provider=request.provider)

        config = self.providers[LLMProvider.OLLAMA]

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{config['base_url']}/api/pull",
                    json={"name": request.model_name},
                    timeout=300  # Model pulling can take a while
                ) as response:
                    if response.status == 200:
                        logger.info(f"Successfully pulled model: {request.model_name}")
                        return ModelPullResponse(
                            success=True,
                            message=f"Successfully pulled model: {request.model_name}",
                            model_name=request.model_name,
                            provider=request.provider
                        )
                    else:
                        error_text = await response.text()
                        return ModelPullResponse(
                            success=False,
                            message=f"Failed to pull model: {error_text}",
                            model_name=request.model_name,
                            provider=request.provider
                        )
        except Exception as e:
            logger.error(f"Error pulling model {request.model_name}: {str(e)}")
            return ModelPullResponse(
                success=False,
                message=f"Error pulling model: {str(e)}",
                model_name=request.model_name,
                provider=request.provider
            )

    async def health_check(self) -> HealthCheckResponse:
        """Perform comprehensive health check of all providers.

        Returns:
            HealthCheckResponse with system status
        """
        provider_statuses = []
        total_requests = sum(self.request_stats.get(p, {}).get('total', 0) for p in LLMProvider)
        failed_requests = sum(self.request_stats.get(p, {}).get('failed', 0) for p in LLMProvider)

        # Test each provider
        for provider in LLMProvider:
            status = await self.test_provider_connection(provider)
            provider_statuses.append(status)

        # Calculate overall status
        healthy_providers = sum(1 for s in provider_statuses if s.available)
        if healthy_providers == 0:
            overall_status = "unhealthy"
        elif healthy_providers < len(LLMProvider) / 2:
            overall_status = "degraded"
        else:
            overall_status = "healthy"

        # Calculate average response time
        all_times = []
        for provider_times in self.response_times.values():
            all_times.extend(provider_times)
        avg_response_time = sum(all_times) / len(all_times) if all_times else None

        return HealthCheckResponse(
            status=overall_status,
            providers=provider_statuses,
            total_requests=total_requests,
            failed_requests=failed_requests,
            average_response_time_ms=avg_response_time
        )

    async def get_usage_statistics(self, time_window: str = "1h") -> Dict[str, Any]:
        """Get usage statistics for monitoring.

        Args:
            time_window: Time window for statistics (1h, 1d, 1w)

        Returns:
            Dictionary containing usage statistics
        """
        if not self.redis_client:
            return {"error": "Redis not available for statistics"}

        # This is a simplified implementation
        # In production, you'd have more sophisticated metrics collection
        stats = {
            "providers": {},
            "total_requests": sum(self.request_stats.get(p, {}).get('total', 0) for p in LLMProvider),
            "total_errors": sum(self.error_counts.values()),
            "average_response_times": {}
        }

        for provider in LLMProvider:
            provider_stats = self.request_stats.get(provider, {})
            response_times = list(self.response_times.get(provider, []))

            stats["providers"][provider] = {
                "requests": provider_stats.get('total', 0),
                "errors": provider_stats.get('failed', 0),
                "success_rate": provider_stats.get('success', 0) / max(provider_stats.get('total', 1), 1),
            }

            if response_times:
                stats["average_response_times"][provider] = sum(response_times) / len(response_times)

        return stats

    async def get_service_health(self) -> Dict[str, Any]:
        """Get comprehensive health status of the LLM service.

        Returns:
            Dict containing service health information
        """
        try:
            # Test Redis connection
            redis_healthy = False
            if self.redis_client:
                try:
                    await self.redis_client.ping()
                    redis_healthy = True
                except Exception:
                    pass

            # Test provider connectivity
            provider_status = {}
            for provider in LLMProvider:
                try:
                    status = await self.test_provider_connection(provider)
                    provider_status[provider.value] = {
                        "available": status.available,
                        "response_time_ms": status.response_time_ms,
                        "error": status.error_message
                    }
                except Exception as e:
                    provider_status[provider.value] = {
                        "available": False,
                        "error": str(e)
                    }

            return {
                "status": "healthy",
                "redis_available": redis_healthy,
                "prometheus_available": PROMETHEUS_AVAILABLE,
                "providers": provider_status,
                "circuit_breakers": dict(self.circuit_breakers),
                "error_counts": dict(self.error_counts)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }

    # Additional methods for OpenAI and Anthropic would be similar to OpenRouter
    # Omitting for brevity but following the same pattern