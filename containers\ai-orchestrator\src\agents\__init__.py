# Project: AI Coding Agent
# Purpose: Agents module exports

from .base_agent import BaseAgent
from .architect import ArchitectAgent

# Import other agent implementations
from .backend import BackendAgent
from .frontend import FrontendAgent
from .shell import ShellAgent
from .issue_fix import IssueFixAgent

# Export all available agents
__all__ = [
    "BaseAgent",
    "ArchitectAgent",
    "BackendAgent",
    "FrontendAgent",
    "ShellAgent",
    "IssueFixAgent",
]

# Agent registry for dynamic access
AGENT_CLASSES = {
    "architect": ArchitectAgent,
    "backend": BackendAgent,
    "frontend": FrontendAgent,
    "shell": ShellAgent,
    "issue_fix": IssueFixAgent,
}


def get_agent_class(agent_type: str):
    """Get agent class by type string.

    Args:
        agent_type: Agent type string (e.g., 'architect', 'backend')

    Returns:
        Agent class or None if not found
    """
    return AGENT_CLASSES.get(agent_type)


def get_available_agents():
    """Get list of available agent types.

    Returns:
        List of available agent type strings
    """
    return list(AGENT_CLASSES.keys())