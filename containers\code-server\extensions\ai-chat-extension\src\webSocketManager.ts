/**
 * WebSocket Manager for AI Chat Extension
 * Handles WebSocket connection and message routing with AI Orchestrator
 */

import * as vscode from 'vscode';

export interface ChatMessage {
    id: string;
    type: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: string;
    agent_type?: string;
    user_id?: string;
}

export interface ServerMessage {
    type: 'connection_established' | 'pong' | 'chat_response' | 'system' | 'error' | 'agent_started' | 'agent_progress' | 'agent_completed' | 'agent_failed';
    content?: string;
    response?: string;
    agent_type?: string;
    user_id?: string;
    timestamp?: string;
    id?: string;
    error?: string;
    progress?: number;
    message?: string;
}

export interface MessageHandler {
    (message: ChatMessage): void;
}

export interface ConnectionConfig {
    serverUrl: string;
    maxReconnectAttempts: number;
    reconnectDelay: number;
    heartbeatInterval: number;
}

export class WebSocketManager {
    private ws: WebSocket | null = null;
    private config: ConnectionConfig;
    private reconnectAttempts: number = 0;
    private reconnectTimeout: NodeJS.Timeout | null = null;
    private heartbeatTimeout: NodeJS.Timeout | null = null;
    private messageHandlers: MessageHandler[] = [];
    private isConnecting: boolean = false;
    private disposed: boolean = false;

    constructor(config?: Partial<ConnectionConfig>) {
        const vscodeConfig = vscode.workspace.getConfiguration('aiChat');

        this.config = {
            serverUrl: config?.serverUrl || vscodeConfig.get('serverUrl', 'ws://localhost:8000'),
            maxReconnectAttempts: config?.maxReconnectAttempts || vscodeConfig.get('maxReconnectAttempts', 5),
            reconnectDelay: config?.reconnectDelay || vscodeConfig.get('reconnectDelay', 1000),
            heartbeatInterval: config?.heartbeatInterval || vscodeConfig.get('heartbeatInterval', 30000)
        };
    }

    public connect(): void {
        if (this.disposed) {
            console.warn('WebSocketManager is disposed, cannot connect');
            return;
        }

        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
            return;
        }

        this.isConnecting = true;
        const wsUrl = this.buildWebSocketUrl();

        try {
            this.ws = new WebSocket(wsUrl);

            this.ws.onopen = () => {
                console.log('Connected to AI Orchestrator');
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.showStatusMessage('Connected to AI service', false);
                this.startHeartbeat();

                // Send initial connection message
                this.sendMessage({
                    type: 'system',
                    content: 'Connected to VS Code extension',
                    timestamp: new Date().toISOString()
                });
            };

            this.ws.onmessage = (event: MessageEvent) => {
                try {
                    const message = this.parseMessage(event.data);
                    if (message) {
                        this.handleIncomingMessage(message);
                    }
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                    this.showStatusMessage(`Message parsing error: ${error}`, true);
                }
            };

            this.ws.onclose = (event: CloseEvent) => {
                console.log(`WebSocket closed: ${event.code} - ${event.reason}`);
                this.isConnecting = false;
                this.ws = null;
                this.stopHeartbeat();

                if (!this.disposed) {
                    this.showStatusMessage('Disconnected from AI service', true);
                    this.attemptReconnect();
                }
            };

            this.ws.onerror = (event: Event) => {
                console.error('WebSocket error:', event);
                this.isConnecting = false;
                this.showStatusMessage('Connection error occurred', true);
            };

        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.isConnecting = false;
            this.showStatusMessage(`Failed to connect: ${error}`, true);
        }
    }

    public disconnect(): void {
        this.clearTimeouts();

        if (this.ws) {
            this.ws.close(1000, 'Manual disconnect');
            this.ws = null;
        }

        this.reconnectAttempts = 0;
        this.isConnecting = false;
    }

    public reconnect(): void {
        this.disconnect();
        setTimeout(() => {
            if (!this.disposed) {
                this.connect();
            }
        }, this.config.reconnectDelay);
    }

    public dispose(): void {
        this.disposed = true;
        this.clearTimeouts();

        if (this.ws) {
            this.ws.close(1000, 'Extension disposing');
            this.ws = null;
        }

        this.messageHandlers = [];
        this.reconnectAttempts = 0;
        this.isConnecting = false;
    }

    public sendChatMessage(content: string): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!content || content.trim().length === 0) {
                reject(new Error('Message content cannot be empty'));
                return;
            }

            if (!this.isConnected()) {
                vscode.window.showWarningMessage('Not connected to AI service. Attempting to reconnect...');
                this.connect();
                reject(new Error('Not connected to AI service'));
                return;
            }

            try {
                const message: ChatMessage = {
                    id: this.generateMessageId(),
                    type: 'user',
                    content: content.trim(),
                    timestamp: new Date().toISOString(),
                    user_id: this.getUserId()
                };

                this.sendMessage(message);
                resolve();
            } catch (error) {
                reject(error);
            }
        });
    }

    public addMessageHandler(handler: MessageHandler): void {
        this.messageHandlers.push(handler);
    }

    public removeMessageHandler(handler: MessageHandler): void {
        const index = this.messageHandlers.indexOf(handler);
        if (index > -1) {
            this.messageHandlers.splice(index, 1);
        }
    }

    public isConnected(): boolean {
        return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
    }

    public getConnectionStatus(): string {
        if (this.isConnecting) {
            return 'Connecting...';
        }

        if (this.isConnected()) {
            return 'Connected';
        }

        return 'Disconnected';
    }

    private sendMessage(message: Partial<ChatMessage> | { type: string; content: string; timestamp: string }): void {
        if (this.isConnected() && this.ws) {
            try {
                const serializedMessage = JSON.stringify(message);
                this.ws.send(serializedMessage);
            } catch (error) {
                console.error('Error sending message:', error);
                vscode.window.showErrorMessage(`Failed to send message: ${error}`);
                throw error;
            }
        } else {
            throw new Error('WebSocket is not connected');
        }
    }

    private handleIncomingMessage(data: ServerMessage): void {
        try {
            // Handle different message types
            switch (data.type) {
                case 'connection_established':
                    console.log('Connection established:', data);
                    break;

                case 'pong':
                    console.log('Received pong from server');
                    break;

                case 'error':
                    console.error('Server error:', data.error);
                    vscode.window.showErrorMessage(`Server error: ${data.error}`);
                    break;

                case 'agent_started':
                    console.log('Agent started:', data.agent_type);
                    this.notifyStatusChange(data, 'Agent started');
                    break;

                case 'agent_progress':
                    console.log('Agent progress:', data.progress, data.message);
                    this.notifyStatusChange(data, `Progress: ${data.progress}% - ${data.message}`);
                    break;

                case 'agent_completed':
                    console.log('Agent completed:', data.agent_type);
                    this.notifyStatusChange(data, 'Agent completed');
                    break;

                case 'agent_failed':
                    console.error('Agent failed:', data.error);
                    this.notifyStatusChange(data, `Agent failed: ${data.error}`);
                    break;

                case 'chat_response':
                case 'system':
                    // Handle chat messages
                    if (data.response && data.agent_type) {
                        const chatMessage: ChatMessage = {
                            id: this.generateMessageId(),
                            type: 'assistant',
                            content: data.response,
                            timestamp: data.timestamp || new Date().toISOString(),
                            agent_type: data.agent_type,
                            user_id: data.user_id
                        };
                        this.notifyMessageHandlers(chatMessage);
                    } else if (data.content) {
                        // Generic message format
                        const messageType = this.validateMessageType(data.type);
                        const chatMessage: ChatMessage = {
                            id: data.id || this.generateMessageId(),
                            type: messageType,
                            content: data.content,
                            timestamp: data.timestamp || new Date().toISOString(),
                            agent_type: data.agent_type,
                            user_id: data.user_id
                        };
                        this.notifyMessageHandlers(chatMessage);
                    }
                    break;

                default:
                    console.warn('Unknown message type:', data.type);
            }

        } catch (error) {
            console.error('Error handling incoming message:', error);
            this.showStatusMessage(`Message handling error: ${error}`, true);
        }
    }

    private attemptReconnect(): void {
        if (this.disposed) {
            return;
        }

        if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
            vscode.window.showErrorMessage(
                'Failed to reconnect to AI service. Please check your connection and try manually reconnecting.',
                'Retry'
            ).then((selection: string | undefined) => {
                if (selection === 'Retry') {
                    this.reconnectAttempts = 0;
                    this.connect();
                }
            });
            return;
        }

        this.reconnectAttempts++;
        const delay = Math.min(Math.pow(2, this.reconnectAttempts) * this.config.reconnectDelay, 30000); // Exponential backoff with max delay

        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);

        this.reconnectTimeout = setTimeout(() => {
            if (!this.disposed) {
                this.connect();
            }
        }, delay);
    }

    private notifyMessageHandlers(message: ChatMessage): void {
        this.messageHandlers.forEach(handler => {
            try {
                handler(message);
            } catch (error) {
                console.error('Error in message handler:', error);
            }
        });
    }

    private notifyStatusChange(data: ServerMessage, statusMessage: string): void {
        // Create a system message for status updates
        const systemMessage: ChatMessage = {
            id: this.generateMessageId(),
            type: 'system',
            content: statusMessage,
            timestamp: data.timestamp || new Date().toISOString(),
            agent_type: data.agent_type,
            user_id: data.user_id
        };
        this.notifyMessageHandlers(systemMessage);
    }

    private generateMessageId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    private getUserId(): string {
        // For now, use a simple identifier
        // In a real implementation, this might come from authentication
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        const workspaceName = workspaceFolder?.name || 'unknown';
        return `vscode-user-${workspaceName}`;
    }

    private showStatusMessage(message: string, isError: boolean): void {
        if (isError) {
            console.error(message);
            // Optionally show in VS Code status bar or output channel
        } else {
            console.log(message);
        }
    }

    private buildWebSocketUrl(): string {
        let serverUrl = this.config.serverUrl;

        // Ensure URL format is correct
        if (!serverUrl.startsWith('ws://') && !serverUrl.startsWith('wss://')) {
            // Convert http/https to ws/wss
            if (serverUrl.startsWith('https://')) {
                serverUrl = serverUrl.replace('https://', 'wss://');
            } else if (serverUrl.startsWith('http://')) {
                serverUrl = serverUrl.replace('http://', 'ws://');
            } else {
                // Default to ws if no protocol specified
                serverUrl = `ws://${serverUrl}`;
            }
        }

        // Remove trailing slash
        serverUrl = serverUrl.replace(/\/$/, '');

        // Add WebSocket endpoint
        return `${serverUrl}/ws`;
    }

    private parseMessage(data: string): ServerMessage | null {
        try {
            const parsed = JSON.parse(data);

            // Basic validation
            if (typeof parsed !== 'object' || parsed === null) {
                throw new Error('Message is not an object');
            }

            if (!parsed.type || typeof parsed.type !== 'string') {
                throw new Error('Message missing type field');
            }

            return parsed as ServerMessage;
        } catch (error) {
            console.error('Failed to parse message:', error);
            return null;
        }
    }

    private validateMessageType(type: string): ChatMessage['type'] {
        const validTypes: ChatMessage['type'][] = ['user', 'assistant', 'system'];
        return validTypes.includes(type as ChatMessage['type']) ? type as ChatMessage['type'] : 'assistant';
    }

    private startHeartbeat(): void {
        this.stopHeartbeat(); // Clear any existing heartbeat

        this.heartbeatTimeout = setTimeout(() => {
            if (this.isConnected()) {
                try {
                    this.sendMessage({
                        type: 'ping',
                        content: '',
                        timestamp: new Date().toISOString()
                    });

                    // Schedule next heartbeat
                    this.startHeartbeat();
                } catch (error) {
                    console.error('Failed to send heartbeat:', error);
                }
            }
        }, this.config.heartbeatInterval);
    }

    private stopHeartbeat(): void {
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }
    }

    private clearTimeouts(): void {
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }

        this.stopHeartbeat();
    }
}