{"python.pythonPath": "/usr/bin/python3", "python.linting.enabled": true, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "editor.formatOnSave": true, "files.associations": {"*.py": "python", "*.js": "javascript", "*.jsx": "javascriptreact", "*.ts": "typescript", "*.tsx": "typescriptreact", "*.json": "json", "*.yml": "yaml", "*.yaml": "yaml", "Dockerfile*": "dockerfile"}, "terminal.integrated.shell.linux": "/bin/bash", "gitlens.advanced.blame.customArguments": [], "tailwindCSS.includeLanguages": {"plaintext": "html", "javascript": "javascript", "typescript": "typescript", "javascriptreact": "javascriptreact", "typescriptreact": "typescriptreact"}}