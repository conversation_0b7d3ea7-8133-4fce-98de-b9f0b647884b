# syntax=docker/dockerfile:1
# Multi-stage Dockerfile for Next.js application with standalone output
# This creates a minimal production image optimized for enterprise deployment

# ============================================================================
# DEPENDENCIES STAGE: Install all dependencies including devDependencies
# ============================================================================
FROM node:20-alpine AS deps

# Install libc6-compat for Alpine compatibility
RUN apk add --no-cache libc6-compat

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package*.json .npmrc ./

# Install dependencies - generate package-lock.json if missing, then use npm ci
RUN if [ -f package-lock.json ]; then \
  npm ci --legacy-peer-deps; \
  else \
  npm install --legacy-peer-deps; \
  fi

# ============================================================================
# BUILDER STAGE: Build the application
# ============================================================================
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Ensure public directory exists
RUN mkdir -p ./public

# Build the application with standalone output for minimal runtime
# This creates a self-contained application in .next/standalone
RUN npm run build

# ============================================================================
# PRODUCTION STAGE: Minimal runtime image
# ============================================================================
FROM node:20-alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user for security (following Next.js best practices)
RUN addgroup --system --gid 1001 nodejs && \
  adduser --system --uid 1001 nextjs

# Set working directory
WORKDIR /app

# Create public directory and copy static assets
RUN mkdir -p ./public
# Copy public directory from builder (will copy empty dir if no content)
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Copy standalone application from builder stage
# This includes all necessary dependencies and Next.js runtime
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./

# Copy static assets generated during build
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Switch to non-root user for security
USER nextjs

# Expose application port
EXPOSE 3000

# Set environment variable for production
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Add health check for production monitoring
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Set security and project labels
LABEL org.opencontainers.image.title="AI Coding Agent - Admin Dashboard" \
  org.opencontainers.image.description="Next.js-based role management and LLM configuration dashboard" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="nextjs"

# Start the Next.js standalone server
# The standalone build includes its own minimal Node.js server
CMD ["node", "server.js"]
