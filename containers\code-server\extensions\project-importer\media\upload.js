(function() {
    const vscode = acquireVsCodeApi();
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('file-input');
    const fileList = document.getElementById('file-list');
    const uploadButton = document.getElementById('upload-button');

    let files = [];

    // Handle drop area click
    dropArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Handle file selection
    fileInput.addEventListener('change', handleFiles);

    // Handle drag and drop
    dropArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropArea.classList.add('active');
    });

    dropArea.addEventListener('dragleave', () => {
        dropArea.classList.remove('active');
    });

    dropArea.addEventListener('drop', (e) => {
        e.preventDefault();
        dropArea.classList.remove('active');

        if (e.dataTransfer.items) {
            // Use DataTransferItemList interface
            for (let i = 0; i < e.dataTransfer.items.length; i++) {
                if (e.dataTransfer.items[i].kind === 'file') {
                    const file = e.dataTransfer.items[i].getAsFile();
                    addFileToList(file);
                }
            }
        } else {
            // Use DataTransfer interface
            for (let i = 0; i < e.dataTransfer.files.length; i++) {
                addFileToList(e.dataTransfer.files[i]);
            }
        }

        updateUploadButton();
    });

    // Handle file selection
    function handleFiles() {
        const selectedFiles = fileInput.files;
        if (!selectedFiles.length) return;

        // Clear previous files
        files = [];
        fileList.innerHTML = '';

        for (let i = 0; i < selectedFiles.length; i++) {
            addFileToList(selectedFiles[i]);
        }

        updateUploadButton();
    }

    // Add file to the list
    function addFileToList(file) {
        // Read file content
        const reader = new FileReader();
        reader.onload = function(e) {
            const fileData = {
                name: file.name,
                path: file.webkitRelativePath || file.name,
                content: e.target.result,
                type: file.type
            };

            files.push(fileData);

            // Add file to UI list
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span>${fileData.path}</span>
                <span>${formatFileSize(file.size)}</span>
            `;
            fileList.appendChild(fileItem);

            updateUploadButton();
        };

        reader.readAsText(file);
    }

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Update upload button state
    function updateUploadButton() {
        uploadButton.disabled = files.length === 0;
    }

    // Handle upload button click
    uploadButton.addEventListener('click', () => {
        if (files.length === 0) return;

        vscode.postMessage({
            command: 'uploadFiles',
            files: files
        });
    });
})();