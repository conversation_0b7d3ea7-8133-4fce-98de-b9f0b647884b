import os
import sys
import shutil
import subprocess
from pathlib import Path
from typing import Any, Dict, List

import pytest

# Ensure we can import the ai-orchestrator src package named "src"
AI_SRC_PATH = os.path.join(
    os.path.dirname(__file__), "..", "..", "containers", "ai-orchestrator", "src"
)
sys.path.insert(0, os.path.abspath(AI_SRC_PATH))

from src.repository.project_repository import (  # type: ignore  # noqa: E402
    ProjectRepository,
    ProjectRepositoryError,
    ProjectNotFoundError,
)
from src.core.config import settings  # type: ignore  # noqa: E402


@pytest.fixture()
def temp_workspace(tmp_path: Path, monkeypatch: pytest.MonkeyPatch) -> Path:
    """Provide a temporary workspace path and patch settings to use it."""
    monkeypatch.setattr(settings, "WORKSPACE_PATH", tmp_path)
    return tmp_path


@pytest.mark.asyncio
async def test_get_workspace_info_flags(temp_workspace: Path) -> None:
    repo = ProjectRepository()
    user_id = "u1"

    info = await repo.get_workspace_info(user_id)
    assert isinstance(info, dict)
    assert info["workspace_path"] == str(temp_workspace)
    assert info["user_workspace"].endswith(f"user_{user_id}")
    assert info["workspace_exists"] is True
    # user dir not created yet
    assert info["user_workspace_exists"] is False


@pytest.mark.asyncio
async def test_upload_and_list_projects(temp_workspace: Path) -> None:
    repo = ProjectRepository()
    user_id = "u2"

    files = [
        {"name": "README.md", "path": "demo/README.md", "content": "hello"},
        {"name": "main.py", "path": "demo/src/main.py", "content": "print('hi')"},
    ]

    result = await repo.upload_project_files(user_id, files)
    assert result["files_processed"] == 2
    user_dir = temp_workspace / f"user_{user_id}"
    assert (user_dir / "demo/README.md").exists()
    assert (user_dir / "demo/src/main.py").exists()

    projects = await repo.list_user_projects(user_id)
    assert isinstance(projects, list)
    assert any(p["name"] == "demo" for p in projects)


@pytest.mark.asyncio
async def test_delete_project_success_and_errors(temp_workspace: Path) -> None:
    repo = ProjectRepository()
    user_id = "u3"
    user_dir = temp_workspace / f"user_{user_id}"
    project_name = "demo_project"
    project_path = user_dir / project_name
    project_path.mkdir(parents=True, exist_ok=True)

    # Delete existing project
    res = await repo.delete_project(user_id, project_name)
    assert "Successfully deleted" in res["message"]
    assert not project_path.exists()

    # Deleting non-existent should raise
    with pytest.raises(ProjectNotFoundError):
        await repo.delete_project(user_id, project_name)


@pytest.mark.asyncio
@pytest.mark.skipif(shutil.which("git") is None, reason="git not available")
async def test_clone_git_repository_local(temp_workspace: Path) -> None:
    repo = ProjectRepository()
    user_id = "u4"

    # Create a local git repository to clone
    source_repo = temp_workspace / "source_repo"
    source_repo.mkdir(parents=True, exist_ok=True)

    # Initialize and commit a file
    subprocess.run(["git", "init"], cwd=str(source_repo), check=True)
    subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=str(source_repo), check=True)
    subprocess.run(["git", "config", "user.name", "Test User"], cwd=str(source_repo), check=True)

    (source_repo / "hello.txt").write_text("hello", encoding="utf-8")
    subprocess.run(["git", "add", "hello.txt"], cwd=str(source_repo), check=True)
    subprocess.run(["git", "commit", "-m", "init"], cwd=str(source_repo), check=True)

    # Clone into the user's workspace
    res = await repo.clone_git_repository(user_id=user_id, repository_url=str(source_repo))
    assert res["message"].startswith("Successfully cloned")
    target_path = Path(res["path"])  # inside user workspace
    assert target_path.exists()
    assert (target_path / ".git").exists()
    assert (target_path / "hello.txt").exists()

    # Attempt to clone into existing directory should raise
    with pytest.raises(ProjectRepositoryError):
        await repo.clone_git_repository(user_id=user_id, repository_url=str(source_repo))