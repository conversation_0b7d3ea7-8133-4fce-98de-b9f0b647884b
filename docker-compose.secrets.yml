# Security-Enhanced Docker Compose Configuration
# For AI Coding Agent - Secrets Management
# Usage: docker-compose -f docker-compose.yml -f docker-compose.secrets.yml up -d

# Define secrets for secure credential management
secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  supabase_service_key:
    file: ./secrets/supabase_service_key.txt
  openrouter_api_key:
    file: ./secrets/openrouter_api_key.txt
  openai_api_key:
    file: ./secrets/openai_api_key.txt
  anthropic_api_key:
    file: ./secrets/anthropic_api_key.txt
  code_server_password:
    file: ./secrets/code_server_password.txt

services:
  postgresql:
    secrets:
      - postgres_password
    environment:
      # Remove POSTGRES_PASSWORD from environment variables
      POSTGRES_DB: ai_coding_agent
      POSTGRES_USER: postgres
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
      # PostgreS<PERSON> will read password from /run/secrets/postgres_password
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password

  ai-orchestrator:
    secrets:
      - postgres_password
      - jwt_secret
      - supabase_service_key
      - openrouter_api_key
      - openai_api_key
      - anthropic_api_key
    environment:
      # Database URL with reference to secret
      DATABASE_URL: postgresql://postgres@postgresql:5432/ai_coding_agent
      REDIS_URL: redis://redis:6379/0
      OLLAMA_BASE_URL: ${OLLAMA_BASE_URL:-http://host.docker.internal:11434}

      # Secret file paths (to be read by application)
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
      JWT_SECRET_FILE: /run/secrets/jwt_secret
      SUPABASE_SERVICE_KEY_FILE: /run/secrets/supabase_service_key
      OPENROUTER_API_KEY_FILE: /run/secrets/openrouter_api_key
      OPENAI_API_KEY_FILE: /run/secrets/openai_api_key
      ANTHROPIC_API_KEY_FILE: /run/secrets/anthropic_api_key

      # Non-sensitive configuration
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_KEY: ${SUPABASE_KEY}  # This can remain as env var as it's typically public
      DEFAULT_LOCAL_PROVIDER: ${DEFAULT_LOCAL_PROVIDER:-ollama}
      DEFAULT_CLOUD_PROVIDER: ${DEFAULT_CLOUD_PROVIDER:-openrouter}
      ENABLE_CLOUD_FALLBACK: ${ENABLE_CLOUD_FALLBACK:-true}
      PYTHONPATH: /app

  code-server:
    secrets:
      - code_server_password
    environment:
      # Remove PASSWORD from environment variables
      DOCKER_USER: coder
      CODE_SERVER_URL: http://localhost:${CODE_SERVER_PORT:-8080}
      PASSWORD_FILE: /run/secrets/code_server_password

# Note: This configuration requires:
# 1. Creating a ./secrets/ directory
# 2. Creating individual secret files with appropriate permissions (600)
# 3. Updating application code to read from secret files instead of environment variables
# 4. Using: docker-compose -f docker-compose.yml -f docker-compose.secrets.yml up -d