# Project: AI Coding Agent
# Purpose: Comprehensive retry strategies with exponential backoff and advanced policies

import asyncio
import logging
import random
import time
from typing import Dict, Any, Callable, Optional, Union, Type, List
from datetime import datetime
from enum import Enum

from ..models.validation_models import RetryPolicy, ErrorType


class RetryStrategy(Enum):
    """Available retry strategies"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIXED_INTERVAL = "fixed_interval"
    FIBONACCI_BACKOFF = "fibonacci_backoff"
    JITTERED_EXPONENTIAL = "jittered_exponential"


class RetryResult:
    """Result of retry operation"""

    def __init__(self,
                 success: bool,
                 result: Any = None,
                 error: Optional[Exception] = None,
                 attempts: int = 0,
                 total_duration: float = 0.0):
        self.success = success
        self.result = result
        self.error = error
        self.attempts = attempts
        self.total_duration = total_duration


class RetryExecutor:
    """
    Advanced retry executor with multiple strategies and intelligent error handling.

    Supports various backoff strategies, conditional retries, and detailed metrics.
    """

    def __init__(self, name: str = "default"):
        self.name = name
        self.logger = logging.getLogger(f"retry_executor.{name}")

        # Metrics
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        self.total_retries = 0

        # Execution history for analysis
        self.execution_history: List[Dict[str, Any]] = []

    async def execute_with_retry(self,
                                func: Callable,
                                policy: RetryPolicy,
                                strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
                                *args, **kwargs) -> RetryResult:
        """
        Execute function with retry policy.

        Args:
            func: Function to execute
            policy: Retry policy configuration
            strategy: Retry strategy to use
            *args, **kwargs: Arguments for the function

        Returns:
            RetryResult: Result of the retry operation
        """
        self.total_executions += 1
        start_time = time.time()

        attempt = 0
        last_error = None

        while attempt < policy.max_attempts:
            attempt += 1

            try:
                self.logger.debug(f"Retry executor '{self.name}' - attempt {attempt}/{policy.max_attempts}")

                # Execute the function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)

                # Success case
                total_duration = time.time() - start_time
                self.successful_executions += 1

                if attempt > 1:
                    self.total_retries += attempt - 1

                retry_result = RetryResult(
                    success=True,
                    result=result,
                    attempts=attempt,
                    total_duration=total_duration
                )

                self._record_execution(retry_result, policy, strategy)

                self.logger.debug(f"Retry executor '{self.name}' - succeeded on attempt {attempt}")
                return retry_result

            except Exception as e:
                last_error = e
                self.logger.warning(f"Retry executor '{self.name}' - attempt {attempt} failed: {str(e)}")

                # Check if we should retry this error
                if not self._should_retry_error(e, policy):
                    self.logger.info(f"Error type not retryable: {type(e).__name__}")
                    break

                # If this was the last attempt, break
                if attempt >= policy.max_attempts:
                    break

                # Calculate delay before next retry
                delay = self._calculate_delay(attempt, policy, strategy)

                self.logger.debug(f"Retry executor '{self.name}' - waiting {delay:.2f}s before retry")
                await asyncio.sleep(delay)

        # All attempts failed
        total_duration = time.time() - start_time
        self.failed_executions += 1
        self.total_retries += attempt - 1

        retry_result = RetryResult(
            success=False,
            error=last_error,
            attempts=attempt,
            total_duration=total_duration
        )

        self._record_execution(retry_result, policy, strategy)

        self.logger.error(f"Retry executor '{self.name}' - all {attempt} attempts failed")
        return retry_result

    def _should_retry_error(self, error: Exception, policy: RetryPolicy) -> bool:
        """Determine if error should trigger a retry"""
        error_type = type(error).__name__

        # Check explicit no-retry list
        if error_type in policy.no_retry_on_errors:
            return False

        # If retry_on_errors is specified, only retry those errors
        if policy.retry_on_errors:
            return error_type in policy.retry_on_errors

        # Default behavior - retry most errors except certain types
        non_retryable_errors = [
            'ValueError',
            'TypeError',
            'AttributeError',
            'KeyError',
            'AssertionError',
            'SyntaxError'
        ]

        return error_type not in non_retryable_errors

    def _calculate_delay(self,
                        attempt: int,
                        policy: RetryPolicy,
                        strategy: RetryStrategy) -> float:
        """Calculate delay before next retry based on strategy"""

        if strategy == RetryStrategy.FIXED_INTERVAL:
            delay = policy.base_delay_seconds

        elif strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = policy.base_delay_seconds * attempt

        elif strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = policy.base_delay_seconds * (policy.backoff_multiplier ** (attempt - 1))

        elif strategy == RetryStrategy.FIBONACCI_BACKOFF:
            delay = policy.base_delay_seconds * self._fibonacci(attempt)

        elif strategy == RetryStrategy.JITTERED_EXPONENTIAL:
            # Exponential backoff with jitter to avoid thundering herd
            base_delay = policy.base_delay_seconds * (policy.backoff_multiplier ** (attempt - 1))
            jitter = random.uniform(0.5, 1.5)  # ±50% jitter
            delay = base_delay * jitter

        else:
            # Default to exponential backoff
            delay = policy.base_delay_seconds * (policy.backoff_multiplier ** (attempt - 1))

        # Apply maximum delay limit
        return min(delay, policy.max_delay_seconds)

    def _fibonacci(self, n: int) -> int:
        """Calculate fibonacci number for fibonacci backoff"""
        if n <= 1:
            return n
        elif n == 2:
            return 1

        a, b = 1, 1
        for _ in range(3, n + 1):
            a, b = b, a + b
        return b

    def _record_execution(self,
                         result: RetryResult,
                         policy: RetryPolicy,
                         strategy: RetryStrategy):
        """Record execution details for analysis"""
        execution_record = {
            'timestamp': datetime.now().isoformat(),
            'success': result.success,
            'attempts': result.attempts,
            'duration_seconds': result.total_duration,
            'strategy': strategy.value,
            'policy': {
                'max_attempts': policy.max_attempts,
                'base_delay': policy.base_delay_seconds,
                'max_delay': policy.max_delay_seconds,
                'backoff_multiplier': policy.backoff_multiplier
            },
            'error_type': type(result.error).__name__ if result.error else None,
            'error_message': str(result.error) if result.error else None
        }

        self.execution_history.append(execution_record)

        # Keep only recent history (last 100 executions)
        if len(self.execution_history) > 100:
            self.execution_history = self.execution_history[-100:]

    def get_metrics(self) -> Dict[str, Any]:
        """Get retry executor metrics"""
        success_rate = (self.successful_executions / self.total_executions * 100
                       if self.total_executions > 0 else 0)

        avg_retries = (self.total_retries / self.total_executions
                      if self.total_executions > 0 else 0)

        # Analyze recent executions
        recent_executions = self.execution_history[-10:] if self.execution_history else []
        recent_success_rate = (sum(1 for ex in recent_executions if ex['success']) /
                              len(recent_executions) * 100 if recent_executions else 0)

        return {
            'name': self.name,
            'total_executions': self.total_executions,
            'successful_executions': self.successful_executions,
            'failed_executions': self.failed_executions,
            'total_retries': self.total_retries,
            'success_rate_percent': round(success_rate, 2),
            'average_retries_per_execution': round(avg_retries, 2),
            'recent_success_rate_percent': round(recent_success_rate, 2),
            'recent_executions_count': len(recent_executions)
        }

    def reset_metrics(self):
        """Reset all metrics and history"""
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        self.total_retries = 0
        self.execution_history.clear()

        self.logger.info(f"Retry executor '{self.name}' - metrics reset")


class RetryManager:
    """Manager for multiple retry executors and policies"""

    def __init__(self):
        self.executors: Dict[str, RetryExecutor] = {}
        self.policies: Dict[str, RetryPolicy] = {}
        self.logger = logging.getLogger("retry_manager")

        # Create default policies
        self._create_default_policies()

    def _create_default_policies(self):
        """Create common retry policies"""

        # Quick retry for fast operations
        self.policies['quick'] = RetryPolicy(
            max_attempts=3,
            base_delay_seconds=0.5,
            max_delay_seconds=5.0,
            backoff_multiplier=2.0
        )

        # Standard retry for most operations
        self.policies['standard'] = RetryPolicy(
            max_attempts=5,
            base_delay_seconds=1.0,
            max_delay_seconds=30.0,
            backoff_multiplier=2.0
        )

        # Aggressive retry for critical operations
        self.policies['aggressive'] = RetryPolicy(
            max_attempts=10,
            base_delay_seconds=0.5,
            max_delay_seconds=60.0,
            backoff_multiplier=1.5
        )

        # Network retry for network operations
        self.policies['network'] = RetryPolicy(
            max_attempts=5,
            base_delay_seconds=2.0,
            max_delay_seconds=60.0,
            backoff_multiplier=2.0,
            retry_on_errors=['ConnectionError', 'TimeoutError', 'HTTPError']
        )

        # Database retry for database operations
        self.policies['database'] = RetryPolicy(
            max_attempts=3,
            base_delay_seconds=1.0,
            max_delay_seconds=10.0,
            backoff_multiplier=2.0,
            retry_on_errors=['DatabaseError', 'ConnectionError', 'OperationalError']
        )

    def get_executor(self, name: str = "default") -> RetryExecutor:
        """Get or create retry executor"""
        if name not in self.executors:
            self.executors[name] = RetryExecutor(name)
            self.logger.info(f"Created retry executor '{name}'")

        return self.executors[name]

    def get_policy(self, name: str) -> Optional[RetryPolicy]:
        """Get retry policy by name"""
        return self.policies.get(name)

    def create_policy(self,
                     name: str,
                     max_attempts: int = 3,
                     base_delay: float = 1.0,
                     max_delay: float = 30.0,
                     backoff_multiplier: float = 2.0,
                     retry_on_errors: Optional[List[str]] = None,
                     no_retry_on_errors: Optional[List[str]] = None) -> RetryPolicy:
        """Create and register a new retry policy"""
        policy = RetryPolicy(
            max_attempts=max_attempts,
            base_delay_seconds=base_delay,
            max_delay_seconds=max_delay,
            backoff_multiplier=backoff_multiplier,
            retry_on_errors=retry_on_errors or [],
            no_retry_on_errors=no_retry_on_errors or []
        )

        self.policies[name] = policy
        self.logger.info(f"Created retry policy '{name}'")
        return policy

    async def execute_with_retry(self,
                                func: Callable,
                                policy_name: str = "standard",
                                strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
                                executor_name: str = "default",
                                *args, **kwargs) -> RetryResult:
        """
        Execute function with retry using named policy and executor.

        Args:
            func: Function to execute
            policy_name: Name of retry policy to use
            strategy: Retry strategy
            executor_name: Name of retry executor to use
            *args, **kwargs: Arguments for the function

        Returns:
            RetryResult: Result of retry operation
        """
        executor = self.get_executor(executor_name)
        policy = self.get_policy(policy_name)

        if not policy:
            raise ValueError(f"Unknown retry policy: {policy_name}")

        return await executor.execute_with_retry(func, policy, strategy, *args, **kwargs)

    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get metrics for all executors"""
        return {name: executor.get_metrics() for name, executor in self.executors.items()}

    def get_summary(self) -> Dict[str, Any]:
        """Get summary of retry manager state"""
        total_executions = sum(executor.total_executions for executor in self.executors.values())
        total_successes = sum(executor.successful_executions for executor in self.executors.values())

        success_rate = (total_successes / total_executions * 100) if total_executions > 0 else 0

        return {
            'total_executors': len(self.executors),
            'total_policies': len(self.policies),
            'total_executions': total_executions,
            'overall_success_rate_percent': round(success_rate, 2),
            'available_policies': list(self.policies.keys()),
            'active_executors': list(self.executors.keys())
        }


# Global retry manager instance
retry_manager = RetryManager()


# Decorator for easy retry integration
def with_retry(policy_name: str = "standard",
              strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
              executor_name: str = "default"):
    """
    Decorator to automatically add retry behavior to functions.

    Usage:
        @with_retry("network", RetryStrategy.JITTERED_EXPONENTIAL)
        async def call_api():
            # API call code
            pass
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            result = await retry_manager.execute_with_retry(
                func, policy_name, strategy, executor_name, *args, **kwargs
            )

            if not result.success:
                # Re-raise the last error if all retries failed
                raise result.error

            return result.result

        # Preserve function metadata
        wrapper.__name__ = func.__name__
        wrapper.__doc__ = func.__doc__
        wrapper.retry_policy = policy_name
        wrapper.retry_strategy = strategy
        wrapper.retry_executor = executor_name

        return wrapper

    return decorator