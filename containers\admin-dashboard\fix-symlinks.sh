#!/bin/bash
# Symlink repair script for Next.js development container
# This script ensures npm executable symlinks are properly created

set -e

echo "🔧 Starting symlink repair process..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Run this script from the project root."
    exit 1
fi

# Check current state
echo "📋 Current node_modules/.bin contents:"
ls -la node_modules/.bin/ || echo "❌ .bin directory not found"

# Function to create symlinks manually if needed
create_symlinks() {
    echo "🔨 Creating symlinks manually..."
    
    # Create .bin directory if it doesn't exist
    mkdir -p node_modules/.bin
    
    # Find all packages with bin entries and create symlinks
    for pkg_dir in node_modules/*/; do
        if [ -f "${pkg_dir}package.json" ]; then
            # Extract bin entries from package.json
            node -e "
                const pkg = require('./${pkg_dir}package.json');
                if (pkg.bin) {
                    const bins = typeof pkg.bin === 'string' ? {[pkg.name]: pkg.bin} : pkg.bin;
                    Object.entries(bins).forEach(([name, path]) => {
                        const fs = require('fs');
                        const srcPath = './${pkg_dir}' + path;
                        const destPath = './node_modules/.bin/' + name;
                        
                        try {
                            if (fs.existsSync(srcPath)) {
                                // Remove existing symlink/file
                                if (fs.existsSync(destPath)) {
                                    fs.unlinkSync(destPath);
                                }
                                // Create symlink
                                fs.symlinkSync('../' + pkg.name + '/' + path, destPath);
                                // Make executable
                                fs.chmodSync(destPath, 0o755);
                                console.log('✅ Created symlink:', name);
                            }
                        } catch (err) {
                            console.log('⚠️  Failed to create symlink for', name, ':', err.message);
                        }
                    });
                }
            " 2>/dev/null || echo "⚠️  Manual symlink creation encountered issues"
        fi
    done
}

# Method 1: Try npm rebuild
echo "🔄 Attempting npm rebuild..."
if npm rebuild --legacy-peer-deps; then
    echo "✅ npm rebuild completed"
else
    echo "⚠️  npm rebuild failed, continuing..."
fi

# Check if symlinks were created
if [ -x "node_modules/.bin/next" ]; then
    echo "✅ next executable found and is executable"
else
    echo "❌ next executable not found or not executable"
    
    # Method 2: Try reinstalling specific packages
    echo "🔄 Attempting to reinstall Next.js..."
    npm install next@latest --legacy-peer-deps || echo "⚠️  Next.js reinstall failed"
    
    # Method 3: Manual symlink creation
    if [ ! -x "node_modules/.bin/next" ]; then
        echo "🔧 Attempting manual symlink creation..."
        create_symlinks
    fi
fi

# Final verification
echo "📋 Final verification:"
if [ -x "node_modules/.bin/next" ]; then
    echo "✅ SUCCESS: next executable is ready"
    echo "📋 Available executables:"
    ls -la node_modules/.bin/ | grep -E '^-|^l' || echo "No executables found"
    
    # Test the executable
    echo "🧪 Testing next executable..."
    if node_modules/.bin/next --version; then
        echo "✅ next executable works correctly"
    else
        echo "⚠️  next executable exists but may have issues"
    fi
else
    echo "❌ FAILED: next executable still not available"
    echo "💡 Manual intervention may be required"
    exit 1
fi

echo "🎉 Symlink repair completed successfully!"
