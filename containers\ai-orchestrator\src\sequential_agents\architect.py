"""
Sequential Agents - <PERSON><PERSON><PERSON> (Project Manager)

Performs multi-step planning using the LLM and delegates work by creating
specialist tasks in the database.

Flow:
1) Analyze user request (goal)
2) LLM Call #1: Produce a structured JSON plan assigning steps to agent roles
3) Parse plan and create pending tasks in DB via TaskRepository
4) Return the plan and created task IDs
"""
from __future__ import annotations

import json
import re
from typing import Any, Dict, List

from .base import BaseAgent
from ..router.llm_router import get_llm_service
from ..models.llm_models import GenerateRequest, LLMResponse
from ..repository.task_repository import TaskRepository
from ..models.database import SessionLocal


class ArchitectAgent(BaseAgent):
    """Architect that plans and delegates work to specialist agents."""

    def __init__(self) -> None:
        super().__init__()
        self._llm = None

    async def _get_llm(self):
        if self._llm is None:
            self._llm = await get_llm_service()
        return self._llm

    @staticmethod
    def _planning_prompt(user_goal: str) -> str:
        allowed_roles = ["backend", "frontend", "shell"]
        return (
            "You are the Architect agent (project manager). Break down the user's goal into a step-by-step, "
            "actionable plan. Each step must be assigned to one of the specialist agent roles: "
            f"{allowed_roles}.\n\n"
            "Return ONLY JSON using this schema (no extra text, no markdown, no code fences):\n"
            "{\n"
            "  \"plan\": [\n"
            "    { \"agent_role\": \"backend|frontend|shell\", \"task_description\": \"string\" }\n"
            "  ]\n"
            "}\n\n"
            f"User goal: {user_goal}\n"
        )

    @staticmethod
    def _extract_json(text: str) -> Dict[str, Any]:
        """Extract JSON object from raw LLM content (handles accidental fences)."""
        # Strip code fences if present
        fence = re.findall(r"```(?:json)?\n(.*?)```", text, flags=re.DOTALL | re.IGNORECASE)
        candidate = fence[0].strip() if fence else text.strip()
        # Try direct parse first
        try:
            return json.loads(candidate)
        except Exception:
            pass
        # Try to locate the first balanced JSON object
        start = candidate.find("{")
        end = candidate.rfind("}")
        if start != -1 and end != -1 and end > start:
            try:
                return json.loads(candidate[start : end + 1])
            except Exception:
                pass
        # As a last resort, return empty plan
        return {"plan": []}

    @staticmethod
    def _normalize_plan(plan_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Validate and normalize the plan entries."""
        normalized: List[Dict[str, str]] = []
        raw_list = plan_data.get("plan")
        if not isinstance(raw_list, list):
            return normalized
        allowed = {"backend", "frontend", "shell"}
        for step in raw_list:
            if not isinstance(step, dict):
                continue
            role = str(step.get("agent_role", "")).strip().lower()
            desc = str(step.get("task_description", "")).strip()
            if role in allowed and desc:
                normalized.append({"agent_role": role, "task_description": desc})
        return normalized

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Plan and delegate tasks for the given user request."""
        # 1) Analyze request
        user_goal = (
            task_input.get("goal")
            or task_input.get("feature")
            or task_input.get("request")
            or task_input.get("description")
            or "Build requested feature"
        )
        project_id = task_input.get("project_id")
        if not isinstance(project_id, int):
            return {
                "agent": "architect",
                "status": "invalid_input",
                "error": "'project_id' (int) is required to create tasks",
            }

        # 2) LLM Call #1: Step-by-step plan
        llm = await self._get_llm()
        prompt = self._planning_prompt(str(user_goal))
        req = GenerateRequest(prompt=prompt)
        resp: LLMResponse = await llm.generate(req, user_id="architect-agent")

        # 3) Parse JSON plan and create tasks
        plan_data = self._extract_json(resp.content)
        plan = self._normalize_plan(plan_data)

        db = SessionLocal()
        created_task_ids: List[int] = []
        try:
            for step in plan:
                # Minimal viable input_data; downstream agents can interpret 'feature'
                input_data = {
                    "feature": step["task_description"],
                    "source": "architect_plan",
                    "goal": user_goal,
                }
                task = await TaskRepository.create_task(
                    db,
                    project_id=project_id,
                    agent_role=step["agent_role"],
                    input_data=input_data,
                )
                created_task_ids.append(task.id)
        finally:
            db.close()

        # 4) Report plan
        return {
            "agent": "architect",
            "status": "success",
            "goal": user_goal,
            "plan": plan,
            "created_task_ids": created_task_ids,
            "llm": {
                "model": resp.model,
                "provider": resp.provider.value,
            },
        }