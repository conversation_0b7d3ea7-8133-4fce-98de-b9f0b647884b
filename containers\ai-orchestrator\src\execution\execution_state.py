# Project: AI Coding Agent
# Purpose: Execution state management for pipeline tracking and persistence

import asyncio
import json
import logging
import pickle
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from contextlib import asynccontextmanager

from ..models.validation_models import (
    ExecutionState, ExecutionPipeline, ExecutionStatus, StateSnapshot,
    RollbackPlan, ValidationResult
)


class ExecutionStateManager:
    """
    Manages execution state persistence and recovery for pipeline execution.

    Provides:
    - State persistence and serialization
    - State recovery and restoration
    - Progress tracking and monitoring
    - State snapshots for rollback capability
    - Cross-session state continuity
    """

    def __init__(self, state_directory: str = "/workspace/.execution_state"):
        self.logger = logging.getLogger("execution_state_manager")
        self.state_directory = Path(state_directory)

        # Active execution states
        self.execution_states: Dict[str, ExecutionState] = {}
        self.state_locks: Dict[str, asyncio.Lock] = {}

        # State persistence configuration
        self.auto_save_interval = 30  # seconds
        self.max_snapshots_per_execution = 20

        # State snapshots for rollback
        self.state_snapshots: Dict[str, List[StateSnapshot]] = {}

        # Initialize state directory
        self._initialize_state_directory()

        # Start auto-save background task
        self._auto_save_task = None

        self.logger.info("Execution State Manager initialized")

    def _initialize_state_directory(self):
        """Initialize state directory structure"""
        try:
            self.state_directory.mkdir(parents=True, exist_ok=True)

            # Create subdirectories
            (self.state_directory / "active").mkdir(exist_ok=True)
            (self.state_directory / "snapshots").mkdir(exist_ok=True)
            (self.state_directory / "archived").mkdir(exist_ok=True)

            self.logger.info(f"State directory initialized: {self.state_directory}")

        except Exception as e:
            self.logger.error(f"Failed to initialize state directory: {str(e)}")
            raise

    async def create_execution_state(self,
                                   pipeline_id: str,
                                   pipeline: ExecutionPipeline) -> ExecutionState:
        """
        Create new execution state for pipeline.

        Args:
            pipeline_id: ID of the pipeline
            pipeline: Pipeline configuration

        Returns:
            ExecutionState: New execution state
        """

        execution_state = ExecutionState(
            pipeline_id=pipeline_id,
            current_phase="initialization",
            variables={
                "pipeline_name": pipeline.name,
                "user_id": pipeline.user_id,
                "roadmap_id": pipeline.roadmap_id,
                "strict_mode": pipeline.strict_mode
            }
        )

        # Store state
        self.execution_states[pipeline_id] = execution_state
        self.state_locks[pipeline_id] = asyncio.Lock()

        # Persist state
        await self._save_execution_state(pipeline_id)

        self.logger.info(f"Created execution state for pipeline: {pipeline_id}")
        return execution_state

    async def get_execution_state(self, pipeline_id: str) -> Optional[ExecutionState]:
        """Get execution state for pipeline"""

        if pipeline_id in self.execution_states:
            return self.execution_states[pipeline_id]

        # Try to load from persistence
        loaded_state = await self._load_execution_state(pipeline_id)
        if loaded_state:
            self.execution_states[pipeline_id] = loaded_state
            self.state_locks[pipeline_id] = asyncio.Lock()
            return loaded_state

        return None

    @asynccontextmanager
    async def state_transaction(self, pipeline_id: str):
        """Context manager for atomic state updates"""

        if pipeline_id not in self.state_locks:
            self.state_locks[pipeline_id] = asyncio.Lock()

        async with self.state_locks[pipeline_id]:
            try:
                # Create snapshot before modification
                await self._create_state_snapshot(pipeline_id, "transaction_start")

                yield self.execution_states.get(pipeline_id)

                # Save state after successful modification
                await self._save_execution_state(pipeline_id)

            except Exception as e:
                # Rollback on error
                await self._rollback_to_latest_snapshot(pipeline_id)
                self.logger.error(f"State transaction failed for {pipeline_id}: {str(e)}")
                raise

    async def update_execution_progress(self,
                                      pipeline_id: str,
                                      progress_percentage: float,
                                      current_phase: str,
                                      current_stage_id: Optional[str] = None,
                                      additional_context: Optional[Dict[str, Any]] = None):
        """Update execution progress"""

        async with self.state_transaction(pipeline_id) as state:
            if not state:
                raise ValueError(f"Execution state not found: {pipeline_id}")

            state.progress_percentage = progress_percentage
            state.current_phase = current_phase

            if current_stage_id:
                state.current_stage_id = current_stage_id

            if additional_context:
                state.variables.update(additional_context)

            state.updated_at = datetime.now()

            self.logger.debug(f"Updated execution progress for {pipeline_id}: {progress_percentage:.1f}%")

    async def set_execution_status(self,
                                 pipeline_id: str,
                                 status: ExecutionStatus,
                                 error_context: Optional[Dict[str, Any]] = None):
        """Set execution status"""

        async with self.state_transaction(pipeline_id) as state:
            if not state:
                raise ValueError(f"Execution state not found: {pipeline_id}")

            state.status = status

            if error_context:
                state.error_context = error_context

            state.updated_at = datetime.now()

            self.logger.info(f"Set execution status for {pipeline_id}: {status.value}")

    async def add_execution_artifact(self,
                                   pipeline_id: str,
                                   artifact_name: str,
                                   artifact_path: str):
        """Add execution artifact"""

        async with self.state_transaction(pipeline_id) as state:
            if not state:
                raise ValueError(f"Execution state not found: {pipeline_id}")

            state.artifacts[artifact_name] = artifact_path
            state.updated_at = datetime.now()

            self.logger.debug(f"Added artifact for {pipeline_id}: {artifact_name} -> {artifact_path}")

    async def create_checkpoint(self,
                              pipeline_id: str,
                              checkpoint_name: str) -> str:
        """Create execution checkpoint"""

        state = await self.get_execution_state(pipeline_id)
        if not state:
            raise ValueError(f"Execution state not found: {pipeline_id}")

        # Create state snapshot
        snapshot = await self._create_state_snapshot(pipeline_id, checkpoint_name)

        # Update state with checkpoint reference
        async with self.state_transaction(pipeline_id) as state:
            state.last_checkpoint_id = snapshot.id
            state.variables[f"checkpoint_{checkpoint_name}"] = snapshot.id

        self.logger.info(f"Created checkpoint for {pipeline_id}: {checkpoint_name} -> {snapshot.id}")
        return snapshot.id

    async def rollback_to_checkpoint(self,
                                   pipeline_id: str,
                                   checkpoint_id: str) -> bool:
        """Rollback to specific checkpoint"""

        try:
            # Find snapshot
            snapshot = await self._get_state_snapshot(pipeline_id, checkpoint_id)
            if not snapshot:
                self.logger.error(f"Checkpoint not found: {checkpoint_id}")
                return False

            # Restore state from snapshot
            restored_state = snapshot.execution_state
            restored_state.updated_at = datetime.now()

            # Update current state
            async with self.state_transaction(pipeline_id) as current_state:
                if current_state:
                    # Preserve some current metadata
                    current_metadata = {
                        "rollback_performed_at": datetime.now().isoformat(),
                        "rollback_from_checkpoint": checkpoint_id,
                        "previous_status": current_state.status.value
                    }

                    # Update state
                    self.execution_states[pipeline_id] = restored_state
                    restored_state.variables.update(current_metadata)

            self.logger.info(f"Rolled back execution state for {pipeline_id} to checkpoint: {checkpoint_id}")
            return True

        except Exception as e:
            self.logger.error(f"Rollback failed for {pipeline_id}: {str(e)}")
            return False

    async def get_execution_summary(self, pipeline_id: str) -> Dict[str, Any]:
        """Get execution summary"""

        state = await self.get_execution_state(pipeline_id)
        if not state:
            return {}

        # Calculate additional metrics
        execution_duration = None
        if "started_at" in state.variables:
            try:
                started_at = datetime.fromisoformat(state.variables["started_at"])
                execution_duration = (datetime.now() - started_at).total_seconds()
            except Exception:
                pass

        # Get available snapshots
        snapshots = self.state_snapshots.get(pipeline_id, [])

        return {
            "pipeline_id": pipeline_id,
            "status": state.status.value,
            "current_phase": state.current_phase,
            "progress_percentage": state.progress_percentage,
            "current_stage_id": state.current_stage_id,
            "last_checkpoint_id": state.last_checkpoint_id,
            "execution_duration_seconds": execution_duration,
            "artifacts_count": len(state.artifacts),
            "variables_count": len(state.variables),
            "snapshots_count": len(snapshots),
            "last_updated": state.updated_at.isoformat(),
            "has_error_context": state.error_context is not None
        }

    async def cleanup_execution_state(self, pipeline_id: str, archive: bool = True):
        """Clean up execution state"""

        try:
            if archive:
                # Archive state before cleanup
                await self._archive_execution_state(pipeline_id)

            # Remove from memory
            self.execution_states.pop(pipeline_id, None)
            self.state_locks.pop(pipeline_id, None)
            self.state_snapshots.pop(pipeline_id, None)

            # Remove active state file
            state_file = self.state_directory / "active" / f"{pipeline_id}.json"
            if state_file.exists():
                state_file.unlink()

            # Clean up snapshot files
            snapshot_dir = self.state_directory / "snapshots" / pipeline_id
            if snapshot_dir.exists():
                for snapshot_file in snapshot_dir.glob("*.json"):
                    snapshot_file.unlink()
                snapshot_dir.rmdir()

            self.logger.info(f"Cleaned up execution state for pipeline: {pipeline_id}")

        except Exception as e:
            self.logger.error(f"Failed to cleanup execution state for {pipeline_id}: {str(e)}")

    # Private methods for state persistence

    async def _save_execution_state(self, pipeline_id: str):
        """Save execution state to disk"""

        state = self.execution_states.get(pipeline_id)
        if not state:
            return

        try:
            state_file = self.state_directory / "active" / f"{pipeline_id}.json"

            # Serialize state
            state_data = {
                "pipeline_id": state.pipeline_id,
                "current_stage_id": state.current_stage_id,
                "current_phase": state.current_phase,
                "progress_percentage": state.progress_percentage,
                "variables": state.variables,
                "artifacts": state.artifacts,
                "serialized_state": state.serialized_state,
                "last_checkpoint_id": state.last_checkpoint_id,
                "status": state.status.value,
                "error_context": state.error_context,
                "updated_at": state.updated_at.isoformat()
            }

            with open(state_file, 'w') as f:
                json.dump(state_data, f, indent=2, default=str)

        except Exception as e:
            self.logger.error(f"Failed to save execution state for {pipeline_id}: {str(e)}")

    async def _load_execution_state(self, pipeline_id: str) -> Optional[ExecutionState]:
        """Load execution state from disk"""

        try:
            state_file = self.state_directory / "active" / f"{pipeline_id}.json"

            if not state_file.exists():
                return None

            with open(state_file, 'r') as f:
                state_data = json.load(f)

            # Deserialize state
            execution_state = ExecutionState(
                pipeline_id=state_data["pipeline_id"],
                current_stage_id=state_data.get("current_stage_id"),
                current_phase=state_data["current_phase"],
                progress_percentage=state_data["progress_percentage"],
                variables=state_data["variables"],
                artifacts=state_data["artifacts"],
                serialized_state=state_data["serialized_state"],
                last_checkpoint_id=state_data.get("last_checkpoint_id"),
                status=ExecutionStatus(state_data["status"]),
                error_context=state_data.get("error_context"),
                updated_at=datetime.fromisoformat(state_data["updated_at"])
            )

            return execution_state

        except Exception as e:
            self.logger.error(f"Failed to load execution state for {pipeline_id}: {str(e)}")
            return None

    async def _create_state_snapshot(self,
                                   pipeline_id: str,
                                   snapshot_type: str) -> StateSnapshot:
        """Create state snapshot for rollback"""

        state = self.execution_states.get(pipeline_id)
        if not state:
            raise ValueError(f"Execution state not found: {pipeline_id}")

        # Create snapshot
        snapshot = StateSnapshot(
            checkpoint_id=f"{pipeline_id}_{snapshot_type}_{int(datetime.now().timestamp())}",
            execution_state=state.copy() if hasattr(state, 'copy') else state
        )

        # Store snapshot
        if pipeline_id not in self.state_snapshots:
            self.state_snapshots[pipeline_id] = []

        self.state_snapshots[pipeline_id].append(snapshot)

        # Limit number of snapshots
        if len(self.state_snapshots[pipeline_id]) > self.max_snapshots_per_execution:
            # Remove oldest snapshot
            removed_snapshot = self.state_snapshots[pipeline_id].pop(0)
            await self._delete_snapshot_file(pipeline_id, removed_snapshot.id)

        # Save snapshot to disk
        await self._save_state_snapshot(pipeline_id, snapshot)

        return snapshot

    async def _save_state_snapshot(self, pipeline_id: str, snapshot: StateSnapshot):
        """Save state snapshot to disk"""

        try:
            snapshot_dir = self.state_directory / "snapshots" / pipeline_id
            snapshot_dir.mkdir(parents=True, exist_ok=True)

            snapshot_file = snapshot_dir / f"{snapshot.id}.json"

            # Serialize snapshot (simplified)
            snapshot_data = {
                "id": snapshot.id,
                "checkpoint_id": snapshot.checkpoint_id,
                "created_at": snapshot.created_at.isoformat(),
                "execution_state": {
                    "pipeline_id": snapshot.execution_state.pipeline_id,
                    "current_stage_id": snapshot.execution_state.current_stage_id,
                    "current_phase": snapshot.execution_state.current_phase,
                    "progress_percentage": snapshot.execution_state.progress_percentage,
                    "variables": snapshot.execution_state.variables,
                    "artifacts": snapshot.execution_state.artifacts,
                    "status": snapshot.execution_state.status.value,
                    "updated_at": snapshot.execution_state.updated_at.isoformat()
                }
            }

            with open(snapshot_file, 'w') as f:
                json.dump(snapshot_data, f, indent=2, default=str)

        except Exception as e:
            self.logger.error(f"Failed to save snapshot {snapshot.id}: {str(e)}")

    async def _get_state_snapshot(self,
                                pipeline_id: str,
                                snapshot_id: str) -> Optional[StateSnapshot]:
        """Get state snapshot by ID"""

        # Check in memory first
        snapshots = self.state_snapshots.get(pipeline_id, [])
        for snapshot in snapshots:
            if snapshot.id == snapshot_id:
                return snapshot

        # Try to load from disk
        try:
            snapshot_file = self.state_directory / "snapshots" / pipeline_id / f"{snapshot_id}.json"

            if not snapshot_file.exists():
                return None

            with open(snapshot_file, 'r') as f:
                snapshot_data = json.load(f)

            # Reconstruct snapshot
            execution_state = ExecutionState(**snapshot_data["execution_state"])
            execution_state.status = ExecutionStatus(snapshot_data["execution_state"]["status"])
            execution_state.updated_at = datetime.fromisoformat(
                snapshot_data["execution_state"]["updated_at"]
            )

            snapshot = StateSnapshot(
                id=snapshot_data["id"],
                checkpoint_id=snapshot_data["checkpoint_id"],
                execution_state=execution_state,
                created_at=datetime.fromisoformat(snapshot_data["created_at"])
            )

            return snapshot

        except Exception as e:
            self.logger.error(f"Failed to load snapshot {snapshot_id}: {str(e)}")
            return None

    async def _rollback_to_latest_snapshot(self, pipeline_id: str):
        """Rollback to latest snapshot"""

        snapshots = self.state_snapshots.get(pipeline_id, [])
        if not snapshots:
            return

        latest_snapshot = snapshots[-1]
        await self.rollback_to_checkpoint(pipeline_id, latest_snapshot.checkpoint_id)

    async def _archive_execution_state(self, pipeline_id: str):
        """Archive execution state"""

        try:
            archive_dir = self.state_directory / "archived"
            archive_file = archive_dir / f"{pipeline_id}_{int(datetime.now().timestamp())}.json"

            state = self.execution_states.get(pipeline_id)
            if state:
                with open(archive_file, 'w') as f:
                    json.dump({
                        "pipeline_id": pipeline_id,
                        "archived_at": datetime.now().isoformat(),
                        "execution_state": state.model_dump()
                    }, f, indent=2, default=str)

        except Exception as e:
            self.logger.error(f"Failed to archive execution state for {pipeline_id}: {str(e)}")

    async def _delete_snapshot_file(self, pipeline_id: str, snapshot_id: str):
        """Delete snapshot file"""

        try:
            snapshot_file = self.state_directory / "snapshots" / pipeline_id / f"{snapshot_id}.json"
            if snapshot_file.exists():
                snapshot_file.unlink()
        except Exception as e:
            self.logger.error(f"Failed to delete snapshot file {snapshot_id}: {str(e)}")

    # Management methods

    def get_active_executions(self) -> List[str]:
        """Get list of active execution IDs"""
        return list(self.execution_states.keys())

    async def get_system_statistics(self) -> Dict[str, Any]:
        """Get execution state manager statistics"""

        active_executions = len(self.execution_states)
        total_snapshots = sum(len(snapshots) for snapshots in self.state_snapshots.values())

        # Calculate storage usage
        try:
            active_files = list((self.state_directory / "active").glob("*.json"))
            snapshot_files = list((self.state_directory / "snapshots").rglob("*.json"))
            archive_files = list((self.state_directory / "archived").glob("*.json"))

            storage_mb = sum(
                f.stat().st_size for f in active_files + snapshot_files + archive_files
            ) / (1024 * 1024)

        except Exception:
            storage_mb = 0.0

        return {
            "active_executions": active_executions,
            "total_snapshots": total_snapshots,
            "storage_usage_mb": round(storage_mb, 2),
            "state_directory": str(self.state_directory),
            "auto_save_interval": self.auto_save_interval,
            "max_snapshots_per_execution": self.max_snapshots_per_execution
        }

    async def start_auto_save(self):
        """Start auto-save background task"""

        if self._auto_save_task:
            return

        async def auto_save_loop():
            while True:
                try:
                    await asyncio.sleep(self.auto_save_interval)

                    # Save all active states
                    for pipeline_id in list(self.execution_states.keys()):
                        await self._save_execution_state(pipeline_id)

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"Auto-save error: {str(e)}")

        self._auto_save_task = asyncio.create_task(auto_save_loop())
        self.logger.info("Auto-save background task started")

    async def stop_auto_save(self):
        """Stop auto-save background task"""

        if self._auto_save_task:
            self._auto_save_task.cancel()
            try:
                await self._auto_save_task
            except asyncio.CancelledError:
                pass
            self._auto_save_task = None

        self.logger.info("Auto-save background task stopped")