# LangGraph Studio Integration Guide for AI Coding Agent

## Overview

LangGraph Studio provides a specialized agent IDE for building, visualizing, and debugging complex AI agent workflows. This guide demonstrates how to integrate LangGraph for orchestrating the AI Coding Agent's validation pipelines, approval workflows, and multi-step code generation processes.

## Table of Contents

1. [Why LangGraph for AI Coding Agent](#why-langgraph)
2. [Core Concepts & Architecture](#core-concepts--architecture)
3. [Validation Pipeline Workflow](#validation-pipeline-workflow)
4. [Human-in-the-Loop Approval System](#human-in-the-loop-approval-system)
5. [Custom State Management](#custom-state-management)
6. [Code Generation Workflows](#code-generation-workflows)
7. [Integration with Existing Stack](#integration-with-existing-stack)
8. [LangGraph Studio Setup](#langgraph-studio-setup)
9. [Production Deployment](#production-deployment)
10. [Monitoring & Debugging](#monitoring--debugging)

## Why LangGraph for AI Coding Agent

### Perfect Alignment with Project Requirements

1. **Multi-layered Validation**: Sequential pipeline execution with validation gates
2. **Human-in-the-Loop**: User-controlled approval workflows for critical operations
3. **State Management**: Checkpoints and rollback for transaction-like behavior
4. **Real-time Monitoring**: Visual debugging and execution tracking
5. **Reliability**: Circuit breakers and error recovery mechanisms

### Key Benefits

- **Visual Workflow Design**: Graph-based representation of complex validation pipelines
- **State Persistence**: Maintain context across long-running workflows
- **Human Approval Gates**: Built-in interrupt/resume functionality
- **Debugging Capabilities**: Time travel and state inspection
- **Streaming Support**: Real-time progress updates via WebSocket

## Core Concepts & Architecture

### LangGraph Integration Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                   LangGraph Orchestrator                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Code Gen    │  │ Validation  │  │ Approval    │        │
│  │ Workflow    │  │ Pipeline    │  │ Workflow    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                    State Management                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Supabase    │  │ Redis       │  │ Memory      │        │
│  │ Persistence │  │ Cache       │  │ Checkpoints │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                    Tool Integration                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Ollama LLM  │  │ Code        │  │ Security    │        │
│  │ Service     │  │ Validators  │  │ Scanners    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### State Schema Design

```python
# src/workflows/state_schemas.py
from typing import Annotated, List, Dict, Any, Optional, Literal
from typing_extensions import TypedDict
from langgraph.graph.message import add_messages
from datetime import datetime
import uuid

# Base state for all workflows
class BaseWorkflowState(TypedDict):
    """Base state for AI Coding Agent workflows."""
    workflow_id: str
    user_id: str
    project_id: str
    created_at: datetime
    updated_at: datetime
    status: Literal["pending", "running", "completed", "failed", "cancelled"]
    error_message: Optional[str]
    messages: Annotated[List[Dict[str, Any]], add_messages]

# Code generation workflow state
class CodeGenerationState(BaseWorkflowState):
    """State for code generation workflows."""
    prompt: str
    language: str
    context_documents: List[str]
    generated_code: Optional[str]
    validation_results: Dict[str, Any]
    approval_status: Optional[Literal["pending", "approved", "rejected"]]
    approval_feedback: Optional[str]

# Validation pipeline state
class ValidationPipelineState(BaseWorkflowState):
    """State for multi-layered validation."""
    code_content: str
    file_path: str
    language: str
    validation_stages: Dict[str, Dict[str, Any]]  # syntax, security, functionality, integration
    current_stage: str
    passed_stages: List[str]
    failed_stages: List[str]
    overall_score: Optional[float]
    recommendations: List[str]

# Approval workflow state
class ApprovalWorkflowState(BaseWorkflowState):
    """State for human approval workflows."""
    operation_type: str
    operation_context: Dict[str, Any]
    risk_level: Literal["low", "medium", "high", "critical"]
    required_approvers: List[str]
    approvals_received: List[Dict[str, Any]]
    auto_approve_conditions: Dict[str, Any]
    timeout_minutes: int
    escalation_rules: List[Dict[str, Any]]
```

## Validation Pipeline Workflow

### Multi-Stage Validation Graph

```python
# src/workflows/validation_pipeline.py
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolNode
from langchain_core.tools import tool
from langgraph.types import Command, interrupt

import asyncio
import logging

logger = logging.getLogger(__name__)

# Validation tools
@tool
async def syntax_validator(code: str, language: str) -> Dict[str, Any]:
    """Validate code syntax."""
    # Integration with existing validation service
    from ..services.validation_service import ValidationService

    validator = ValidationService()
    result = await validator.validate_syntax(code, language)

    return {
        "stage": "syntax",
        "passed": result.get("valid", False),
        "score": result.get("score", 0.0),
        "issues": result.get("issues", []),
        "details": result
    }

@tool
async def security_scanner(code: str, language: str) -> Dict[str, Any]:
    """Scan code for security vulnerabilities."""
    from ..services.security_service import SecurityService

    scanner = SecurityService()
    result = await scanner.scan_vulnerabilities(code, language)

    return {
        "stage": "security",
        "passed": len(result.get("vulnerabilities", [])) == 0,
        "score": result.get("security_score", 0.0),
        "issues": result.get("vulnerabilities", []),
        "details": result
    }

@tool
async def functionality_tester(code: str, language: str, test_cases: List[Dict]) -> Dict[str, Any]:
    """Run functionality tests on code."""
    from ..services.testing_service import TestingService

    tester = TestingService()
    result = await tester.run_tests(code, language, test_cases)

    return {
        "stage": "functionality",
        "passed": result.get("all_passed", False),
        "score": result.get("pass_rate", 0.0),
        "issues": result.get("failed_tests", []),
        "details": result
    }

@tool
async def integration_validator(code: str, project_context: Dict) -> Dict[str, Any]:
    """Validate code integration with project."""
    from ..services.integration_service import IntegrationService

    integrator = IntegrationService()
    result = await integrator.validate_integration(code, project_context)

    return {
        "stage": "integration",
        "passed": result.get("compatible", False),
        "score": result.get("compatibility_score", 0.0),
        "issues": result.get("incompatibilities", []),
        "details": result
    }

# Validation workflow nodes
def validation_coordinator(state: ValidationPipelineState) -> ValidationPipelineState:
    """Coordinate validation pipeline execution."""
    stages = ["syntax", "security", "functionality", "integration"]

    if not state.get("current_stage"):
        # Start with syntax validation
        state["current_stage"] = "syntax"
        state["validation_stages"] = {}
        state["passed_stages"] = []
        state["failed_stages"] = []

    return state

def syntax_validation_node(state: ValidationPipelineState) -> ValidationPipelineState:
    """Execute syntax validation stage."""
    if state["current_stage"] != "syntax":
        return state

    # This will be handled by the tool node
    return state

def security_validation_node(state: ValidationPipelineState) -> ValidationPipelineState:
    """Execute security validation stage."""
    if state["current_stage"] != "security":
        return state

    return state

def functionality_validation_node(state: ValidationPipelineState) -> ValidationPipelineState:
    """Execute functionality validation stage."""
    if state["current_stage"] != "functionality":
        return state

    return state

def integration_validation_node(state: ValidationPipelineState) -> ValidationPipelineState:
    """Execute integration validation stage."""
    if state["current_stage"] != "integration":
        return state

    return state

def validation_decision_node(state: ValidationPipelineState) -> ValidationPipelineState:
    """Make decisions based on validation results."""
    current_stage = state["current_stage"]
    stage_result = state["validation_stages"].get(current_stage, {})

    if stage_result.get("passed", False):
        state["passed_stages"].append(current_stage)

        # Move to next stage
        stages = ["syntax", "security", "functionality", "integration"]
        current_index = stages.index(current_stage)

        if current_index < len(stages) - 1:
            state["current_stage"] = stages[current_index + 1]
            state["status"] = "running"
        else:
            # All stages completed
            state["status"] = "completed"
            state["overall_score"] = sum(
                stage["score"] for stage in state["validation_stages"].values()
            ) / len(state["validation_stages"])
    else:
        state["failed_stages"].append(current_stage)

        # Check if this is a critical failure
        if stage_result.get("score", 0) < 0.3:  # Critical failure threshold
            state["status"] = "failed"
            state["error_message"] = f"Critical failure in {current_stage} validation"
        else:
            # Continue to next stage with warning
            stages = ["syntax", "security", "functionality", "integration"]
            current_index = stages.index(current_stage)

            if current_index < len(stages) - 1:
                state["current_stage"] = stages[current_index + 1]
                state["status"] = "running"
            else:
                state["status"] = "completed"

    return state

def validation_router(state: ValidationPipelineState) -> str:
    """Route to appropriate validation stage."""
    current_stage = state.get("current_stage")

    if state.get("status") == "completed":
        return "end"
    elif state.get("status") == "failed":
        return "end"
    elif current_stage == "syntax":
        return "syntax_validation"
    elif current_stage == "security":
        return "security_validation"
    elif current_stage == "functionality":
        return "functionality_validation"
    elif current_stage == "integration":
        return "integration_validation"
    else:
        return "end"

# Build validation pipeline graph
def create_validation_pipeline() -> StateGraph:
    """Create the validation pipeline workflow."""

    # Tools for validation stages
    validation_tools = [syntax_validator, security_scanner, functionality_tester, integration_validator]

    # Create state graph
    workflow = StateGraph(ValidationPipelineState)

    # Add nodes
    workflow.add_node("coordinator", validation_coordinator)
    workflow.add_node("syntax_validation", ToolNode([syntax_validator]))
    workflow.add_node("security_validation", ToolNode([security_scanner]))
    workflow.add_node("functionality_validation", ToolNode([functionality_tester]))
    workflow.add_node("integration_validation", ToolNode([integration_validator]))
    workflow.add_node("decision", validation_decision_node)

    # Add edges
    workflow.add_edge(START, "coordinator")
    workflow.add_conditional_edges("coordinator", validation_router)
    workflow.add_edge("syntax_validation", "decision")
    workflow.add_edge("security_validation", "decision")
    workflow.add_edge("functionality_validation", "decision")
    workflow.add_edge("integration_validation", "decision")
    workflow.add_conditional_edges("decision", validation_router)

    return workflow

# Initialize validation pipeline
def get_validation_pipeline():
    """Get compiled validation pipeline with checkpointing."""
    workflow = create_validation_pipeline()

    # Use Supabase for persistence in production
    # For now, using memory checkpointer
    memory = MemorySaver()

    return workflow.compile(checkpointer=memory)
```

## Human-in-the-Loop Approval System

### Approval Workflow Implementation

```python
# src/workflows/approval_workflow.py
from langgraph.graph import StateGraph, START, END
from langgraph.types import Command, interrupt
from langchain_core.tools import tool
from datetime import datetime, timedelta
import asyncio

@tool
async def risk_assessor(operation_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Assess risk level of operation."""
    risk_rules = {
        "code_deployment": {"base_risk": "medium", "factors": ["target_env", "code_complexity"]},
        "database_migration": {"base_risk": "high", "factors": ["data_volume", "downtime"]},
        "config_change": {"base_risk": "low", "factors": ["scope", "reversibility"]},
        "user_deletion": {"base_risk": "critical", "factors": ["user_type", "data_impact"]},
    }

    rule = risk_rules.get(operation_type, {"base_risk": "medium", "factors": []})

    # Simple risk calculation (enhance with ML model)
    risk_score = 0.5  # Base medium risk
    for factor in rule["factors"]:
        if factor in context:
            risk_score += context[factor] * 0.1

    risk_level = "low"
    if risk_score > 0.8:
        risk_level = "critical"
    elif risk_score > 0.6:
        risk_level = "high"
    elif risk_score > 0.4:
        risk_level = "medium"

    return {
        "risk_level": risk_level,
        "risk_score": risk_score,
        "factors_considered": rule["factors"],
        "assessment_time": datetime.utcnow().isoformat()
    }

@tool
async def human_approver(
    operation_type: str,
    risk_level: str,
    context: Dict[str, Any],
    required_approvers: List[str]
) -> Dict[str, Any]:
    """Request human approval for operation."""

    approval_request = {
        "operation_type": operation_type,
        "risk_level": risk_level,
        "context": context,
        "required_approvers": required_approvers,
        "timestamp": datetime.utcnow().isoformat(),
        "timeout_minutes": 30 if risk_level == "critical" else 60
    }

    # Interrupt for human input
    human_response = interrupt(approval_request)

    return {
        "approved": human_response.get("approved", False),
        "approver_id": human_response.get("approver_id"),
        "feedback": human_response.get("feedback", ""),
        "approval_time": datetime.utcnow().isoformat(),
        "escalated": human_response.get("escalated", False)
    }

def approval_coordinator(state: ApprovalWorkflowState) -> ApprovalWorkflowState:
    """Coordinate approval workflow."""
    if not state.get("status"):
        state["status"] = "pending"
        state["approvals_received"] = []

    return state

def risk_assessment_node(state: ApprovalWorkflowState) -> ApprovalWorkflowState:
    """Assess risk level of operation."""
    # This will be handled by the risk_assessor tool
    return state

def auto_approval_check(state: ApprovalWorkflowState) -> ApprovalWorkflowState:
    """Check if operation can be auto-approved."""
    risk_level = state.get("risk_level", "medium")
    operation_type = state.get("operation_type", "")

    # Auto-approval rules
    auto_approve_conditions = state.get("auto_approve_conditions", {})

    can_auto_approve = (
        risk_level == "low" and
        operation_type in auto_approve_conditions.get("allowed_operations", []) and
        len(state.get("approvals_received", [])) == 0  # No prior approvals needed
    )

    if can_auto_approve:
        state["status"] = "approved"
        state["approvals_received"].append({
            "type": "auto",
            "timestamp": datetime.utcnow().isoformat(),
            "reason": "Low risk operation with auto-approval enabled"
        })

    return state

def human_approval_node(state: ApprovalWorkflowState) -> ApprovalWorkflowState:
    """Request human approval."""
    # This will be handled by the human_approver tool
    return state

def approval_decision_node(state: ApprovalWorkflowState) -> ApprovalWorkflowState:
    """Make final approval decision."""
    approvals = state.get("approvals_received", [])
    required_approvers = state.get("required_approvers", [])

    # Check if we have enough approvals
    approved_count = sum(1 for approval in approvals if approval.get("approved", False))
    required_count = len(required_approvers)

    if approved_count >= required_count:
        state["status"] = "approved"
    elif any(approval.get("escalated", False) for approval in approvals):
        state["status"] = "escalated"
    else:
        # Check timeout
        created_time = state.get("created_at")
        timeout_minutes = state.get("timeout_minutes", 60)

        if created_time and datetime.utcnow() > created_time + timedelta(minutes=timeout_minutes):
            state["status"] = "timeout"
        else:
            state["status"] = "pending"

    return state

def approval_router(state: ApprovalWorkflowState) -> str:
    """Route approval workflow."""
    status = state.get("status")

    if status in ["approved", "rejected", "timeout", "escalated"]:
        return "end"
    elif not state.get("risk_level"):
        return "risk_assessment"
    elif status == "pending" and not state.get("approvals_received"):
        return "auto_approval_check"
    elif state.get("risk_level") in ["medium", "high", "critical"]:
        return "human_approval"
    else:
        return "decision"

def create_approval_workflow() -> StateGraph:
    """Create human approval workflow."""

    workflow = StateGraph(ApprovalWorkflowState)

    # Add nodes
    workflow.add_node("coordinator", approval_coordinator)
    workflow.add_node("risk_assessment", ToolNode([risk_assessor]))
    workflow.add_node("auto_approval_check", auto_approval_check)
    workflow.add_node("human_approval", ToolNode([human_approver]))
    workflow.add_node("decision", approval_decision_node)

    # Add edges
    workflow.add_edge(START, "coordinator")
    workflow.add_conditional_edges("coordinator", approval_router)
    workflow.add_edge("risk_assessment", "auto_approval_check")
    workflow.add_conditional_edges("auto_approval_check", approval_router)
    workflow.add_edge("human_approval", "decision")
    workflow.add_conditional_edges("decision", approval_router)

    return workflow
```

## Code Generation Workflows

### RAG-Enhanced Code Generation

```python
# src/workflows/code_generation.py
from langgraph.graph import StateGraph, START, END
from langchain_core.tools import tool
from ..services.rag_service import RAGService
from ..services.llm_service import LLMService

@tool
async def context_retriever(prompt: str, project_id: str, user_jwt: str) -> Dict[str, Any]:
    """Retrieve relevant context for code generation."""
    rag_service = RAGService()

    context = await rag_service.get_relevant_context(
        prompt=prompt,
        project_id=project_id,
        user_jwt=user_jwt,
        max_docs=5
    )

    return {
        "context_documents": context.get("documents", []),
        "context_text": context.get("formatted_context", ""),
        "relevance_scores": context.get("scores", [])
    }

@tool
async def code_generator(
    prompt: str,
    context: str,
    language: str,
    style_preferences: Dict[str, Any]
) -> Dict[str, Any]:
    """Generate code using LLM with context."""
    llm_service = LLMService()

    enhanced_prompt = f"""
    Context from codebase:
    {context}

    User Request:
    {prompt}

    Language: {language}
    Style Preferences: {style_preferences}

    Generate clean, well-documented code that follows the patterns from the context.
    """

    generated_code = await llm_service.generate_code(
        prompt=enhanced_prompt,
        language=language,
        max_tokens=2000
    )

    return {
        "generated_code": generated_code,
        "language": language,
        "generation_metadata": {
            "model_used": llm_service.model_name,
            "tokens_used": generated_code.get("usage", {}),
            "generation_time": generated_code.get("timing", {})
        }
    }

def code_generation_coordinator(state: CodeGenerationState) -> CodeGenerationState:
    """Coordinate code generation workflow."""
    if not state.get("status"):
        state["status"] = "running"

    return state

def create_code_generation_workflow() -> StateGraph:
    """Create code generation workflow with validation."""

    workflow = StateGraph(CodeGenerationState)

    # Add nodes
    workflow.add_node("coordinator", code_generation_coordinator)
    workflow.add_node("context_retrieval", ToolNode([context_retriever]))
    workflow.add_node("code_generation", ToolNode([code_generator]))
    workflow.add_node("validation", get_validation_pipeline())  # Embed validation pipeline
    workflow.add_node("approval", create_approval_workflow())  # Embed approval workflow

    # Add edges
    workflow.add_edge(START, "coordinator")
    workflow.add_edge("coordinator", "context_retrieval")
    workflow.add_edge("context_retrieval", "code_generation")
    workflow.add_edge("code_generation", "validation")
    workflow.add_edge("validation", "approval")
    workflow.add_edge("approval", END)

    return workflow
```

## Integration with FastAPI

### LangGraph Service Integration

```python
# src/services/langgraph_service.py
from langgraph.checkpoint.postgres import PostgresCheckpointSaver
from langgraph.pregel import Pregel
import asyncpg
import uuid
from typing import Dict, Any, Optional

class LangGraphService:
    """Service for managing LangGraph workflows."""

    def __init__(self, database_url: str):
        self.database_url = database_url
        self.checkpointer = None
        self.workflows = {}

    async def initialize(self):
        """Initialize LangGraph service with Supabase checkpointer."""
        # Use Supabase PostgreSQL for checkpointing
        self.checkpointer = PostgresCheckpointSaver.from_conn_string(self.database_url)

        # Initialize workflows
        self.workflows = {
            "validation": get_validation_pipeline().compile(checkpointer=self.checkpointer),
            "approval": create_approval_workflow().compile(checkpointer=self.checkpointer),
            "code_generation": create_code_generation_workflow().compile(checkpointer=self.checkpointer),
        }

    async def start_workflow(
        self,
        workflow_type: str,
        initial_state: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Start a new workflow."""
        if workflow_type not in self.workflows:
            raise ValueError(f"Unknown workflow type: {workflow_type}")

        thread_id = str(uuid.uuid4())
        workflow = self.workflows[workflow_type]

        config = config or {"configurable": {"thread_id": thread_id}}

        # Start workflow execution
        await workflow.ainvoke(initial_state, config=config)

        return thread_id

    async def resume_workflow(
        self,
        workflow_type: str,
        thread_id: str,
        command: Command
    ) -> Dict[str, Any]:
        """Resume interrupted workflow."""
        workflow = self.workflows[workflow_type]
        config = {"configurable": {"thread_id": thread_id}}

        result = await workflow.ainvoke(command, config=config)
        return result

    async def get_workflow_state(
        self,
        workflow_type: str,
        thread_id: str
    ) -> Dict[str, Any]:
        """Get current workflow state."""
        workflow = self.workflows[workflow_type]
        config = {"configurable": {"thread_id": thread_id}}

        state = workflow.get_state(config)
        return {
            "values": state.values,
            "next": state.next,
            "metadata": state.metadata,
            "created_at": state.created_at,
            "parent_config": state.parent_config
        }

# FastAPI endpoints
@app.post("/api/v1/workflows/validation/start")
async def start_validation_workflow(
    request: ValidationRequest,
    current_user = Depends(get_current_user),
    langgraph_service: LangGraphService = Depends(get_langgraph_service)
):
    """Start validation workflow."""

    initial_state = {
        "workflow_id": str(uuid.uuid4()),
        "user_id": current_user["id"],
        "project_id": request.project_id,
        "code_content": request.code,
        "file_path": request.file_path,
        "language": request.language,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "status": "pending",
        "messages": []
    }

    thread_id = await langgraph_service.start_workflow(
        "validation",
        initial_state
    )

    return {
        "thread_id": thread_id,
        "status": "started",
        "workflow_type": "validation"
    }

@app.post("/api/v1/workflows/{workflow_type}/{thread_id}/resume")
async def resume_workflow(
    workflow_type: str,
    thread_id: str,
    command_data: Dict[str, Any],
    current_user = Depends(get_current_user),
    langgraph_service: LangGraphService = Depends(get_langgraph_service)
):
    """Resume interrupted workflow."""

    command = Command(resume=command_data)

    result = await langgraph_service.resume_workflow(
        workflow_type,
        thread_id,
        command
    )

    return {
        "thread_id": thread_id,
        "status": "resumed",
        "result": result
    }

@app.get("/api/v1/workflows/{workflow_type}/{thread_id}/state")
async def get_workflow_state(
    workflow_type: str,
    thread_id: str,
    current_user = Depends(get_current_user),
    langgraph_service: LangGraphService = Depends(get_langgraph_service)
):
    """Get workflow state."""

    state = await langgraph_service.get_workflow_state(
        workflow_type,
        thread_id
    )

    return state
```

## Key Takeaways

1. **Visual Workflow Design**: LangGraph provides graph-based representation of complex AI agent workflows
2. **Human-in-the-Loop**: Built-in interrupt/resume functionality for approval workflows
3. **State Management**: Persistent state with checkpointing and rollback capabilities
4. **Multi-Stage Validation**: Sequential pipeline execution with validation gates
5. **Debugging & Monitoring**: Time travel and state inspection capabilities
6. **Production Ready**: Integration with Supabase for persistence and FastAPI for APIs
7. **Streaming Support**: Real-time progress updates and WebSocket notifications
8. **Modular Architecture**: Composable workflows that can be embedded and reused

LangGraph Studio transforms your AI Coding Agent into a sophisticated workflow orchestration platform with enterprise-grade debugging, monitoring, and human oversight capabilities.