#!/usr/bin/env python3
"""
Validation script for centralized configuration implementation.

This script validates that the centralized configuration system is working correctly
and all environment variables are properly mapped.
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path for imports
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_basic_import():
    """Test basic import of settings module."""
    try:
        from src.core.config import settings, get_settings, validate_configuration
        print("✓ Successfully imported settings module")
        return True
    except ImportError as e:
        print(f"✗ Failed to import settings module: {e}")
        return False

def test_settings_properties():
    """Test that settings properties are accessible."""
    try:
        from src.core.config import settings

        # Test a few key properties
        print(f"✓ App name: {settings.APP_NAME}")
        print(f"✓ Environment: {settings.ENVIRONMENT}")
        print(f"✓ JWT algorithm: {settings.JWT_ALGORITHM}")
        print(f"✓ CORS origins: {settings.CORS_ORIGINS}")

        # Test computed properties
        print(f"✓ Redis available: {settings.redis_available}")
        print(f"✓ Supabase available: {settings.supabase_available}")
        print(f"✓ OpenRouter available: {settings.openrouter_available}")

        return True
    except Exception as e:
        print(f"✗ Failed to access settings properties: {e}")
        return False

def test_dependency_injection():
    """Test dependency injection function."""
    try:
        from src.core.config import get_settings

        settings_instance = get_settings()
        print(f"✓ Dependency injection works: {type(settings_instance)}")
        return True
    except Exception as e:
        print(f"✗ Dependency injection failed: {e}")
        return False

def test_utility_functions():
    """Test utility functions."""
    try:
        from src.core.config import get_llm_provider_config, get_embedding_config, LLMProvider

        # Test LLM provider config
        ollama_config = get_llm_provider_config(LLMProvider.OLLAMA)
        print(f"✓ Ollama config: {ollama_config.get('base_url', 'Not configured')}")

        # Test embedding config
        embedding_config = get_embedding_config()
        print(f"✓ Embedding provider: {embedding_config.get('provider', 'Not configured')}")

        return True
    except Exception as e:
        print(f"✗ Utility functions failed: {e}")
        return False

def main():
    """Run all validation tests."""
    print("=" * 60)
    print("AI Orchestrator - Centralized Configuration Validation")
    print("=" * 60)

    tests = [
        ("Basic Import", test_basic_import),
        ("Settings Properties", test_settings_properties),
        ("Dependency Injection", test_dependency_injection),
        ("Utility Functions", test_utility_functions),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        if test_func():
            passed += 1
        else:
            print("This test failed!")

    print("\n" + "=" * 60)
    print(f"Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Centralized configuration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())