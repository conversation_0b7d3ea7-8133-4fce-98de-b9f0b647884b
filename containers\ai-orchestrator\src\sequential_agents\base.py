"""
Sequential Agents - Base class

Defines a minimal abstract BaseAgent with a simple async execute interface.
"""
from __future__ import annotations

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any


class BaseAgent(ABC):
    """Abstract base class for simplified sequential agents."""

    def __init__(self) -> None:
        self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the agent's task with the provided input and return a result dict."""
        raise NotImplementedError