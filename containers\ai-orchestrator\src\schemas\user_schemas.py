# Project: AI Coding Agent
# Purpose: Pydantic schemas for User data transfer objects
# Author: AI Coding Agent Team

"""
User Pydantic schemas for API request/response validation.

This module defines data transfer objects (DTOs) for User operations:
- UserBase: Common fields shared across schemas
- UserCreate: Fields required when creating a new user
- UserUpdate: Fields that can be updated (all optional)
- UserInDB: Complete user data as stored in database
- UserResponse: User data returned in API responses (excludes sensitive data)
- UserListResponse: Paginated list response for multiple users
- SupabaseUser: Schema for Supabase Auth integration
- UserProfileUpdateSchema: Schema for updating user profiles
"""

from typing import Optional, Any, Dict, List
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field, ConfigDict, field_validator
from uuid import UUID
import re


class UserBase(BaseModel):
    """
    Base User schema with common fields.

    This schema defines the core user attributes that are shared
    across different operations (create, update, response).
    """

    username: str = Field(
        ...,
        min_length=3,
        max_length=50,
        description="Unique username for authentication",
        examples=["johndoe", "alice_smith", "developer123"]
    )

    email: EmailStr = Field(
        ...,
        description="Valid email address for account identification",
        examples=["<EMAIL>", "<EMAIL>"]
    )

    full_name: Optional[str] = Field(
        default=None,
        max_length=255,
        description="User's full display name",
        examples=["John Doe", "Alice Smith", "Jane Developer"]
    )

    is_active: bool = Field(
        default=True,
        description="Account activation status"
    )

    @field_validator('username')
    @classmethod
    def validate_username(cls, v: str) -> str:
        """
        Validate username format.

        Args:
            v: Username to validate

        Returns:
            str: Validated username

        Raises:
            ValueError: If username format is invalid
        """
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v.lower().strip()

    @field_validator('full_name')
    @classmethod
    def validate_full_name(cls, v: Optional[str]) -> Optional[str]:
        """
        Validate and clean full name.

        Args:
            v: Full name to validate

        Returns:
            Optional[str]: Cleaned full name or None
        """
        if v is None:
            return None
        cleaned = v.strip()
        return cleaned if cleaned else None


class UserCreate(UserBase):
    """
    Schema for creating a new user.

    Extends UserBase with fields required only during user creation,
    specifically the plain text password that will be hashed.
    """

    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="Plain text password (will be hashed before storage)",
        examples=["SecurePassword123!", "MyP@ssw0rd2024"]
    )

    is_superuser: bool = Field(
        default=False,
        description="Admin privileges flag (only for initial setup)"
    )

    @field_validator('password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        """
        Validate password strength.

        Args:
            v: Password to validate

        Returns:
            str: Validated password

        Raises:
            ValueError: If password doesn't meet security requirements
        """
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')

        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')

        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')

        if not re.search(r'[0-9]', v):
            raise ValueError('Password must contain at least one digit')

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')

        return v


class UserUpdate(BaseModel):
    """
    Schema for updating an existing user.

    All fields are optional since users may want to update
    only specific attributes. Fields not provided will remain unchanged.
    """

    username: Optional[str] = Field(
        default=None,
        min_length=3,
        max_length=50,
        description="New username (must be unique)"
    )

    email: Optional[EmailStr] = Field(
        default=None,
        description="New email address (must be unique)"
    )

    full_name: Optional[str] = Field(
        default=None,
        max_length=255,
        description="Updated full display name"
    )

    password: Optional[str] = Field(
        default=None,
        min_length=8,
        max_length=128,
        description="New password (will be hashed)"
    )

    is_active: Optional[bool] = Field(
        default=None,
        description="Updated account activation status"
    )

    is_superuser: Optional[bool] = Field(
        default=None,
        description="Updated admin privileges (admin only)"
    )

    profile_data: Optional[str] = Field(
        default=None,
        description="JSON string for additional profile information"
    )

    @field_validator('username')
    @classmethod
    def validate_username(cls, v: Optional[str]) -> Optional[str]:
        """Validate username format if provided."""
        if v is None:
            return None
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v.lower().strip()

    @field_validator('password')
    @classmethod
    def validate_password(cls, v: Optional[str]) -> Optional[str]:
        """Validate password strength if provided."""
        if v is None:
            return None

        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')

        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')

        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')

        if not re.search(r'[0-9]', v):
            raise ValueError('Password must contain at least one digit')

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')

        return v

    @field_validator('full_name')
    @classmethod
    def validate_full_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and clean full name if provided."""
        if v is None:
            return None
        cleaned = v.strip()
        return cleaned if cleaned else None


class UserInDB(UserBase):
    """
    Schema representing a user as stored in the database.

    This includes all database fields including auto-generated ones
    like ID, timestamps, and the hashed password.
    """

    model_config = ConfigDict(from_attributes=True)

    id: int = Field(
        ...,
        description="Unique user identifier (auto-generated)"
    )

    hashed_password: str = Field(
        ...,
        description="Bcrypt hashed password"
    )

    is_superuser: bool = Field(
        default=False,
        description="Admin privileges flag"
    )

    created_at: datetime = Field(
        ...,
        description="Account creation timestamp"
    )

    updated_at: datetime = Field(
        ...,
        description="Last modification timestamp"
    )

    last_login: Optional[datetime] = Field(
        default=None,
        description="Last successful login timestamp"
    )

    profile_data: Optional[str] = Field(
        default=None,
        description="JSON field for additional profile information"
    )


class UserResponse(BaseModel):
    """
    Schema for user data in API responses.

    This excludes sensitive information like hashed passwords
    and includes computed fields for API consumers.
    """

    model_config = ConfigDict(from_attributes=True)

    id: int = Field(
        ...,
        description="Unique user identifier"
    )

    username: str = Field(
        ...,
        description="Username for authentication"
    )

    email: str = Field(
        ...,
        description="User email address"
    )

    full_name: Optional[str] = Field(
        default=None,
        description="User's full display name"
    )

    is_active: bool = Field(
        ...,
        description="Account activation status"
    )

    is_superuser: bool = Field(
        ...,
        description="Admin privileges flag"
    )

    created_at: datetime = Field(
        ...,
        description="Account creation timestamp"
    )

    updated_at: datetime = Field(
        ...,
        description="Last modification timestamp"
    )

    last_login: Optional[datetime] = Field(
        default=None,
        description="Last successful login timestamp"
    )

    display_name: str = Field(
        ...,
        description="Best available display name for the user"
    )

    @classmethod
    def from_user_model(cls, user: Any) -> "UserResponse":
        """
        Create UserResponse from SQLAlchemy User model.

        Args:
            user: SQLAlchemy User model instance

        Returns:
            UserResponse: Formatted response object
        """
        return cls(
            id=user.id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login=user.last_login,
            display_name=user.display_name
        )


class UserListResponse(BaseModel):
    """
    Schema for paginated list of users.

    Provides metadata for pagination along with the user data.
    """

    users: List[UserResponse] = Field(
        ...,
        description="List of users in current page"
    )

    total: int = Field(
        ...,
        ge=0,
        description="Total number of users matching the query"
    )

    page: int = Field(
        ...,
        ge=1,
        description="Current page number (1-based)"
    )

    page_size: int = Field(
        ...,
        ge=1,
        le=100,
        description="Number of users per page"
    )

    total_pages: int = Field(
        ...,
        ge=0,
        description="Total number of pages"
    )

    has_next: bool = Field(
        ...,
        description="Whether there are more pages available"
    )

    has_previous: bool = Field(
        ...,
        description="Whether there are previous pages available"
    )

    @classmethod
    def create_paginated_response(
        cls,
        users: List[Any],
        total: int,
        page: int,
        page_size: int
    ) -> "UserListResponse":
        """
        Create paginated response from user list and metadata.

        Args:
            users: List of SQLAlchemy User model instances
            total: Total number of users matching the query
            page: Current page number (1-based)
            page_size: Number of users per page

        Returns:
            UserListResponse: Formatted paginated response
        """
        total_pages = (total + page_size - 1) // page_size

        return cls(
            users=[UserResponse.from_user_model(user) for user in users],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_previous=page > 1
        )


# ==================================================================================
# SUPABASE INTEGRATION SCHEMAS
# ==================================================================================

class SupabaseUser(BaseModel):
    """
    Schema representing user data from Supabase Auth.

    This schema matches the structure of user data returned by Supabase Auth API,
    enabling seamless integration between Supabase authentication and our
    custom user management system.
    """

    id: UUID = Field(
        ...,
        description="Supabase user UUID from auth.users table"
    )

    email: EmailStr = Field(
        ...,
        description="User email address from Supabase Auth"
    )

    email_confirmed_at: Optional[datetime] = Field(
        default=None,
        description="Email confirmation timestamp from Supabase"
    )

    created_at: datetime = Field(
        ...,
        description="Account creation timestamp from Supabase"
    )

    updated_at: datetime = Field(
        ...,
        description="Last update timestamp from Supabase"
    )

    last_sign_in_at: Optional[datetime] = Field(
        default=None,
        description="Last sign-in timestamp from Supabase"
    )

    # User metadata from Supabase (optional fields)
    user_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="User metadata stored in Supabase"
    )

    app_metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="App metadata stored in Supabase"
    )

    # Computed properties for easier access
    @property
    def display_name(self) -> str:
        """Get display name from metadata or email."""
        return (
            self.user_metadata.get('full_name') or
            self.user_metadata.get('name') or
            self.email.split('@')[0]
        )

    @property
    def full_name(self) -> Optional[str]:
        """Get full name from metadata."""
        return self.user_metadata.get('full_name') or self.user_metadata.get('name')

    @property
    def username(self) -> str:
        """Generate username from email or metadata."""
        return (
            self.user_metadata.get('username') or
            self.email.split('@')[0].lower()
        )

    model_config = ConfigDict(
        json_encoders={
            UUID: str,
            datetime: lambda dt: dt.isoformat() if dt else None
        }
    )


class UserProfileUpdateSchema(BaseModel):
    """
    Schema for updating user profile information.

    This schema defines the fields that can be updated for a user's profile
    after initial creation. All fields are optional to support partial updates.
    """

    username: Optional[str] = Field(
        default=None,
        min_length=3,
        max_length=50,
        description="New username (must be unique)",
        pattern=r'^[a-zA-Z0-9_-]+$'
    )

    full_name: Optional[str] = Field(
        default=None,
        max_length=255,
        description="Updated full display name"
    )

    is_active: Optional[bool] = Field(
        default=None,
        description="Updated account activation status (admin only)"
    )

    profile_data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional profile data as JSON"
    )

    # Preferences and settings
    preferences: Optional[Dict[str, Any]] = Field(
        default=None,
        description="User preferences and settings"
    )

    # Metadata tracking
    last_login: Optional[datetime] = Field(
        default=None,
        description="Update last login timestamp"
    )

    @field_validator('username')
    @classmethod
    def validate_username_format(cls, v: Optional[str]) -> Optional[str]:
        """Validate username format if provided."""
        if v is None:
            return None

        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError(
                'Username can only contain letters, numbers, hyphens, and underscores'
            )

        return v.lower().strip()

    @field_validator('full_name')
    @classmethod
    def validate_full_name_format(cls, v: Optional[str]) -> Optional[str]:
        """Validate and clean full name if provided."""
        if v is None:
            return None

        cleaned = v.strip()
        return cleaned if cleaned else None

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda dt: dt.isoformat() if dt else None
        }
    )


class UserProjectAssociation(BaseModel):
    """
    Schema for user-project relationship data.

    Used for managing project memberships and permissions.
    """

    user_id: int = Field(
        ...,
        description="User ID from our custom users table"
    )

    project_id: str = Field(
        ...,
        description="Project identifier"
    )

    role: str = Field(
        default="member",
        description="User role in the project"
    )

    permissions: List[str] = Field(
        default_factory=list,
        description="Specific permissions for this project"
    )

    joined_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="When user joined the project"
    )

    is_active: bool = Field(
        default=True,
        description="Whether the association is active"
    )


# ==================================================================================
# REPOSITORY RESPONSE SCHEMAS
# ==================================================================================

class UserCreateResponse(BaseModel):
    """Response schema for user creation via repository."""

    user: UserResponse = Field(
        ...,
        description="Created user data"
    )

    created_from_supabase: bool = Field(
        ...,
        description="Whether user was created from Supabase Auth data"
    )

    sync_status: str = Field(
        default="synced",
        description="Synchronization status with Supabase"
    )

    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional creation metadata"
    )