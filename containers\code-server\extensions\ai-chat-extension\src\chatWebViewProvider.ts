/**
 * Chat WebView Provider for AI Chat Extension
 * Manages the chat UI interface and integrates with WebSocket manager
 */

import * as vscode from 'vscode';
import { WebSocketManager, ChatMessage, MessageHandler } from './webSocketManager';

export class ChatWeb<PERSON>iewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'aiChatView';

    private _view?: vscode.WebviewView;
    private messages: ChatMessage[] = [];
    private messageHandler: MessageHandler;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly webSocketManager: WebSocketManager
    ) {
        // Bind message handler
        this.messageHandler = this.handleMessage.bind(this);
        this.webSocketManager.addMessageHandler(this.messageHandler);
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(async (data: { type: string; content?: string }) => {
            switch (data.type) {
                case 'sendMessage':
                    if (data.content) {
                        await this.sendMessage(data.content);
                    }
                    break;
                case 'clearHistory':
                    this.clearHistory();
                    break;
                case 'reconnect':
                    this.webSocketManager.reconnect();
                    break;
                case 'getStatus':
                    this.updateConnectionStatus();
                    break;
            }
        });

        // Update connection status when view is resolved
        this.updateConnectionStatus();
    }

    public show(): void {
        if (this._view) {
            this._view.show?.(true);
        }
    }

    public clearHistory(): void {
        this.messages = [];
        this.updateMessagesInWebview();
        vscode.window.showInformationMessage('Chat history cleared');
    }

    private async sendMessage(content: string): Promise<void> {
        if (!content.trim()) {
            return;
        }

        // Get current editor context for better AI assistance
        const editor = vscode.window.activeTextEditor;
        const contextInfo = {
            activeFile: editor?.document.fileName,
            selectedText: editor?.document.getText(editor.selection),
            language: editor?.document.languageId,
            workspacePath: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
        };

        // Add user message to local history
        const userMessage: ChatMessage = {
            id: this.generateMessageId(),
            type: 'user',
            content: content.trim(),
            timestamp: new Date().toISOString()
        };

        this.messages.push(userMessage);
        this.updateMessagesInWebview();

        // Send message via WebSocket with context
        try {
            await this.webSocketManager.sendChatMessage(content.trim());

            // If we have context, send it as a system message
            if (contextInfo.selectedText && contextInfo.activeFile) {
                const contextMessage = `Context: File: ${contextInfo.activeFile}, Language: ${contextInfo.language}, Selected: ${contextInfo.selectedText.substring(0, 200)}...`;
                await this.webSocketManager.sendChatMessage(contextMessage);
            }
        } catch (error) {
            console.error('Failed to send message:', error);
            vscode.window.showErrorMessage(`Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`);

            // Add error message to chat
            const errorMessage: ChatMessage = {
                id: this.generateMessageId(),
                type: 'system',
                content: `Error: Failed to send message - ${error instanceof Error ? error.message : 'Unknown error'}`,
                timestamp: new Date().toISOString()
            };
            this.messages.push(errorMessage);
            this.updateMessagesInWebview();
        }
    }

    private handleMessage(message: ChatMessage): void {
        // Add received message to history
        this.messages.push(message);

        // Limit message history
        const config = vscode.workspace.getConfiguration('aiChat');
        const maxMessages = config.get('maxMessages', 100);

        if (this.messages.length > maxMessages) {
            this.messages = this.messages.slice(-maxMessages);
        }

        // Update webview
        this.updateMessagesInWebview();
    }

    private updateMessagesInWebview(): void {
        if (this._view) {
            this._view.webview.postMessage({
                type: 'updateMessages',
                messages: this.messages
            });
        }
    }

    private updateConnectionStatus(): void {
        if (this._view) {
            this._view.webview.postMessage({
                type: 'updateStatus',
                status: this.webSocketManager.getConnectionStatus(),
                isConnected: this.webSocketManager.isConnected()
            });
        }
    }

    private generateMessageId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        // Use CSP to only allow specific sources
        const nonce = this.getNonce();

        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AI Chat</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 10px;
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                }

                .header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                }

                .status {
                    font-size: 12px;
                    padding: 2px 6px;
                    border-radius: 3px;
                    background-color: var(--vscode-badge-background);
                    color: var(--vscode-badge-foreground);
                }

                .status.connected {
                    background-color: var(--vscode-testing-iconPassed);
                    color: white;
                }

                .status.disconnected {
                    background-color: var(--vscode-testing-iconFailed);
                    color: white;
                }

                .messages-container {
                    flex: 1;
                    overflow-y: auto;
                    padding: 5px 0;
                    margin-bottom: 10px;
                }

                .message {
                    margin-bottom: 15px;
                    padding: 8px 12px;
                    border-radius: 8px;
                    max-width: 85%;
                    word-wrap: break-word;
                }

                .message.user {
                    background-color: var(--vscode-inputOption-activeBackground);
                    border: 1px solid var(--vscode-inputOption-activeBorder);
                    margin-left: auto;
                    text-align: right;
                }

                .message.assistant {
                    background-color: var(--vscode-editor-inactiveSelectionBackground);
                    border: 1px solid var(--vscode-panel-border);
                }

                .message.system {
                    background-color: var(--vscode-textCodeBlock-background);
                    border: 1px solid var(--vscode-panel-border);
                    font-style: italic;
                    opacity: 0.8;
                    text-align: center;
                }

                .message-header {
                    font-size: 11px;
                    opacity: 0.7;
                    margin-bottom: 4px;
                }

                .message-content {
                    white-space: pre-wrap;
                    line-height: 1.4;
                }

                .input-container {
                    display: flex;
                    gap: 5px;
                    margin-top: auto;
                }

                .message-input {
                    flex: 1;
                    padding: 8px 12px;
                    border: 1px solid var(--vscode-input-border);
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 4px;
                    font-family: inherit;
                    font-size: inherit;
                    resize: none;
                    min-height: 20px;
                    max-height: 100px;
                }

                .message-input:focus {
                    outline: none;
                    border-color: var(--vscode-focusBorder);
                }

                .send-button {
                    padding: 8px 12px;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-family: inherit;
                    font-size: inherit;
                }

                .send-button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }

                .send-button:disabled {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    cursor: not-allowed;
                }

                .controls {
                    display: flex;
                    gap: 5px;
                    margin-bottom: 10px;
                }

                .control-button {
                    padding: 4px 8px;
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 11px;
                }

                .control-button:hover {
                    background-color: var(--vscode-button-secondaryHoverBackground);
                }

                .empty-state {
                    text-align: center;
                    opacity: 0.6;
                    margin-top: 20px;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h3 style="margin: 0;">AI Chat</h3>
                <span id="status" class="status">Connecting...</span>
            </div>

            <div class="controls">
                <button id="clearBtn" class="control-button">Clear History</button>
                <button id="reconnectBtn" class="control-button">Reconnect</button>
            </div>

            <div id="messages" class="messages-container">
                <div class="empty-state">
                    <p>Welcome to AI Chat!</p>
                    <p>Start a conversation with the AI coding agents.</p>
                </div>
            </div>

            <div class="input-container">
                <textarea id="messageInput" class="message-input" placeholder="Type your message here..." rows="1"></textarea>
                <button id="sendBtn" class="send-button">Send</button>
            </div>

            <script nonce="${nonce}">
                (function() {
                    const vscode = acquireVsCodeApi();

                    // DOM elements
                    const messagesContainer = document.getElementById('messages');
                    const messageInput = document.getElementById('messageInput');
                    const sendButton = document.getElementById('sendBtn');
                    const statusElement = document.getElementById('status');
                    const clearButton = document.getElementById('clearBtn');
                    const reconnectButton = document.getElementById('reconnectBtn');

                    let messages = [];

                    // Auto-resize textarea
                    messageInput.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = Math.min(this.scrollHeight, 100) + 'px';
                    });

                    // Send message on Enter (Shift+Enter for new line)
                    messageInput.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            sendMessage();
                        }
                    });

                    // Send button click
                    sendButton.addEventListener('click', sendMessage);

                    // Clear button click
                    clearButton.addEventListener('click', function() {
                        vscode.postMessage({ type: 'clearHistory' });
                    });

                    // Reconnect button click
                    reconnectButton.addEventListener('click', function() {
                        vscode.postMessage({ type: 'reconnect' });
                    });

                    function sendMessage() {
                        const content = messageInput.value.trim();
                        if (content) {
                            vscode.postMessage({
                                type: 'sendMessage',
                                content: content
                            });
                            messageInput.value = '';
                            messageInput.style.height = 'auto';
                        }
                    }

                    function updateMessages(newMessages) {
                        messages = newMessages;
                        renderMessages();
                    }

                    function renderMessages() {
                        if (messages.length === 0) {
                            messagesContainer.innerHTML = \`
                                <div class="empty-state">
                                    <p>Welcome to AI Chat!</p>
                                    <p>Start a conversation with the AI coding agents.</p>
                                </div>
                            \`;
                            return;
                        }

                        const messagesHTML = messages.map(msg => {
                            const time = new Date(msg.timestamp).toLocaleTimeString();
                            const agentInfo = msg.agent_type ? \` (\${msg.agent_type})\` : '';

                            return \`
                                <div class="message \${msg.type}">
                                    <div class="message-header">
                                        \${msg.type === 'user' ? 'You' : 'AI Agent\${agentInfo}'} • \${time}
                                    </div>
                                    <div class="message-content">\${escapeHtml(msg.content)}</div>
                                </div>
                            \`;
                        }).join('');

                        messagesContainer.innerHTML = messagesHTML;

                        // Scroll to bottom
                        messagesContainer.scrollTop = messagesContainer.scrollHeight;
                    }

                    function updateStatus(status, isConnected) {
                        statusElement.textContent = status;
                        statusElement.className = 'status ' + (isConnected ? 'connected' : 'disconnected');
                        sendButton.disabled = !isConnected;
                    }

                    function escapeHtml(text) {
                        const div = document.createElement('div');
                        div.textContent = text;
                        return div.innerHTML;
                    }

                    // Handle messages from extension
                    window.addEventListener('message', event => {
                        const message = event.data;

                        switch (message.type) {
                            case 'updateMessages':
                                updateMessages(message.messages);
                                break;
                            case 'updateStatus':
                                updateStatus(message.status, message.isConnected);
                                break;
                        }
                    });

                    // Request initial status
                    vscode.postMessage({ type: 'getStatus' });
                })();
            </script>
        </body>
        </html>`;
    }

    private getNonce(): string {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
}