# Project: AI Coding Agent
# Purpose: Utils package exports with comprehensive initialization

"""
Utils Package for AI Orchestrator

This package contains utility modules for authentication, LLM services,
and common helper functions used throughout the AI Orchestrator.
"""

import logging

logger = logging.getLogger(__name__)

# Import utility modules with error handling for optional dependencies
try:
    from .auth import (
        init_supabase, get_supabase, verify_token, create_access_token,
        get_current_user, hash_password, verify_password, TokenData, get_auth_status,
        create_local_user, get_local_user, authenticate_local_user, is_supabase_available
    )
    AUTH_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import auth utilities: {e}")
    AUTH_AVAILABLE = False

    # Create minimal fallback functions that don't raise NotImplementedError
    def init_supabase():
        logger.warning("Auth module not available - authentication disabled")

    def get_supabase():
        return None

    def verify_token(*args, **kwargs):
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service not available"
        )

    def create_access_token(*args, **kwargs):
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Token creation service not available"
        )

    def get_current_user(*args, **kwargs):
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="User authentication service not available"
        )

    def hash_password(password: str) -> str:
        import hashlib
        return hashlib.sha256(password.encode()).hexdigest()

    def verify_password(plain_password: str, hashed_password: str) -> bool:
        return hash_password(plain_password) == hashed_password

    def get_auth_status():
        return {"available": False, "error": "Auth module import failed"}

    def create_local_user(*args, **kwargs):
        return None

    def get_local_user(*args, **kwargs):
        return None

    def authenticate_local_user(*args, **kwargs):
        return None

    def is_supabase_available():
        return False

    class TokenData:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

try:
    from .llm_service import (
        UniversalLLMService, LLMService, LLMProvider, LLMResponse, llm_service
    )
except ImportError as e:
    logger.error(f"Failed to import LLM service utilities: {e}")
    UniversalLLMService = None
    LLMService = None
    LLMProvider = None
    LLMResponse = None
    llm_service = None

try:
    from .utils import (
        generate_request_id, sanitize_filename, ensure_directory,
        safe_json_loads, safe_json_dumps, get_environment_info,
        format_bytes, is_valid_email, truncate_string, merge_dicts
    )
    UTILS_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import utility functions: {e}")
    UTILS_AVAILABLE = False

    # Create enhanced fallback functions with actual functionality
    import uuid
    import json
    import re
    import os
    from pathlib import Path

    def generate_request_id(*args, **kwargs):
        return f"req-{uuid.uuid4().hex[:12]}"

    def sanitize_filename(name):
        # Remove invalid characters for filenames
        return re.sub(r'[<>:"/\\|?*]', '_', str(name))

    def ensure_directory(path):
        Path(path).mkdir(parents=True, exist_ok=True)
        return path

    def safe_json_loads(data, default=None):
        try:
            return json.loads(data) if data else default
        except (json.JSONDecodeError, TypeError):
            return default

    def safe_json_dumps(data, default="{}"):
        try:
            return json.dumps(data) if data is not None else default
        except (TypeError, ValueError):
            return default

    def get_environment_info():
        return {
            "python_version": os.sys.version,
            "platform": os.name,
            "cwd": os.getcwd()
        }

    def format_bytes(size):
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"

    def is_valid_email(email):
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, str(email))) if email else False

    def truncate_string(text, max_len=100, suffix="..."):
        text = str(text) if text else ""
        return text[:max_len] + suffix if len(text) > max_len else text

    def merge_dicts(*dicts):
        result = {}
        for d in dicts:
            if isinstance(d, dict):
                result.update(d)
        return result

# Define comprehensive exports for clean imports and proper IDE support
__all__ = [
    # Authentication utilities
    "init_supabase",
    "get_supabase",
    "verify_token",
    "create_access_token",
    "get_current_user",
    "hash_password",
    "verify_password",
    "get_auth_status",
    "TokenData",

    # LLM service utilities
    "UniversalLLMService",
    "LLMService",
    "LLMProvider",
    "LLMResponse",
    "llm_service",

    # Common utilities
    "generate_request_id",
    "sanitize_filename",
    "ensure_directory",
    "safe_json_loads",
    "safe_json_dumps",
    "get_environment_info",
    "format_bytes",
    "is_valid_email",
    "truncate_string",
    "merge_dicts",
]