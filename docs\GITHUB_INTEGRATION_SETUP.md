# GitHub Integration Setup Guide

## Overview

The AI Coding Agent now includes comprehensive GitHub integration via the Project Importer VS Code extension. This allows users to:

- **Authenticate with GitHub** through OAuth
- **Clone repositories** directly from GitHub
- **Upload projects** from their computer
- **Manage workspace projects** efficiently

## Prerequisites

- Running AI Coding Agent environment
- GitHub account
- Supabase project (for OAuth handling)

## 🚀 Quick Setup

### 1. Configure GitHub OAuth Application

1. **Go to GitHub Settings**:
   - Navigate to [GitHub Developer Settings](https://github.com/settings/developers)
   - Click "OAuth Apps" → "New OAuth App"

2. **Configure OAuth App**:
   ```
   Application Name: AI Coding Agent
   Homepage URL: http://localhost:8080
   Authorization callback URL: YOUR_SUPABASE_URL/auth/v1/callback
   ```

   **Note**: Replace `YOUR_SUPABASE_URL` with your actual Supabase project URL

3. **Get Credentials**:
   - Copy the **Client ID**
   - Generate and copy the **Client Secret**

### 2. Configure Supabase

1. **Enable GitHub Provider**:
   - Go to your Supabase dashboard
   - Navigate to Authentication → Providers
   - Enable GitHub provider
   - Paste your GitHub Client ID and Client Secret

2. **Configure Redirect URLs**:
   - Add `http://localhost:8080` to allowed redirect URLs
   - Add `http://localhost:8000/auth/github/callback` to allowed redirect URLs

### 3. Update Environment Variables

Add to your `.env` file:

```bash
# GitHub OAuth Configuration
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here
GITHUB_REDIRECT_URI=http://localhost:8000/auth/github/callback

# Ensure these are set for Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key
JWT_SECRET=your_secure_jwt_secret
```

### 4. Rebuild and Start Services

```bash
# Rebuild containers to include new environment variables
docker-compose down
docker-compose up --build -d

# Or if using development mode
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build -d
```

## 📋 Using the Project Importer

### Accessing the Project Importer

1. **Open Code Server**: Navigate to `http://localhost:8080`
2. **Find Project Importer**: Look for "Project Importer" in the Explorer sidebar
3. **Authentication**: Click "Login with GitHub" to authenticate

### Available Actions

#### 🔐 Login with GitHub
- Click "Login with GitHub"
- Complete OAuth flow in your browser
- Return to VS Code authenticated

#### 📥 Clone Repository
1. Click "Clone GitHub Repository"
2. Enter the repository URL (e.g., `https://github.com/username/repo.git`)
3. Repository will be cloned to your workspace

#### 📤 Upload Project
1. Click "Upload Project from Computer"
2. Select files or folders to upload
3. Files will be uploaded to your workspace

### Workspace Structure

Projects are organized per user:
```
workspace/
└── user_<your_user_id>/
    ├── project-1/
    ├── project-2/
    └── uploaded-project/
```

## 🔧 API Endpoints

### Authentication Endpoints

- **GET** `/auth/github/status` - Check GitHub OAuth configuration
- **GET** `/auth/github/url` - Get GitHub OAuth URL
- **GET** `/auth/github/callback` - OAuth callback handler
- **POST** `/auth/github/logout` - Logout from GitHub

### Project Management Endpoints

- **GET** `/projects/workspace/info` - Get workspace information
- **POST** `/projects/upload` - Upload project files
- **POST** `/projects/clone` - Clone Git repository
- **GET** `/projects/list` - List user projects
- **DELETE** `/projects/delete/{project_name}` - Delete project

## 🛠️ Development & Customization

### Extension Structure

```
containers/code-server/extensions/project-importer/
├── src/
│   └── extension.ts      # Main extension logic (TypeScript)
├── media/
│   └── upload.js         # Webview JavaScript
├── package.json          # Extension configuration
└── tsconfig.json         # TypeScript configuration
```

### Building the Extension

```bash
cd containers/code-server/extensions/project-importer
npm install
npm run compile
```

### Backend Router Files

- `src/router/github_auth_router.py` - GitHub OAuth handling
- `src/router/project_router.py` - Project management API

## 🔍 Troubleshooting

### Common Issues

**1. "GitHub OAuth not configured" error**
- Ensure `GITHUB_CLIENT_ID` is set in your `.env` file
- Restart the ai-orchestrator service

**2. Authentication fails**
- Check Supabase GitHub provider configuration
- Verify callback URLs are correctly set
- Check Supabase logs for detailed errors

**3. Clone/Upload fails**
- Ensure workspace directory is writable
- Check user permissions
- Verify Git is installed in the ai-orchestrator container

**4. Extension not appearing**
- Check if the extension path is correct in `extensions.json`
- Verify the extension is compiled (`npm run compile`)
- Restart code-server container

### Debugging

**Check GitHub OAuth Status**:
```bash
curl http://localhost:8000/auth/github/status
```

**Check Workspace Info**:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/projects/workspace/info
```

**View Service Logs**:
```bash
docker-compose logs ai-orchestrator
docker-compose logs code-server
```

## 🔒 Security Considerations

1. **Environment Variables**: Never commit real credentials to version control
2. **HTTPS in Production**: Use HTTPS for all OAuth callbacks in production
3. **Token Management**: JWT tokens have configurable expiry times
4. **Workspace Isolation**: User workspaces are isolated by user ID
5. **Path Traversal Protection**: File paths are validated to prevent directory traversal

## 🚀 Advanced Configuration

### Custom OAuth Redirect

For production deployments, update the redirect URI:

```bash
GITHUB_REDIRECT_URI=https://yourdomain.com/auth/github/callback
```

### Workspace Customization

Customize the workspace path:

```bash
WORKSPACE_PATH=/custom/workspace/path
```

### Multiple GitHub Organizations

The integration works with any GitHub repository the authenticated user has access to, including:
- Personal repositories
- Organization repositories (public and private)
- Forked repositories

## 📚 Next Steps

- Configure additional Git providers (GitLab, Bitbucket)
- Add repository templates
- Implement project scaffolding
- Add collaborative features

## 💡 Tips

- Use meaningful project names when cloning
- Regularly clean up unused projects
- Leverage VS Code's Git integration for version control
- Use the integrated terminal for Git commands

---

For more information, see the complete [Git Integration Documentation](./Gitintegration.md).