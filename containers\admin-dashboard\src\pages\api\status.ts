/**
 * System Status API Route
 * Provides real-time system status information by checking actual service health
 */

import type { NextApiRequest, NextApiResponse } from 'next';

interface ComponentHealth {
  name: string;
  status: string;
  response_time_ms: number;
  details: Record<string, any>;
}

interface StatusResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  services: {
    'ai-orchestrator': 'connected' | 'disconnected' | 'unknown';
    'database': 'connected' | 'disconnected' | 'unknown';
    'redis': 'connected' | 'disconnected' | 'unknown';
    'ollama': 'connected' | 'disconnected' | 'unknown';
  };
  timestamp: string;
  response_time_ms?: number;
  details?: Record<string, any>;
}

/**
 * Get AI Orchestrator base URL from environment or default
 */
function getAIOrchestatorURL(): string {
  // In development, use direct container communication
  if (process.env.NODE_ENV === 'development') {
    return process.env.API_BASE_URL || 'http://ai-orchestrator:8000';
  }
  // In production, use the public API URL
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
}

/**
 * Check individual service health
 */
async function checkServiceHealth(
  serviceName: string,
  url: string,
  timeout: number = 5000
): Promise<{ status: 'connected' | 'disconnected'; details?: any }> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      return {
        status: 'connected',
        details: {
          status_code: response.status,
          response_time_ms: data.response_time_ms,
          service_status: data.status
        }
      };
    } else {
      return {
        status: 'disconnected',
        details: {
          status_code: response.status,
          error: `HTTP ${response.status}`
        }
      };
    }
  } catch (error) {
    return {
      status: 'disconnected',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        timeout: timeout
      }
    };
  }
}

/**
 * Check AI Orchestrator general health
 */
async function checkAIOrchestatorHealth(baseUrl: string): Promise<{ status: 'connected' | 'disconnected'; details?: any }> {
  try {
    const response = await fetch(`${baseUrl}/api/v1/health`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      signal: AbortSignal.timeout(5000),
    });

    if (response.ok) {
      const data = await response.json();
      return {
        status: 'connected',
        details: {
          status_code: response.status,
          service_status: data.status,
          uptime_seconds: data.details?.uptime_seconds
        }
      };
    } else {
      return {
        status: 'disconnected',
        details: {
          status_code: response.status,
          error: `HTTP ${response.status}`
        }
      };
    }
  } catch (error) {
    return {
      status: 'disconnected',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

/**
 * Status Handler
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<StatusResponse>
) {
  const startTime = Date.now();

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({
      status: 'unhealthy',
      services: {
        'ai-orchestrator': 'unknown',
        'database': 'unknown',
        'redis': 'unknown',
        'ollama': 'unknown'
      },
      timestamp: new Date().toISOString()
    });
  }

  try {
    const baseUrl = getAIOrchestatorURL();

    // Perform parallel health checks
    const [
      aiOrchestratorResult,
      databaseResult,
      redisResult,
      ollamaResult
    ] = await Promise.allSettled([
      checkAIOrchestatorHealth(baseUrl),
      checkServiceHealth('database', `${baseUrl}/api/v1/health/database`),
      checkServiceHealth('redis', `${baseUrl}/api/v1/health/redis`),
      checkServiceHealth('ollama', `${baseUrl}/api/v1/health/llm/local`)
    ]);

    // Extract results with fallback to disconnected
    const aiOrchestrator = aiOrchestratorResult.status === 'fulfilled'
      ? aiOrchestratorResult.value.status
      : 'disconnected';

    const database = databaseResult.status === 'fulfilled'
      ? databaseResult.value.status
      : 'disconnected';

    const redis = redisResult.status === 'fulfilled'
      ? redisResult.value.status
      : 'disconnected';

    const ollama = ollamaResult.status === 'fulfilled'
      ? ollamaResult.value.status
      : 'disconnected';

    // Determine overall system status
    const connectedServices = [aiOrchestrator, database, redis, ollama].filter(
      status => status === 'connected'
    ).length;

    let overallStatus: 'healthy' | 'unhealthy' | 'degraded';
    if (connectedServices === 4) {
      overallStatus = 'healthy';
    } else if (connectedServices >= 2) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'unhealthy';
    }

    const responseTime = Date.now() - startTime;

    // Collect detailed information for debugging
    const details = {
      checks_performed: 4,
      connected_services: connectedServices,
      ai_orchestrator: aiOrchestratorResult.status === 'fulfilled' ? aiOrchestratorResult.value.details : null,
      database: databaseResult.status === 'fulfilled' ? databaseResult.value.details : null,
      redis: redisResult.status === 'fulfilled' ? redisResult.value.details : null,
      ollama: ollamaResult.status === 'fulfilled' ? ollamaResult.value.details : null,
      base_url: baseUrl
    };

    res.status(200).json({
      status: overallStatus,
      services: {
        'ai-orchestrator': aiOrchestrator,
        'database': database,
        'redis': redis,
        'ollama': ollama
      },
      timestamp: new Date().toISOString(),
      response_time_ms: responseTime,
      details
    });

  } catch (error) {
    // Handle unexpected errors
    const responseTime = Date.now() - startTime;

    res.status(500).json({
      status: 'unhealthy',
      services: {
        'ai-orchestrator': 'unknown',
        'database': 'unknown',
        'redis': 'unknown',
        'ollama': 'unknown'
      },
      timestamp: new Date().toISOString(),
      response_time_ms: responseTime,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        type: 'health_check_failure'
      }
    });
  }
}