-- =====================================================================================
-- Supabase Initial Schema Migration
-- Version: 001
-- Description: Create core tables with Row Level Security for AI Coding Agent
-- =====================================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================================================
-- AUTHENTICATION SCHEMA (auth.users is provided by Supabase)
-- =====================================================================================

-- Create auth schema if it doesn't exist (usually created by Supabase)
CREATE SCHEMA IF NOT EXISTS auth;

-- =====================================================================================
-- PUBLIC SCHEMA TABLES
-- =====================================================================================

-- User profiles (extends auth.users with application-specific data)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE,
    full_name TEXT,
    role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'developer', 'viewer')),
    avatar_url TEXT,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS to user profiles
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view own profile"
    ON public.user_profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
    ON public.user_profiles
    FOR UPDATE
    USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile"
    ON public.user_profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

-- =====================================================================================
-- PROJECTS AND WORKSPACES
-- =====================================================================================

-- Projects table
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS to projects
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- Projects policies
CREATE POLICY "Users can view own projects"
    ON public.projects
    FOR SELECT
    USING (owner_id = auth.uid());

CREATE POLICY "Users can create projects"
    ON public.projects
    FOR INSERT
    WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update own projects"
    ON public.projects
    FOR UPDATE
    USING (owner_id = auth.uid());

CREATE POLICY "Users can delete own projects"
    ON public.projects
    FOR DELETE
    USING (owner_id = auth.uid());

-- =====================================================================================
-- DOCUMENT MANAGEMENT (for RAG)
-- =====================================================================================

-- Documents table
CREATE TABLE IF NOT EXISTS public.documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    content TEXT,
    file_path TEXT,
    file_type TEXT,
    file_size INTEGER,
    owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS to documents
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;

-- Documents policies
CREATE POLICY "Users can view own documents"
    ON public.documents
    FOR SELECT
    USING (
        owner_id = auth.uid() OR
        project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
    );

CREATE POLICY "Users can create documents in own projects"
    ON public.documents
    FOR INSERT
    WITH CHECK (
        owner_id = auth.uid() AND
        project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
    );

CREATE POLICY "Users can update own documents"
    ON public.documents
    FOR UPDATE
    USING (
        owner_id = auth.uid() OR
        project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
    );

CREATE POLICY "Users can delete own documents"
    ON public.documents
    FOR DELETE
    USING (
        owner_id = auth.uid() OR
        project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
    );

-- =====================================================================================
-- VECTOR EMBEDDINGS (for AI/RAG functionality)
-- =====================================================================================

-- Document sections with vector embeddings
CREATE TABLE IF NOT EXISTS public.document_sections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_id UUID REFERENCES public.documents(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    embedding vector(384), -- Using 384 dimensions for sentence-transformers
    section_index INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS to document sections
ALTER TABLE public.document_sections ENABLE ROW LEVEL SECURITY;

-- Document sections policies (inherit from documents)
CREATE POLICY "Users can query their document sections"
    ON public.document_sections
    FOR SELECT
    USING (
        document_id IN (
            SELECT id FROM public.documents
            WHERE owner_id = auth.uid() OR
                  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
        )
    );

CREATE POLICY "Users can create sections in their documents"
    ON public.document_sections
    FOR INSERT
    WITH CHECK (
        document_id IN (
            SELECT id FROM public.documents
            WHERE owner_id = auth.uid() OR
                  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
        )
    );

CREATE POLICY "Users can update their document sections"
    ON public.document_sections
    FOR UPDATE
    USING (
        document_id IN (
            SELECT id FROM public.documents
            WHERE owner_id = auth.uid() OR
                  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
        )
    );

CREATE POLICY "Users can delete their document sections"
    ON public.document_sections
    FOR DELETE
    USING (
        document_id IN (
            SELECT id FROM public.documents
            WHERE owner_id = auth.uid() OR
                  project_id IN (SELECT id FROM public.projects WHERE owner_id = auth.uid())
        )
    );

-- =====================================================================================
-- AI AGENT EXECUTION LOGS
-- =====================================================================================

-- Agent execution logs
CREATE TABLE IF NOT EXISTS public.agent_executions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    agent_name TEXT NOT NULL,
    task_description TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    input_data JSONB DEFAULT '{}',
    output_data JSONB DEFAULT '{}',
    error_message TEXT,
    execution_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS to agent executions
ALTER TABLE public.agent_executions ENABLE ROW LEVEL SECURITY;

-- Agent executions policies
CREATE POLICY "Users can view own agent executions"
    ON public.agent_executions
    FOR SELECT
    USING (user_id = auth.uid());

CREATE POLICY "Users can create agent executions"
    ON public.agent_executions
    FOR INSERT
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own agent executions"
    ON public.agent_executions
    FOR UPDATE
    USING (user_id = auth.uid());

-- =====================================================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================================================

-- User profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON public.user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON public.user_profiles(role);

-- Projects indexes
CREATE INDEX IF NOT EXISTS idx_projects_owner_id ON public.projects(owner_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON public.projects(created_at);

-- Documents indexes
CREATE INDEX IF NOT EXISTS idx_documents_project_id ON public.documents(project_id);
CREATE INDEX IF NOT EXISTS idx_documents_owner_id ON public.documents(owner_id);
CREATE INDEX IF NOT EXISTS idx_documents_file_type ON public.documents(file_type);
CREATE INDEX IF NOT EXISTS idx_documents_created_at ON public.documents(created_at);

-- Document sections indexes
CREATE INDEX IF NOT EXISTS idx_document_sections_document_id ON public.document_sections(document_id);
CREATE INDEX IF NOT EXISTS idx_document_sections_section_index ON public.document_sections(section_index);

-- Vector similarity search index (CRITICAL for performance)
CREATE INDEX IF NOT EXISTS idx_document_sections_embedding
    ON public.document_sections
    USING ivfflat (embedding vector_cosine_ops)
    WITH (lists = 100);

-- Agent executions indexes
CREATE INDEX IF NOT EXISTS idx_agent_executions_user_id ON public.agent_executions(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_executions_project_id ON public.agent_executions(project_id);
CREATE INDEX IF NOT EXISTS idx_agent_executions_status ON public.agent_executions(status);
CREATE INDEX IF NOT EXISTS idx_agent_executions_agent_name ON public.agent_executions(agent_name);
CREATE INDEX IF NOT EXISTS idx_agent_executions_created_at ON public.agent_executions(created_at);

-- =====================================================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at
    BEFORE UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at
    BEFORE UPDATE ON public.documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_executions_updated_at
    BEFORE UPDATE ON public.agent_executions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================================================
-- INITIAL DATA
-- =====================================================================================

-- Create default roles (if they don't exist)
-- Note: In production, this would be handled by Supabase auth configuration

-- Insert default user roles if the table is empty
INSERT INTO auth.roles (id, name)
VALUES
    ('11111111-1111-1111-1111-111111111111', 'authenticated'),
    ('22222222-2222-2222-2222-222222222222', 'anon'),
    ('33333333-3333-3333-3333-333333333333', 'service_role')
ON CONFLICT (id) DO NOTHING;

-- =====================================================================================
-- GRANTS AND PERMISSIONS
-- =====================================================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO authenticated, anon;

-- Grant table permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.projects TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.documents TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.document_sections TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.agent_executions TO authenticated;

-- Grant sequence permissions
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant function permissions
GRANT EXECUTE ON FUNCTION update_updated_at_column() TO authenticated;

-- Anonymous users can only read certain public data (if needed)
-- For now, restrict anon to no access - all operations require authentication
-- GRANT SELECT ON public.some_public_table TO anon;

-- =====================================================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================================================

COMMENT ON TABLE public.user_profiles IS 'Extended user profile information beyond auth.users';
COMMENT ON TABLE public.projects IS 'User projects and workspaces for organizing work';
COMMENT ON TABLE public.documents IS 'Documents and files within projects for RAG processing';
COMMENT ON TABLE public.document_sections IS 'Document sections with vector embeddings for semantic search';
COMMENT ON TABLE public.agent_executions IS 'Log of AI agent task executions and results';

COMMENT ON COLUMN public.document_sections.embedding IS 'Vector embedding (384 dimensions) for semantic similarity search';
COMMENT ON INDEX idx_document_sections_embedding IS 'IVFFLAT index for fast vector similarity search';

-- =====================================================================================
-- MIGRATION COMPLETE
-- =====================================================================================