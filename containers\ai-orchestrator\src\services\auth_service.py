"""
Authentication Service for AI Coding Agent with Supabase.

This module provides comprehensive authentication functionality including
JWT token management, user registration/login, role-based access control,
and integration with Supabase Auth.

Author: AI Coding Agent
Version: 1.0.0
"""

import os
from src.core.config import settings
import jwt
import json
from typing import Optional, Dict, Any, List, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging
from functools import wraps
from contextlib import asynccontextmanager

# FastAPI imports
from fastapi import HTTPException, Depends, Request, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

# Supabase imports
try:
    from supabase import Client
    from gotrue.types import User, Session
except ImportError:
    raise ImportError(
        "Supabase not installed. Install with: pip install supabase"
    )

# Internal imports
from .supabase_service import SupabaseService, get_supabase_service

# Configure logging
logger = logging.getLogger(__name__)


class UserRole(str, Enum):
    """User roles with hierarchical permissions."""
    VIEWER = "viewer"
    USER = "user"
    DEVELOPER = "developer"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


class AuthProvider(str, Enum):
    """Supported authentication providers."""
    EMAIL = "email"
    GOOGLE = "google"
    GITHUB = "github"
    OAUTH = "oauth"


@dataclass
class AuthConfig:
    """Authentication configuration."""
    jwt_secret: str
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    password_min_length: int = 8
    require_email_verification: bool = True
    allow_signup: bool = True
    max_login_attempts: int = 5
    login_attempt_window_minutes: int = 15

    @classmethod
    def from_env(cls) -> 'AuthConfig':
        """Create configuration from centralized settings."""
        # All configuration values from centralized settings
        return cls(
            jwt_secret=settings.JWT_SECRET,
            jwt_algorithm=settings.JWT_ALGORITHM,
            access_token_expire_minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES,
            refresh_token_expire_days=settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS,
            password_min_length=settings.PASSWORD_MIN_LENGTH,
            require_email_verification=settings.REQUIRE_EMAIL_VERIFICATION,
            allow_signup=settings.ALLOW_SIGNUP,
            max_login_attempts=settings.MAX_LOGIN_ATTEMPTS,
            login_attempt_window_minutes=settings.LOGIN_ATTEMPT_WINDOW_MINUTES
        )


@dataclass
class UserProfile:
    """User profile information."""
    id: str
    email: str
    username: Optional[str] = None
    full_name: Optional[str] = None
    role: UserRole = UserRole.USER
    avatar_url: Optional[str] = None
    preferences: Dict[str, Any] = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    email_verified: bool = False
    is_active: bool = True
    last_login: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'email': self.email,
            'username': self.username,
            'full_name': self.full_name,
            'role': self.role.value,
            'avatar_url': self.avatar_url,
            'preferences': self.preferences,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'email_verified': self.email_verified,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }


@dataclass
class AuthTokens:
    """Authentication tokens."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int = 1800  # 30 minutes default
    expires_at: Optional[datetime] = None


class AuthenticationError(Exception):
    """Authentication-related errors."""
    pass


class AuthorizationError(Exception):
    """Authorization-related errors."""
    pass


class TokenExpiredError(AuthenticationError):
    """Token expiration errors."""
    pass


class InvalidTokenError(AuthenticationError):
    """Invalid token errors."""
    pass


class AuthService:
    """
    Comprehensive authentication service providing:
    - User registration and login
    - JWT token management
    - Role-based access control
    - Password validation
    - Session management
    """

    def __init__(
        self,
        supabase_service: SupabaseService,
        config: Optional[AuthConfig] = None
    ):
        """
        Initialize authentication service.

        Args:
            supabase_service: Supabase service instance.
            config: Authentication configuration.
        """
        self.supabase_service = supabase_service
        self.config = config or AuthConfig.from_env()

        # Security bearer for FastAPI
        self.security = HTTPBearer(auto_error=False)

        logger.info("Authentication service initialized")

    async def cleanup(self) -> None:
        """
        Clean up authentication service resources.

        This method should be called when shutting down the service
        to ensure proper cleanup of any resources.
        """
        try:
            # Clear any cached tokens or sessions if needed
            logger.info("Authentication service cleanup completed")
        except Exception as e:
            logger.warning(f"Error during auth service cleanup: {str(e)}")

    # ==================================================================================
    # USER REGISTRATION AND LOGIN
    # ==================================================================================

    async def register_user(
        self,
        email: str,
        password: str,
        username: Optional[str] = None,
        full_name: Optional[str] = None,
        role: UserRole = UserRole.USER,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[UserProfile, AuthTokens]:
        """
        Register a new user.

        Args:
            email: User email address.
            password: User password.
            username: Optional username.
            full_name: Optional full name.
            role: User role.
            metadata: Additional metadata.

        Returns:
            Tuple of user profile and auth tokens.

        Raises:
            AuthenticationError: If registration fails.
        """
        if not self.config.allow_signup:
            raise AuthenticationError("User registration is disabled")

        try:
            # Validate password
            self._validate_password(password)

            # Prepare user metadata
            user_metadata = {
                'username': username,
                'full_name': full_name,
                'role': role.value,
                **(metadata or {})
            }

            # Register with Supabase Auth
            client = self.supabase_service.service_client
            response = client.auth.sign_up({
                'email': email,
                'password': password,
                'options': {
                    'data': user_metadata
                }
            })

            if not response.user:
                raise AuthenticationError("User registration failed")

            # Create user profile
            user_profile = await self._create_user_profile(response.user, user_metadata)

            # Generate tokens
            tokens = await self._generate_tokens(user_profile)

            logger.info(f"User registered successfully: {email}")
            return user_profile, tokens

        except Exception as e:
            logger.error(f"User registration failed: {str(e)}")
            raise AuthenticationError(f"Registration failed: {str(e)}") from e

    async def login_user(
        self,
        email: str,
        password: str,
        remember_me: bool = False
    ) -> Tuple[UserProfile, AuthTokens]:
        """
        Authenticate user login.

        Args:
            email: User email address.
            password: User password.
            remember_me: Whether to extend token expiration.

        Returns:
            Tuple of user profile and auth tokens.

        Raises:
            AuthenticationError: If login fails.
        """
        try:
            # Authenticate with Supabase
            client = self.supabase_service.service_client
            response = client.auth.sign_in_with_password({
                'email': email,
                'password': password
            })

            if not response.user or not response.session:
                raise AuthenticationError("Invalid email or password")

            # Get user profile
            user_profile = await self._get_user_profile(response.user.id)
            if not user_profile:
                # Create profile if it doesn't exist
                user_profile = await self._create_user_profile(response.user)

            # Update last login
            await self._update_last_login(user_profile.id)

            # Generate tokens
            expires_minutes = (
                self.config.refresh_token_expire_days * 24 * 60
                if remember_me
                else self.config.access_token_expire_minutes
            )
            tokens = await self._generate_tokens(user_profile, expires_minutes)

            logger.info(f"User login successful: {email}")
            return user_profile, tokens

        except Exception as e:
            logger.error(f"User login failed: {str(e)}")
            raise AuthenticationError(f"Login failed: {str(e)}") from e

    async def logout_user(self, access_token: str) -> bool:
        """
        Logout user and invalidate tokens.

        Args:
            access_token: User's access token.

        Returns:
            True if logout successful.
        """
        try:
            # Invalidate Supabase session
            client = self.supabase_service.get_client(access_token)
            client.auth.sign_out()

            logger.info("User logout successful")
            return True

        except Exception as e:
            logger.warning(f"Logout warning: {str(e)}")
            return False

    # ==================================================================================
    # TOKEN MANAGEMENT
    # ==================================================================================

    async def _generate_tokens(
        self,
        user_profile: UserProfile,
        expires_minutes: Optional[int] = None
    ) -> AuthTokens:
        """
        Generate JWT access and refresh tokens.

        Args:
            user_profile: User profile to generate tokens for.
            expires_minutes: Token expiration in minutes.

        Returns:
            Generated authentication tokens.

        Raises:
            AuthenticationError: If token generation fails.
        """
        expires_minutes = expires_minutes or self.config.access_token_expire_minutes
        expires_at = datetime.utcnow() + timedelta(minutes=expires_minutes)

        # Access token payload
        access_payload = {
            'sub': user_profile.id,
            'email': user_profile.email,
            'role': user_profile.role.value,
            'iat': datetime.utcnow(),
            'exp': expires_at,
            'type': 'access'
        }

        # Refresh token payload (longer expiration)
        refresh_expires_at = datetime.utcnow() + timedelta(days=self.config.refresh_token_expire_days)
        refresh_payload = {
            'sub': user_profile.id,
            'iat': datetime.utcnow(),
            'exp': refresh_expires_at,
            'type': 'refresh'
        }

        # Generate tokens
        access_token = jwt.encode(
            access_payload,
            self.config.jwt_secret,
            algorithm=self.config.jwt_algorithm
        )

        refresh_token = jwt.encode(
            refresh_payload,
            self.config.jwt_secret,
            algorithm=self.config.jwt_algorithm
        )

        return AuthTokens(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=expires_minutes * 60,
            expires_at=expires_at
        )

    async def verify_token(self, token: str) -> Dict[str, Any]:
        """
        Verify and decode JWT token.

        Args:
            token: JWT token to verify.

        Returns:
            Decoded token payload.

        Raises:
            TokenExpiredError: If token is expired.
            InvalidTokenError: If token is invalid.
        """
        try:
            payload = jwt.decode(
                token,
                self.config.jwt_secret,
                algorithms=[self.config.jwt_algorithm]
            )

            # Check token type
            if payload.get('type') not in ['access', 'refresh']:
                raise InvalidTokenError("Invalid token type")

            return payload

        except jwt.ExpiredSignatureError:
            raise TokenExpiredError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise InvalidTokenError(f"Invalid token: {str(e)}")

    async def refresh_access_token(self, refresh_token: str) -> AuthTokens:
        """
        Refresh access token using refresh token.

        Args:
            refresh_token: Valid refresh token.

        Returns:
            New auth tokens.

        Raises:
            TokenExpiredError: If refresh token is expired.
            InvalidTokenError: If refresh token is invalid.
        """
        try:
            # Verify refresh token
            payload = await self.verify_token(refresh_token)

            if payload.get('type') != 'refresh':
                raise InvalidTokenError("Invalid refresh token")

            # Get user profile
            user_id = payload.get('sub')
            if not user_id:
                raise AuthenticationError("Invalid token: missing user ID")

            user_profile = await self._get_user_profile(user_id)

            if not user_profile:
                raise AuthenticationError("User not found")

            # Generate new tokens
            tokens = await self._generate_tokens(user_profile)

            logger.info(f"Token refreshed for user: {user_profile.email}")
            return tokens

        except (TokenExpiredError, InvalidTokenError):
            raise
        except Exception as e:
            logger.error(f"Token refresh failed: {str(e)}")
            raise AuthenticationError(f"Token refresh failed: {str(e)}") from e

    # ==================================================================================
    # USER PROFILE MANAGEMENT
    # ==================================================================================

    async def _create_user_profile(
        self,
        auth_user: User,
        metadata: Optional[Dict[str, Any]] = None
    ) -> UserProfile:
        """
        Create user profile in database.

        Args:
            auth_user: Authenticated user from Supabase.
            metadata: Additional user metadata.

        Returns:
            Created user profile.

        Raises:
            AuthenticationError: If profile creation fails or user data is invalid.
        """
        try:
            profile_data = {
                'id': auth_user.id,
                'username': metadata.get('username') if metadata else None,
                'full_name': metadata.get('full_name') if metadata else None,
                'role': metadata.get('role', UserRole.USER.value) if metadata else UserRole.USER.value,
                'preferences': metadata.get('preferences', {}) if metadata else {}
            }

            # Insert into database
            client = self.supabase_service.service_client
            response = client.table('user_profiles').insert(profile_data).execute()

            if not response.data:
                raise AuthenticationError("Failed to create user profile")

            # Validate email
            email = auth_user.email
            if not email:
                raise AuthenticationError("User email is required but not found")

            return UserProfile(
                id=auth_user.id,
                email=email,
                username=profile_data['username'],
                full_name=profile_data['full_name'],
                role=UserRole(profile_data['role']),
                preferences=profile_data['preferences'],
                email_verified=auth_user.email_confirmed_at is not None,
                created_at=datetime.utcnow()
            )

        except Exception as e:
            logger.error(f"Failed to create user profile: {str(e)}")
            raise AuthenticationError(f"Profile creation failed: {str(e)}") from e

    async def _get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """
        Get user profile from database.

        Args:
            user_id: User ID to retrieve profile for.

        Returns:
            User profile if found, None otherwise.

        Raises:
            ValueError: If user_id is empty or invalid.
        """
        try:
            # Validate input
            if not user_id or not user_id.strip():
                raise ValueError("User ID cannot be empty")

            # Get from Supabase
            client = self.supabase_service.service_client
            response = client.table('user_profiles').select('*').eq('id', user_id).execute()

            if not response.data:
                return None

            profile_data = response.data[0]

            # Get auth user for email
            auth_response = client.auth.admin.get_user_by_id(user_id)
            auth_user = auth_response.user if auth_response else None

            # Get email from auth user or fallback
            email = '<EMAIL>'
            if auth_user and auth_user.email:
                email = auth_user.email

            return UserProfile(
                id=profile_data['id'],
                email=email,
                username=profile_data.get('username'),
                full_name=profile_data.get('full_name'),
                role=UserRole(profile_data.get('role', UserRole.USER.value)),
                avatar_url=profile_data.get('avatar_url'),
                preferences=profile_data.get('preferences', {}),
                created_at=datetime.fromisoformat(profile_data['created_at'].replace('Z', '+00:00')),
                updated_at=datetime.fromisoformat(profile_data['updated_at'].replace('Z', '+00:00')) if profile_data.get('updated_at') else None,
                email_verified=auth_user.email_confirmed_at is not None if auth_user else False
            )

        except Exception as e:
            logger.error(f"Failed to get user profile: {str(e)}")
            return None

    async def _update_last_login(self, user_id: str) -> None:
        """Update user's last login timestamp."""
        try:
            client = self.supabase_service.service_client
            client.table('user_profiles').update({
                'updated_at': datetime.utcnow().isoformat()
            }).eq('id', user_id).execute()

        except Exception as e:
            logger.warning(f"Failed to update last login: {str(e)}")

    # ==================================================================================
    # PASSWORD AND VALIDATION
    # ==================================================================================

    def _validate_password(self, password: str) -> None:
        """
        Validate password strength according to security requirements.

        Args:
            password: Password to validate.

        Raises:
            AuthenticationError: If password doesn't meet requirements.
        """
        if not password:
            raise AuthenticationError("Password is required")

        if len(password) < self.config.password_min_length:
            raise AuthenticationError(
                f"Password must be at least {self.config.password_min_length} characters long"
            )

        # Check for uppercase letter
        if not any(c.isupper() for c in password):
            raise AuthenticationError("Password must contain at least one uppercase letter")

        # Check for lowercase letter
        if not any(c.islower() for c in password):
            raise AuthenticationError("Password must contain at least one lowercase letter")

        # Check for digit
        if not any(c.isdigit() for c in password):
            raise AuthenticationError("Password must contain at least one digit")

        # Check for special character
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            raise AuthenticationError("Password must contain at least one special character")

    # ==================================================================================
    # FASTAPI DEPENDENCIES
    # ==================================================================================

    async def get_current_user(
        self,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
    ) -> UserProfile:
        """
        FastAPI dependency to get current authenticated user.

        Args:
            credentials: HTTP bearer credentials.

        Returns:
            Current user profile.

        Raises:
            HTTPException: If authentication fails.
        """
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication credentials required",
                headers={"WWW-Authenticate": "Bearer"}
            )

        try:
            # Verify token
            payload = await self.verify_token(credentials.credentials)

            # Get user profile
            user_id = payload.get('sub')
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: missing user ID"
                )

            user_profile = await self._get_user_profile(user_id)

            if not user_profile:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found"
                )

            return user_profile

        except (TokenExpiredError, InvalidTokenError) as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=str(e),
                headers={"WWW-Authenticate": "Bearer"}
            )
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed"
            )

    def require_role(self, required_role: UserRole):
        """
        Decorator to require specific user role.

        Args:
            required_role: Minimum required role.

        Returns:
            FastAPI dependency function.
        """
        async def role_checker(
            current_user: UserProfile = Depends(self.get_current_user)
        ) -> UserProfile:
            role_hierarchy = {
                UserRole.VIEWER: 0,
                UserRole.USER: 1,
                UserRole.DEVELOPER: 2,
                UserRole.ADMIN: 3,
                UserRole.SUPER_ADMIN: 4
            }

            if role_hierarchy.get(current_user.role, 0) < role_hierarchy.get(required_role, 0):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required role: {required_role.value}"
                )

            return current_user

        return role_checker


# Global service instance
_auth_service: Optional[AuthService] = None


async def get_auth_service() -> AuthService:
    """Get global authentication service instance."""
    global _auth_service

    if _auth_service is None:
        supabase_service = await get_supabase_service()
        _auth_service = AuthService(supabase_service)

    return _auth_service


# FastAPI dependencies
async def get_current_user(
    auth_service: AuthService = Depends(get_auth_service),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> UserProfile:
    """FastAPI dependency for current user."""
    return await auth_service.get_current_user(credentials)


def require_role(role: UserRole):
    """FastAPI dependency for role-based access control."""
    async def role_dependency(
        auth_service: AuthService = Depends(get_auth_service)
    ):
        return auth_service.require_role(role)

    return role_dependency