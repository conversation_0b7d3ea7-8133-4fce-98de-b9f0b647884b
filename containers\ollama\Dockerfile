# syntax=docker/dockerfile:1
# Ollama container for AI model serving
# Note: For production, consider running Ollama on host for better hardware access
# This container setup is for development and testing purposes

FROM ollama/ollama:latest

# Install minimal runtime utilities (curl for health checks only)
USER root
RUN apt-get update && apt-get install -y \
  curl \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Create ollama user and directories with proper permissions
RUN groupadd -r ollama && useradd -r -g ollama ollama
RUN mkdir -p /home/<USER>/.ollama && \
  chown -R ollama:ollama /home/<USER>/.ollama

# Copy model manifests for preloading
COPY --chown=ollama:ollama model-manifests/ /opt/ollama/models/

# Copy entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Switch to non-root user for security
USER ollama

# Set environment variables
ENV OLLAMA_HOST=0.0.0.0
ENV OLLAMA_PORT=11434
ENV OLLAMA_MODELS=/home/<USER>/.ollama/models

# Expose Ollama API port
EXPOSE 11434

# Health check for Ollama service
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
  CMD curl -f http://localhost:11434/api/tags || exit 1

# Set security and project labels
LABEL org.opencontainers.image.title="AI Coding Agent - Ollama" \
  org.opencontainers.image.description="Local LLM server for AI code generation and analysis" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="ollama"

# Volume for model storage
VOLUME ["/home/<USER>/.ollama"]

# Use custom entrypoint for model preloading
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["ollama", "serve"]