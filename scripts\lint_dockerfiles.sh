#!/bin/bash
# Dockerfile linter script for AI Coding Agent Project
# Uses hadolint to validate Dockerfiles against best practices

set -e

echo "🔍 Linting Dockerfiles with hadolint..."

# Find all Dockerfiles in the project
DOCKERFILES=$(find . -name "Dockerfile" -not -path "./.git/*" -not -path "./venv/*" -not -path "./.venv/*")

if [ -z "$DOCKERFILES" ]; then
    echo "❌ No Dockerfiles found in the project"
    exit 1
fi

# Check if hadolint is installed
if ! command -v hadolint &> /dev/null; then
    echo "❌ hadolint is not installed"
    echo "Please install hadolint:"
    echo "  - macOS: brew install hadolint"
    echo "  - Linux: See https://github.com/hadolint/hadolint#install"
    echo "  - Windows: Use WSL or Docker: docker run --rm -i hadolint/hadolint < Dockerfile"
    exit 1
fi

ERRORS_FOUND=false

# Lint each Dockerfile
for dockerfile in $DOCKERFILES; do
    echo "📄 Linting $dockerfile"
    if ! hadolint "$dockerfile"; then
        echo "❌ Issues found in $dockerfile"
        ERRORS_FOUND=true
    else
        echo "✅ $dockerfile passed linting"
    fi
    echo ""
done

if [ "$ERRORS_FOUND" = true ]; then
    echo "❌ Dockerfile linting failed. Please fix the issues above."
    exit 1
else
    echo "✅ All Dockerfiles passed linting!"
    exit 0
fi
