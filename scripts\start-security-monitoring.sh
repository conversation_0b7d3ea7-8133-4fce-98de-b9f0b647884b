#!/bin/bash

# Security Monitoring Startup Script
# Initializes and starts security monitoring for the AI Coding Agent platform

set -e

echo "🔒 Starting AI Coding Agent Security Monitoring..."

# Configuration
SECURITY_MONITORING_ENABLED=${SECURITY_MONITORING_ENABLED:-true}
ALERT_THRESHOLD_CPU=${ALERT_THRESHOLD_CPU:-80.0}
ALERT_THRESHOLD_MEMORY=${ALERT_THRESHOLD_MEMORY:-90.0}
MAX_USER_CONTAINERS=${MAX_USER_CONTAINERS:-10}
MONITORING_INTERVAL_SECONDS=${MONITORING_INTERVAL_SECONDS:-30}

echo "📊 Security Monitoring Configuration:"
echo "  - Monitoring Enabled: $SECURITY_MONITORING_ENABLED"
echo "  - CPU Alert Threshold: $ALERT_THRESHOLD_CPU%"
echo "  - Memory Alert Threshold: $ALERT_THRESHOLD_MEMORY%"
echo "  - Max Containers per User: $MAX_USER_CONTAINERS"
echo "  - Monitoring Interval: $MONITORING_INTERVAL_SECONDS seconds"

# Check if security monitoring is enabled
if [ "$SECURITY_MONITORING_ENABLED" = "false" ]; then
    echo "⚠️  Security monitoring is disabled via configuration"
    echo "   Set SECURITY_MONITORING_ENABLED=true to enable"
    exit 0
fi

# Wait for services to be ready
echo "⏳ Waiting for required services..."

# Wait for Redis
echo "  - Checking Redis..."
timeout=60
while ! nc -z redis 6379 && [ $timeout -gt 0 ]; do
    sleep 1
    timeout=$((timeout-1))
done

if [ $timeout -eq 0 ]; then
    echo "❌ Redis is not available after 60 seconds"
    exit 1
fi
echo "  ✅ Redis is ready"

# Wait for PostgreSQL
echo "  - Checking PostgreSQL..."
timeout=60
while ! nc -z postgresql 5432 && [ $timeout -gt 0 ]; do
    sleep 1
    timeout=$((timeout-1))
done

if [ $timeout -eq 0 ]; then
    echo "❌ PostgreSQL is not available after 60 seconds"
    exit 1
fi
echo "  ✅ PostgreSQL is ready"

# Wait for AI Orchestrator API
echo "  - Checking AI Orchestrator API..."
timeout=120
while ! curl -s http://localhost:8000/health > /dev/null && [ $timeout -gt 0 ]; do
    sleep 1
    timeout=$((timeout-1))
done

if [ $timeout -eq 0 ]; then
    echo "❌ AI Orchestrator API is not available after 120 seconds"
    exit 1
fi
echo "  ✅ AI Orchestrator API is ready"

# Start security monitoring
echo "🚀 Starting security monitoring..."

# Make API call to start monitoring
response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${SECURITY_API_TOKEN:-}" \
    http://localhost:8000/api/v1/security/start-monitoring \
    || echo "error")

if [ "$response" = "error" ]; then
    echo "⚠️  Failed to start security monitoring via API"
    echo "   This may be normal if authentication is required"
    echo "   Security monitoring will start when the first authenticated request is made"
else
    echo "✅ Security monitoring started successfully"
fi

# Display security monitoring endpoints
echo ""
echo "🔍 Security Monitoring Endpoints:"
echo "  - Status:     GET  /api/v1/security/status"
echo "  - Events:     GET  /api/v1/security/events"
echo "  - Metrics:    GET  /api/v1/security/metrics"
echo "  - Alerts:     GET  /api/v1/security/alerts"
echo "  - Container:  GET  /api/v1/security/container/{id}/security"
echo ""

# Display health check endpoints
echo "🩺 Health Check Endpoints:"
echo "  - Basic:      GET  /health"
echo "  - Detailed:   GET  /api/v1/health/detailed"
echo "  - Ready:      GET  /api/v1/health/ready"
echo "  - Live:       GET  /api/v1/health/live"
echo "  - Metrics:    GET  /api/v1/health/metrics"
echo ""

echo "✅ Security monitoring initialization completed!"
echo "🔒 Your AI Coding Agent platform is now protected with:"
echo "   - Real-time container monitoring"
echo "   - Resource usage tracking"
echo "   - Suspicious process detection"
echo "   - Network activity monitoring"
echo "   - User behavior analysis"
echo "   - Automated threat detection"
echo ""
echo "💡 Tip: Check the Traefik dashboard at http://traefik.localhost:8090"
echo "       to monitor service health and routing status"