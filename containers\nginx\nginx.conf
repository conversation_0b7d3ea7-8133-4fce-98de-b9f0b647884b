# Nginx Configuration for AI Coding Agent Production Deployment
# Handles SSL termination, load balancing, and routing

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Worker configuration for production
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
    accept_mutex off;
}

http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Character encoding
    charset utf-8;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    # Access log
    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    client_max_body_size 64M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;

    # Hide nginx version
    server_tokens off;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        application/atom+xml
        application/geo+json
        application/javascript
        application/x-javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rdf+xml
        application/rss+xml
        application/xhtml+xml
        application/xml
        font/eot
        font/otf
        font/ttf
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:;" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=30r/m;
    limit_req_zone $binary_remote_addr zone=login_limit:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=general_limit:10m rate=60r/m;

    # Upstream servers
    upstream ai_orchestrator_backend {
        least_conn;
        server ai-orchestrator:8000 max_fails=3 fail_timeout=30s;
        # Add more backend servers for load balancing if needed
        # server ai-orchestrator-2:8000 max_fails=3 fail_timeout=30s;

        keepalive 32;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }

    upstream admin_dashboard_backend {
        least_conn;
        server admin-dashboard:3000 max_fails=3 fail_timeout=30s;
        # Add more frontend servers for load balancing if needed
        # server admin-dashboard-2:3000 max_fails=3 fail_timeout=30s;

        keepalive 32;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }

    # Default server (catch-all)
    server {
        listen 80 default_server;
        listen 443 ssl default_server;
        server_name _;

        # SSL configuration (if certificates are available)
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        return 444;  # Close connection without response
    }

    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name api.your-domain.com admin.your-domain.com;

        # Let's Encrypt ACME challenge
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }

        # Redirect all other HTTP requests to HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # API Server (ai-orchestrator)
    server {
        listen 443 ssl http2;
        server_name api.your-domain.com;

        # SSL configuration
        ssl_certificate /etc/nginx/ssl/api_cert.pem;
        ssl_certificate_key /etc/nginx/ssl/api_key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # HSTS
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # API-specific security headers
        add_header X-API-Version "1.0" always;
        add_header Access-Control-Allow-Origin "$http_origin" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;

        # Handle preflight requests
        location / {
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "$http_origin" always;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
                add_header Access-Control-Allow-Credentials "true" always;
                add_header Access-Control-Max-Age "1728000" always;
                add_header Content-Length "0" always;
                add_header Content-Type "text/plain charset=UTF-8" always;
                return 204;
            }

            # Rate limiting for API endpoints
            limit_req zone=api_limit burst=10 nodelay;

            # Proxy to ai-orchestrator
            proxy_pass http://ai_orchestrator_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_cache_bypass $http_upgrade;

            # Timeout settings
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;

            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;
        }

        # Health check endpoint (no rate limiting)
        location /health {
            proxy_pass http://ai_orchestrator_backend/health;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            access_log off;
        }

        # Metrics endpoint (restricted access)
        location /metrics {
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;

            proxy_pass http://ai_orchestrator_backend:9090/metrics;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
        }
    }

    # Admin Dashboard (admin-dashboard)
    server {
        listen 443 ssl http2;
        server_name admin.your-domain.com;

        # SSL configuration
        ssl_certificate /etc/nginx/ssl/admin_cert.pem;
        ssl_certificate_key /etc/nginx/ssl/admin_key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # HSTS
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Root location
        location / {
            limit_req zone=general_limit burst=20 nodelay;

            proxy_pass http://admin_dashboard_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_cache_bypass $http_upgrade;

            # Timeout settings
            proxy_connect_timeout 5s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;

            # Next.js specific settings
            proxy_buffering on;
            proxy_buffer_size 64k;
            proxy_buffers 8 64k;
            proxy_busy_buffers_size 64k;
        }

        # Static assets caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://admin_dashboard_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;

            # Cache static assets
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status "STATIC";

            # Gzip compression for static assets
            gzip_static on;
        }

        # Next.js API routes
        location /api/ {
            limit_req zone=api_limit burst=15 nodelay;

            proxy_pass http://admin_dashboard_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # No caching for API routes
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Health check endpoint
        location /api/health {
            proxy_pass http://admin_dashboard_backend/api/health;
            proxy_http_version 1.1;
            proxy_set_header Host $host;

            access_log off;
        }

        # Next.js HMR for development (only if in dev mode)
        location /_next/webpack-hmr {
            proxy_pass http://admin_dashboard_backend/_next/webpack-hmr;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }
    }

    # Development-only server (when not using custom domains)
    server {
        listen 80;
        server_name localhost 127.0.0.1;

        # API endpoints
        location /api/ {
            limit_req zone=api_limit burst=10 nodelay;

            proxy_pass http://ai_orchestrator_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Admin dashboard
        location / {
            limit_req zone=general_limit burst=20 nodelay;

            proxy_pass http://admin_dashboard_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
    }
}