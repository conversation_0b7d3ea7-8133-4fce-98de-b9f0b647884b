# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: Integration tests for the complete LLM system

import pytest
import asyncio
import os
import httpx
from typing import Dict, Any
from unittest.mock import patch, AsyncMock

# Test configuration
TEST_BASE_URL = "http://localhost:8000"
TEST_TIMEOUT = 30


class TestLLMIntegration:
    """Integration tests for the complete enhanced LLM system."""

    @pytest.fixture(scope="class")
    def event_loop(self):
        """Create an event loop for the test session."""
        loop = asyncio.new_event_loop()
        yield loop
        loop.close()

    @pytest.fixture(scope="class")
    async def http_client(self):
        """Create HTTP client for testing."""
        async with httpx.AsyncClient(
            base_url=TEST_BASE_URL,
            timeout=TEST_TIMEOUT
        ) as client:
            yield client

    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        # In a real test, you'd use actual auth tokens
        return {"Authorization": "Bearer test-token"}

    @pytest.mark.asyncio
    async def test_service_startup(self, http_client):
        """Test that the service starts up correctly."""
        try:
            response = await http_client.get("/health")
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "ok"
            assert data["service"] == "ai-orchestrator"
            assert data["version"] == "2.0.0"
        except httpx.ConnectError:
            pytest.skip("Service not running - start with docker-compose for integration tests")

    @pytest.mark.asyncio
    async def test_root_endpoint(self, http_client):
        """Test root endpoint returns correct information."""
        response = await http_client.get("/")
        assert response.status_code == 200

        data = response.json()
        assert "AI Coding Agent Orchestrator" in data["message"]
        assert data["version"] == "2.0.0"
        assert "Enhanced LLM integration" in data["features"]
        assert "endpoints" in data

        # Check that all expected endpoints are listed
        expected_endpoints = ["health", "llm_health", "providers", "models", "generate"]
        for endpoint in expected_endpoints:
            assert endpoint in data["endpoints"]

    @pytest.mark.asyncio
    async def test_system_info(self, http_client):
        """Test system information endpoint."""
        response = await http_client.get("/api/system/info")
        assert response.status_code == 200

        data = response.json()
        assert data["service"] == "ai-orchestrator"
        assert data["version"] == "2.0.0"

        # Check features
        features = data["features"]
        assert features["enhanced_llm"] is True
        assert features["multi_provider"] is True
        assert features["rate_limiting"] is True

        # Check providers configuration
        providers = data["providers"]
        assert "ollama" in providers
        assert "openrouter" in providers
        assert "openai" in providers
        assert "anthropic" in providers

    @pytest.mark.asyncio
    async def test_llm_health_check(self, http_client):
        """Test LLM service health check."""
        response = await http_client.get("/api/llm/health")
        assert response.status_code == 200

        data = response.json()
        assert "status" in data
        assert data["status"] in ["healthy", "degraded", "unhealthy"]
        assert "providers" in data

        # Check that all providers are tested
        providers = data["providers"]
        provider_names = [p["provider"] for p in providers]
        expected_providers = ["ollama", "openrouter", "openai", "anthropic"]

        for expected in expected_providers:
            assert expected in provider_names

        # Each provider should have required fields
        for provider in providers:
            assert "provider" in provider
            assert "available" in provider
            assert "api_key_configured" in provider

    @pytest.mark.asyncio
    async def test_list_providers(self, http_client, auth_headers):
        """Test listing providers endpoint."""
        response = await http_client.get("/api/llm/providers", headers=auth_headers)

        # May fail due to auth - that's expected behavior
        if response.status_code == 401:
            pytest.skip("Authentication required - expected in production")

        assert response.status_code == 200
        data = response.json()
        assert "providers" in data

        providers = data["providers"]
        assert len(providers) >= 1  # At least Ollama should be configured

        for provider in providers:
            required_fields = ["provider", "enabled", "available", "api_key_configured"]
            for field in required_fields:
                assert field in provider

    @pytest.mark.asyncio
    async def test_list_models(self, http_client, auth_headers):
        """Test listing models endpoint."""
        response = await http_client.get("/api/llm/models", headers=auth_headers)

        if response.status_code == 401:
            pytest.skip("Authentication required - expected in production")

        assert response.status_code == 200
        data = response.json()
        assert "models" in data

        models = data["models"]
        if len(models) > 0:  # Only test if models are available
            model = models[0]
            required_fields = ["name", "provider", "status"]
            for field in required_fields:
                assert field in model

    @pytest.mark.asyncio
    async def test_legacy_ollama_status(self, http_client):
        """Test legacy Ollama status endpoint."""
        response = await http_client.get("/api/ollama/status")
        assert response.status_code == 200

        data = response.json()
        assert "connected" in data
        assert "base_url" in data
        assert "message" in data

        # Should include response time if connection is successful
        if data["connected"]:
            assert "response_time_ms" in data

    @pytest.mark.asyncio
    async def test_rate_limiting(self, http_client):
        """Test that rate limiting is working."""
        # Make multiple rapid requests to trigger rate limiting
        responses = []

        # Make 35 requests rapidly (over the 30/minute limit for health endpoint)
        for i in range(35):
            try:
                response = await http_client.get("/health")
                responses.append(response.status_code)
            except Exception as e:
                # Expected if rate limited
                break

        # Should eventually get rate limited (status 429)
        if len(responses) == 35:
            # If all requests succeeded, rate limiting might not be active
            # This is acceptable in development
            assert all(status == 200 for status in responses)
        else:
            # Some requests were rate limited - this is expected
            assert any(status == 429 for status in responses[-5:])

    @pytest.mark.asyncio
    async def test_cors_headers(self, http_client):
        """Test that CORS headers are properly configured."""
        # Make an OPTIONS request to check CORS
        response = await http_client.options("/", headers={"Origin": "http://localhost:3000"})

        # CORS headers should be present
        headers = response.headers
        assert "access-control-allow-origin" in headers or "Access-Control-Allow-Origin" in headers

    @pytest.mark.asyncio
    async def test_error_handling(self, http_client):
        """Test error handling for invalid endpoints."""
        # Test 404 for non-existent endpoint
        response = await http_client.get("/api/nonexistent")
        assert response.status_code == 404

        # Test invalid methods
        response = await http_client.post("/health")
        assert response.status_code == 405  # Method not allowed


class TestLLMGeneration:
    """Tests for LLM text generation functionality."""

    @pytest.fixture
    async def http_client(self):
        """Create HTTP client for testing."""
        async with httpx.AsyncClient(
            base_url=TEST_BASE_URL,
            timeout=60  # Longer timeout for generation
        ) as client:
            yield client

    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer test-token"}

    @pytest.mark.asyncio
    async def test_generate_text_validation(self, http_client, auth_headers):
        """Test text generation with various input validation scenarios."""

        # Test invalid request (missing prompt)
        invalid_request = {
            "model": "test-model",
            "provider": "ollama"
        }

        response = await http_client.post(
            "/api/llm/generate",
            json=invalid_request,
            headers=auth_headers
        )

        if response.status_code == 401:
            pytest.skip("Authentication required")

        assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_generate_text_with_mock_ollama(self, http_client, auth_headers):
        """Test text generation with mocked Ollama response."""

        valid_request = {
            "prompt": "Hello, how are you?",
            "provider": "ollama",
            "model": "llama3.2",
            "temperature": 0.7,
            "max_tokens": 50
        }

        # In a real integration test, this would hit the actual service
        # For unit testing, we'd mock the LLM service
        response = await http_client.post(
            "/api/llm/generate",
            json=valid_request,
            headers=auth_headers
        )

        if response.status_code == 401:
            pytest.skip("Authentication required")
        elif response.status_code == 503:
            pytest.skip("Ollama service not available")
        elif response.status_code == 429:
            pytest.skip("Rate limited")

        # If successful, validate response structure
        if response.status_code == 200:
            data = response.json()
            required_fields = ["content", "model", "provider", "usage"]
            for field in required_fields:
                assert field in data

            assert data["provider"] == "ollama"
            assert data["model"] == "llama3.2"
            assert isinstance(data["usage"], dict)


class TestResourceMonitoring:
    """Tests for resource monitoring and optimization."""

    @pytest.fixture
    async def http_client(self):
        async with httpx.AsyncClient(base_url=TEST_BASE_URL, timeout=30) as client:
            yield client

    @pytest.fixture
    def auth_headers(self):
        return {"Authorization": "Bearer test-token"}

    @pytest.mark.asyncio
    async def test_usage_statistics(self, http_client, auth_headers):
        """Test usage statistics endpoint."""
        response = await http_client.get("/api/llm/statistics", headers=auth_headers)

        if response.status_code == 401:
            pytest.skip("Authentication required")

        assert response.status_code == 200
        data = response.json()

        # Should contain basic statistics structure
        expected_fields = ["providers", "total_requests", "total_errors"]
        for field in expected_fields:
            if field in data:  # Some fields might not be present if no requests made
                assert isinstance(data[field], (int, dict))

    @pytest.mark.asyncio
    async def test_rate_limit_info(self, http_client, auth_headers):
        """Test rate limit information endpoint."""
        response = await http_client.get("/api/llm/rate-limits/ollama", headers=auth_headers)

        if response.status_code == 401:
            pytest.skip("Authentication required")

        assert response.status_code == 200
        data = response.json()

        required_fields = ["provider", "requests_per_minute", "requests_remaining", "reset_time"]
        for field in required_fields:
            assert field in data


if __name__ == "__main__":
    # Run with: python -m pytest tests/test_integration.py -v
    pytest.main([__file__, "-v", "-s"])