# Technical Context: AI Coding Agent

## Technologies Used

### Backend Technologies
- **Python 3.13+**: Primary programming language
- **FastAPI**: High-performance web framework for API development
- **SQLAlchemy 2.0**: Modern ORM for database operations
- **Alembic**: Database migration tool
- **PostgreSQL**: Primary relational database with pgvector extension
- **Redis**: In-memory data structure store for caching and messaging
- **Supabase**: Authentication and authorization services
- **Pydantic**: Data validation and settings management

### AI/ML Technologies
- **Ollama**: Local LLM model serving and management
- **Lang<PERSON>hain**: Framework for developing applications with LLMs
- **LangGraph**: Library for building stateful, multi-actor applications with LLMs
- **OpenAI API**: Cloud-based LLM provider integration
- **Anthropic API**: Cloud-based LLM provider integration
- **OpenRouter**: Aggregated LLM provider access
- **Sentence Transformers**: State-of-the-art sentence and text embeddings
- **Transformers**: Hugging Face library for NLP tasks
- **Torch**: PyTorch for deep learning operations

### Frontend Technologies
- **React**: Component-based JavaScript library for UI development
- **TypeScript**: Typed superset of JavaScript
- **Next.js**: React framework for production applications
- **Tailwind CSS**: Utility-first CSS framework
- **WebSocket**: Real-time communication protocol

### Containerization and DevOps
- **Docker**: Containerization platform
- **Docker Compose**: Multi-container application orchestration
- **Supervisord**: Process control system
- **BuildKit**: Advanced Docker build toolkit
- **GitHub Actions**: CI/CD pipeline automation

### Monitoring and Observability
- **Prometheus**: Systems monitoring and alerting toolkit
- **Grafana**: Analytics and monitoring platform
- **ELK Stack**: Elasticsearch, Logstash, Kibana for log management
- **SlowAPI**: Rate limiting for FastAPI applications

### Testing and Quality Assurance
- **Pytest**: Testing framework for Python
- **Pytest-cov**: Code coverage plugin
- **Pytest-asyncio**: Async support for pytest
- **HTTPX**: HTTP client for testing
- **Pytest-mock**: Mocking library for testing

## Development Setup

### Environment Requirements
- **Docker Desktop**: For container orchestration
- **Python 3.13+**: For local development
- **Node.js 18+**: For frontend development
- **Git**: Version control system
- **VS Code**: Recommended IDE with extensions

### Development Workflow
1. **Container-Based Development**: All services run in Docker containers
2. **Hot Reload**: Development mode with live code reloading
3. **Code-Server**: Browser-based VS Code development environment
4. **Multi-Environment**: Development, testing, and production configurations

### Configuration Management
- **Environment Variables**: `.env` files for configuration
- **Docker Compose Profiles**: Different environment configurations
- **Secrets Management**: Secure handling of API keys and credentials
- **Configuration Validation**: Runtime validation of settings

## Technical Constraints

### Performance Constraints
- **Response Time**: API endpoints should respond within 500ms
- **Memory Usage**: Containers should stay within allocated limits
- **Startup Time**: Services should start within 30 seconds
- **LLM Response Time**: Model responses should be optimized

### Security Constraints
- **Authentication**: JWT-based authentication required for all APIs
- **Authorization**: Role-based access control (RBAC)
- **Data Encryption**: Sensitive data encryption at rest and in transit
- **Input Validation**: Strict input sanitization and validation
- **Rate Limiting**: API rate limiting to prevent abuse

### Scalability Constraints
- **Horizontal Scaling**: Services designed for container replication
- **Database Connections**: Connection pooling for efficient database access
- **Caching Strategy**: Redis caching for frequently accessed data
- **Resource Limits**: CPU and memory limits per container

## Dependencies

### Core Dependencies
- **FastAPI Ecosystem**: uvicorn, pydantic, starlette
- **Database Drivers**: asyncpg, psycopg2-binary
- **Redis Client**: redis-py with hiredis
- **HTTP Libraries**: requests, aiohttp
- **Authentication**: python-jose, passlib, PyJWT
- **AI Libraries**: langchain, openai, transformers
- **Vector Storage**: pgvector, numpy, scikit-learn

### Development Dependencies
- **Debugging**: debugpy, watchdog
- **Testing**: pytest, httpx, pytest-mock
- **Code Quality**: black, flake8, mypy
- **Documentation**: sphinx, mkdocs

## Official Documentation References

### Core Implementation References
- **FastAPI Implementation**: `docs/api/FASTAPI_REFERENCE_GUIDE.md`
- **Database Integration**: `docs/database/SUPABASE_INTEGRATION_GUIDE.md`
- **Redis Integration**: `docs/database/REDIS_INTEGRATION_GUIDE.md`
- **LLM Integration**: `docs/LLM_IMPLEMENTATION_SUMMARY.md`
- **Agent Architecture**: `docs/architecture/SEQUENTIAL_AGENT_ARCHITECTURE_GUIDE.md`
- **Validation Framework**: `VALIDATION_FRAMEWORK_IMPLEMENTATION.md`

### Technology-Specific Documentation
- **Docker Optimization**: `docs/docker/DOCKER_OPTIMIZATION_GUIDE.md`
- **Docker Security**: `docs/security/DOCKER_SECURITY_GUIDE.md`
- **Network Security**: `docs/security/network-security.md`
- **Supabase Integration**: `docs/database/SUPABASE_INTEGRATION_GUIDE.md`
- **Redis Integration**: `docs/database/REDIS_INTEGRATION_GUIDE.md`
- **Code-Server Setup**: `docs/code-server/CODE_SERVER_SETUP_GUIDE.md`
- **VS Code Extensions**: `docs/extensions/VSCODE_EXTENSION_DEVELOPMENT_GUIDE.md`
- **WebSocket Communication**: `docs/realtime/WEBSOCKET_COMMUNICATION_GUIDE.md`
- **OpenRouter Integration**: `docs/llm/OPENROUTER_INTEGRATION_GUIDE.md`
- **CI/CD Pipeline**: `docs/cicd/CICD_PIPELINE_IMPLEMENTATION_GUIDE.md`
- **Monitoring & Observability**: `docs/monitoring/MONITORING_OBSERVABILITY_STACK_GUIDE.md`

## Tool Usage Patterns

### Development Tools
- **Docker Compose**: For local development and testing
- **Pre-commit Hooks**: Automated code quality checks
- **Linting**: flake8, black, mypy for code consistency
- **Testing**: pytest for unit and integration tests
- **Debugging**: debugpy for remote debugging in containers

### Deployment Tools
- **Docker Build**: Multi-stage builds for optimization
- **Docker Compose**: Production deployment orchestration
- **GitHub Actions**: Automated CI/CD pipelines
- **Security Scanning**: Trivy for container vulnerability scanning
- **Monitoring**: Prometheus and Grafana for observability

### Monitoring and Logging
- **Log Aggregation**: ELK stack for centralized logging
- **Metrics Collection**: Prometheus for system metrics
- **Visualization**: Grafana for dashboard creation
- **Alerting**: Prometheus alert manager for notifications
- **Health Checks**: Built-in health check endpoints

## Integration Points

### External Services
- **LLM Providers**: Ollama, OpenRouter, OpenAI, Anthropic
- **Authentication**: Supabase Auth service
- **Database**: PostgreSQL with vector extensions
- **Caching**: Redis for session and cache management
- **Monitoring**: Prometheus, Grafana, ELK stack

### Internal Services
- **API Gateway**: FastAPI routing and middleware
- **WebSocket Server**: Real-time communication
- **Task Queue**: Redis-based background processing
- **Validation Engine**: Multi-layered validation system
- **Error Recovery**: Automated error handling and fixing
