# Redis Integration Guide for AI Coding Agent

## Overview

Redis serves as the caching layer, session store, and real-time messaging backbone for the AI Coding Agent's **ai-orchestrator** service. This guide covers essential integration patterns for high-performance operations.

## Core Architecture

```
AI Orchestrator (FastAPI) ←→ Redis ←→ PostgreSQL
                          ↓
                   WebSocket Clients
```

## Connection Management

### Async Redis Setup

```python
import redis.asyncio as redis
from redis.asyncio import ConnectionPool
from functools import lru_cache

class RedisManager:
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self._pool = None
        self._client = None

    async def initialize(self):
        self._pool = ConnectionPool.from_url(
            self.redis_url,
            max_connections=20,
            decode_responses=True,
            health_check_interval=30
        )
        self._client = redis.Redis(connection_pool=self._pool)
        await self._client.ping()

    async def close(self):
        if self._client:
            await self._client.close()
        if self._pool:
            await self._pool.disconnect()

    @property
    def client(self):
        return self._client

redis_manager = RedisManager("redis://redis:6379/0")

async def get_redis_client():
    if not redis_manager._client:
        await redis_manager.initialize()
    return redis_manager.client
```

## Caching Strategies

### LLM Response Caching

```python
import hashlib
import json
from datetime import timedelta

class LLMCache:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.prefix = "llm:cache"
        self.ttl = 3600  # 1 hour

    def _key(self, prompt: str, model: str, params: dict) -> str:
        content = json.dumps({"prompt": prompt, "model": model, "params": params}, sort_keys=True)
        return f"{self.prefix}:{hashlib.sha256(content.encode()).hexdigest()}"

    async def get(self, prompt: str, model: str, params: dict) -> str:
        return await self.redis.get(self._key(prompt, model, params))

    async def set(self, prompt: str, model: str, params: dict, response: str):
        await self.redis.setex(self._key(prompt, model, params), self.ttl, response)

# Usage
@app.post("/api/v1/llm/generate")
async def generate_code(request: CodeRequest, redis_client = Depends(get_redis_client)):
    cache = LLMCache(redis_client)

    # Try cache first
    cached = await cache.get(request.prompt, request.model, request.params)
    if cached:
        return {"code": cached, "cached": True}

    # Generate and cache
    code = await generate_with_llm(request)
    await cache.set(request.prompt, request.model, request.params, code)
    return {"code": code, "cached": False}
```

### Validation Result Caching

```python
class ValidationCache:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.prefix = "validation"
        self.ttl = 1800  # 30 minutes

    def _code_hash(self, code: str, language: str) -> str:
        return hashlib.sha256(f"{language}:{code}".encode()).hexdigest()

    async def get_validation(self, code: str, language: str) -> dict:
        key = f"{self.prefix}:{self._code_hash(code, language)}"
        result = await self.redis.get(key)
        return json.loads(result) if result else None

    async def cache_validation(self, code: str, language: str, result: dict):
        key = f"{self.prefix}:{self._code_hash(code, language)}"
        await self.redis.setex(key, self.ttl, json.dumps(result))
```

## Session Management

### User Sessions & Approval Workflows

```python
from uuid import uuid4
from datetime import datetime, timedelta

class SessionManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.session_ttl = 86400  # 24 hours
        self.approval_ttl = 1800  # 30 minutes

    async def create_session(self, user_id: str, data: dict) -> str:
        session_id = str(uuid4())
        session_data = {
            "user_id": user_id,
            "created_at": datetime.utcnow().isoformat(),
            **data
        }

        await self.redis.setex(
            f"session:{session_id}",
            self.session_ttl,
            json.dumps(session_data)
        )
        return session_id

    async def get_session(self, session_id: str) -> dict:
        data = await self.redis.get(f"session:{session_id}")
        return json.loads(data) if data else None

    async def create_approval(self, operation: str, context: dict, user_id: str) -> str:
        approval_id = str(uuid4())
        approval_data = {
            "approval_id": approval_id,
            "operation": operation,
            "context": context,
            "user_id": user_id,
            "status": "pending",
            "created_at": datetime.utcnow().isoformat()
        }

        await self.redis.setex(
            f"approval:{approval_id}",
            self.approval_ttl,
            json.dumps(approval_data)
        )

        # Add to pending list
        await self.redis.lpush(f"pending_approvals:{user_id}", approval_id)
        return approval_id

    async def process_approval(self, approval_id: str, approved: bool, admin_id: str) -> bool:
        key = f"approval:{approval_id}"
        data = await self.redis.get(key)
        if not data:
            return False

        approval = json.loads(data)
        approval.update({
            "status": "approved" if approved else "denied",
            "processed_at": datetime.utcnow().isoformat(),
            "processed_by": admin_id
        })

        await self.redis.setex(key, 86400, json.dumps(approval))  # Audit trail
        return True
```

## Task Queue with Redis Streams

### Background Task Processing

```python
class TaskQueue:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.stream = "ai_tasks"
        self.group = "ai_workers"
        self.consumer = f"worker_{uuid4().hex[:8]}"

    async def initialize(self):
        try:
            await self.redis.xgroup_create(self.stream, self.group, id="0", mkstream=True)
        except redis.ResponseError as e:
            if "BUSYGROUP" not in str(e):
                raise

    async def add_task(self, task_type: str, data: dict, priority: int = 0) -> str:
        task_id = str(uuid4())
        payload = {
            "task_id": task_id,
            "task_type": task_type,
            "data": json.dumps(data),
            "priority": str(priority),
            "created_at": datetime.utcnow().isoformat()
        }

        await self.redis.xadd(self.stream, payload)
        return task_id

    async def process_tasks(self, handlers: dict):
        await self.initialize()

        while True:
            try:
                messages = await self.redis.xreadgroup(
                    streams={self.stream: ">"},
                    groupname=self.group,
                    consumername=self.consumer,
                    count=1,
                    block=1000
                )

                for stream, msgs in messages:
                    for msg_id, fields in msgs:
                        await self._process_message(msg_id, fields, handlers)
            except Exception as e:
                logger.error(f"Task processing error: {e}")
                await asyncio.sleep(5)

    async def _process_message(self, msg_id: str, fields: dict, handlers: dict):
        task_type = fields.get("task_type")
        task_id = fields.get("task_id")

        try:
            handler = handlers.get(task_type)
            if handler:
                data = json.loads(fields.get("data", "{}"))
                await handler(task_id, data)

            await self.redis.xack(self.group, self.stream, msg_id)
        except Exception as e:
            logger.error(f"Failed to process task {task_id}: {e}")

# Task handlers
async def validate_code_handler(task_id: str, data: dict):
    # Perform validation
    result = await validate_code(data["code"], data["language"])
    await update_task_status(task_id, "completed", result)

async def generate_code_handler(task_id: str, data: dict):
    # Generate code
    code = await generate_code(data["prompt"], data["model"])
    await update_task_status(task_id, "completed", {"code": code})
```

## Real-time Messaging

### Pub/Sub for WebSocket Notifications

```python
class RealTimeMessaging:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.channels = {
            "validation": "ai:validation:progress",
            "tasks": "ai:tasks:updates",
            "approvals": "ai:approvals:requests"
        }

    async def publish_validation_progress(self, task_id: str, progress: int, message: str):
        event = {
            "type": "validation_progress",
            "task_id": task_id,
            "progress": progress,
            "message": message,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.redis.publish(self.channels["validation"], json.dumps(event))

    async def publish_task_update(self, task_id: str, status: str, details: dict):
        event = {
            "type": "task_update",
            "task_id": task_id,
            "status": status,
            "details": details,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.redis.publish(self.channels["tasks"], json.dumps(event))

    async def subscribe_to_events(self, callback):
        pubsub = self.redis.pubsub()
        for channel in self.channels.values():
            await pubsub.subscribe(channel)

        while True:
            message = await pubsub.get_message()
            if message and message["type"] == "message":
                event_data = json.loads(message["data"])
                await callback(event_data)
```

## Rate Limiting

### API Rate Limiting

```python
class RateLimiter:
    def __init__(self, redis_client):
        self.redis = redis_client

    async def check_rate_limit(self, key: str, limit: int, window: int) -> bool:
        """Check if request is within rate limit."""
        current = await self.redis.incr(key)
        if current == 1:
            await self.redis.expire(key, window)
        return current <= limit

# Usage in FastAPI
@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    redis_client = await get_redis_client()
    limiter = RateLimiter(redis_client)

    # Rate limit by IP
    client_ip = request.client.host
    key = f"rate_limit:{client_ip}"

    if not await limiter.check_rate_limit(key, limit=100, window=60):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")

    return await call_next(request)
```

## Data Structures Usage

### Key Data Types for AI Coding Agent

```python
# Strings - Simple key-value caching
await redis.set("config:llm_model", "llama3.1")
await redis.setex("temp:upload_id", 300, file_data)

# Hashes - User profiles, session data
await redis.hset("user:123", mapping={"name": "John", "role": "admin"})
await redis.hgetall("user:123")

# Lists - Task queues, recent activities
await redis.lpush("recent_validations", validation_id)
await redis.ltrim("recent_validations", 0, 99)  # Keep last 100

# Sets - Active sessions, permissions
await redis.sadd("active_sessions", session_id)
await redis.sismember("admin_users", user_id)

# Sorted Sets - Leaderboards, priorities
await redis.zadd("task_priorities", {task_id: priority})
await redis.zrange("task_priorities", 0, 9, desc=True)  # Top 10

# Streams - Event sourcing, audit logs
await redis.xadd("audit_log", {
    "user_id": user_id,
    "action": "code_generated",
    "timestamp": datetime.utcnow().isoformat()
})
```

## Performance Optimization

### Connection Pooling & Pipeline

```python
# Use pipelines for bulk operations
async def batch_cache_validations(validations: list):
    pipe = redis_client.pipeline()
    for validation in validations:
        key = f"validation:{validation['id']}"
        pipe.setex(key, 1800, json.dumps(validation))
    await pipe.execute()

# Memory optimization
await redis.config_set("maxmemory-policy", "allkeys-lru")
await redis.config_set("maxmemory", "1gb")
```

## Monitoring & Health Checks

### Redis Health Monitoring

```python
@app.get("/api/v1/redis/health")
async def redis_health(redis_client = Depends(get_redis_client)):
    try:
        await redis_client.ping()
        info = await redis_client.info("server")
        return {
            "status": "healthy",
            "redis_version": info.get("redis_version"),
            "connected_clients": info.get("connected_clients"),
            "used_memory": info.get("used_memory_human")
        }
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}
```

## Production Configuration

### Docker Compose Integration

```yaml
services:
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - ai-coding-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:

networks:
  ai-coding-network:
    driver: bridge
```

## Security Best Practices

1. **Use Redis AUTH**: Set password in production
2. **Network Isolation**: Run on private Docker network
3. **Key Expiration**: Set TTL for all keys
4. **Data Encryption**: Use TLS for Redis connections
5. **Access Control**: Limit Redis commands if possible

## Key Takeaways

1. **Connection Management**: Use async Redis with connection pooling
2. **Caching Strategy**: Cache LLM responses and validation results
3. **Session Management**: Store user sessions and approval workflows
4. **Task Queues**: Use Redis Streams for background processing
5. **Real-time Events**: Pub/Sub for WebSocket notifications
6. **Rate Limiting**: Protect APIs with Redis-based rate limiting
7. **Performance**: Use pipelines and appropriate data structures
8. **Monitoring**: Implement health checks and metrics
9. **Security**: Network isolation and authentication
10. **Scaling**: Configure memory policies and connection limits

This guide provides the foundation for integrating Redis effectively into your AI Coding Agent's architecture.