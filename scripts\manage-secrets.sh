#!/bin/bash
# Docker Secrets Management Script for AI Coding Agent
# Manages creation and rotation of Docker secrets

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="ai_coding_agent"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ ${1}${NC}"; }
log_success() { echo -e "${GREEN}✅ ${1}${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️ ${1}${NC}"; }
log_error() { echo -e "${RED}❌ ${1}${NC}"; }

# Check if Docker Swarm is initialized
check_swarm() {
    if ! docker info | grep -q "Swarm: active"; then
        log_warning "Docker Swarm is not initialized. Initializing now..."
        docker swarm init --advertise-addr 127.0.0.1 || {
            log_error "Failed to initialize Docker Swarm"
            exit 1
        }
        log_success "Docker Swarm initialized"
    else
        log_info "Docker Swarm is already active"
    fi
}

# Generate a secure random string
generate_secret() {
    openssl rand -base64 32 | tr -d "\n"
}

# Create a Docker secret
create_secret() {
    local secret_name="$1"
    local secret_value="$2"

    if docker secret inspect "${PROJECT_NAME}_${secret_name}" &>/dev/null; then
        log_warning "Secret ${secret_name} already exists. Skipping..."
        return 0
    fi

    echo -n "$secret_value" | docker secret create "${PROJECT_NAME}_${secret_name}" - || {
        log_error "Failed to create secret: ${secret_name}"
        return 1
    }

    log_success "Created secret: ${secret_name}"
}

# Create all required secrets
create_secrets() {
    log_info "Creating Docker secrets..."

    # Generate secrets if not provided via environment
    POSTGRES_PASSWORD="${POSTGRES_PASSWORD:-$(generate_secret)}"
    JWT_SECRET="${JWT_SECRET:-$(generate_secret)}"
    GRAFANA_ADMIN_PASSWORD="${GRAFANA_ADMIN_PASSWORD:-$(generate_secret)}"

    # Create secrets
    create_secret "postgres_password" "$POSTGRES_PASSWORD"
    create_secret "jwt_secret" "$JWT_SECRET"
    create_secret "grafana_password" "$GRAFANA_ADMIN_PASSWORD"

    # API Keys (must be provided)
    if [ -n "$OPENROUTER_API_KEY" ]; then
        create_secret "openrouter_key" "$OPENROUTER_API_KEY"
    else
        log_warning "OPENROUTER_API_KEY not provided. Skipping..."
    fi

    if [ -n "$OPENAI_API_KEY" ]; then
        create_secret "openai_key" "$OPENAI_API_KEY"
    else
        log_warning "OPENAI_API_KEY not provided. Skipping..."
    fi

    if [ -n "$ANTHROPIC_API_KEY" ]; then
        create_secret "anthropic_key" "$ANTHROPIC_API_KEY"
    else
        log_warning "ANTHROPIC_API_KEY not provided. Skipping..."
    fi

    if [ -n "$SUPABASE_SERVICE_KEY" ]; then
        create_secret "supabase_service_key" "$SUPABASE_SERVICE_KEY"
    else
        log_warning "SUPABASE_SERVICE_KEY not provided. Skipping..."
    fi
}

# List all secrets
list_secrets() {
    log_info "Docker secrets for ${PROJECT_NAME}:"
    docker secret ls --filter "name=${PROJECT_NAME}_" --format "table {{.Name}}\t{{.CreatedAt}}\t{{.UpdatedAt}}"
}

# Rotate a secret
rotate_secret() {
    local secret_name="$1"
    local new_value="$2"

    if [ -z "$new_value" ]; then
        new_value=$(generate_secret)
    fi

    local full_secret_name="${PROJECT_NAME}_${secret_name}"

    # Remove old secret
    if docker secret inspect "$full_secret_name" &>/dev/null; then
        docker secret rm "$full_secret_name" || {
            log_error "Failed to remove old secret: ${secret_name}"
            return 1
        }
        log_info "Removed old secret: ${secret_name}"
    fi

    # Create new secret
    create_secret "$secret_name" "$new_value"
    log_success "Rotated secret: ${secret_name}"
}

# Remove all secrets
remove_secrets() {
    log_warning "Removing all Docker secrets for ${PROJECT_NAME}..."

    docker secret ls --filter "name=${PROJECT_NAME}_" --format "{{.Name}}" | while read -r secret; do
        if [ -n "$secret" ]; then
            docker secret rm "$secret" && log_success "Removed: $secret" || log_error "Failed to remove: $secret"
        fi
    done
}

# Save secrets to encrypted file (backup)
backup_secrets() {
    local backup_file="secrets-backup-$(date +%Y%m%d-%H%M%S).enc"

    log_info "Creating encrypted backup of secrets..."

    cat > "/tmp/secrets.json" << EOF
{
  "postgres_password": "$POSTGRES_PASSWORD",
  "jwt_secret": "$JWT_SECRET",
  "grafana_admin_password": "$GRAFANA_ADMIN_PASSWORD",
  "openrouter_api_key": "$OPENROUTER_API_KEY",
  "openai_api_key": "$OPENAI_API_KEY",
  "anthropic_api_key": "$ANTHROPIC_API_KEY",
  "supabase_service_key": "$SUPABASE_SERVICE_KEY",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

    # Encrypt with GPG (requires GPG key setup)
    if command -v gpg &> /dev/null; then
        gpg --symmetric --cipher-algo AES256 --output "$backup_file" "/tmp/secrets.json"
        rm "/tmp/secrets.json"
        log_success "Encrypted backup created: $backup_file"
    else
        log_warning "GPG not available. Creating unencrypted backup: secrets-backup.json"
        mv "/tmp/secrets.json" "secrets-backup.json"
    fi
}

# Help function
show_help() {
    cat << EOF
Docker Secrets Management for AI Coding Agent

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    create      Create all required secrets
    list        List existing secrets
    rotate      Rotate a specific secret
    remove      Remove all secrets
    backup      Create encrypted backup of secrets
    help        Show this help message

Examples:
    $0 create                           # Create all secrets
    $0 rotate postgres_password         # Rotate PostgreSQL password
    $0 rotate jwt_secret "new_value"    # Rotate with specific value
    $0 list                             # List all secrets
    $0 backup                           # Create encrypted backup

Environment Variables:
    POSTGRES_PASSWORD       PostgreSQL password (auto-generated if not set)
    JWT_SECRET             JWT secret key (auto-generated if not set)
    GRAFANA_ADMIN_PASSWORD  Grafana admin password (auto-generated if not set)
    OPENROUTER_API_KEY     OpenRouter API key (required)
    OPENAI_API_KEY         OpenAI API key (optional)
    ANTHROPIC_API_KEY      Anthropic API key (optional)
    SUPABASE_SERVICE_KEY   Supabase service key (optional)

EOF
}

# Main execution
main() {
    case "${1:-help}" in
        "create")
            check_swarm
            create_secrets
            ;;
        "list")
            list_secrets
            ;;
        "rotate")
            if [ -z "$2" ]; then
                log_error "Please specify secret name to rotate"
                exit 1
            fi
            check_swarm
            rotate_secret "$2" "$3"
            ;;
        "remove")
            remove_secrets
            ;;
        "backup")
            backup_secrets
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

main "$@"