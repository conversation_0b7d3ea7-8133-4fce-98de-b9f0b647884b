# PowerShell script to reset admin-dashboard development environment
# Run this from the project root directory

Write-Host "=== Phase 2: Resetting Development Environment State ===" -ForegroundColor Green

# Step 1: Stop and remove all services
Write-Host "Step 1: Stopping and removing all services..." -ForegroundColor Yellow
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down

# Step 2: Remove specific named volumes to ensure clean slate
Write-Host "Step 2: Removing corrupted named volumes..." -ForegroundColor Yellow
docker volume rm ai-coding-agent-dev_admin_dashboard_node_modules -f 2>$null
docker volume rm ai-coding-agent-dev_admin_dashboard_next -f 2>$null

# Step 3: Remove any orphaned containers
Write-Host "Step 3: Removing orphaned containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down --remove-orphans

# Step 4: Prune any dangling images (optional but recommended)
Write-Host "Step 4: Cleaning up dangling images..." -ForegroundColor Yellow
docker image prune -f

# Step 5: Rebuild admin-dashboard service with hardened Dockerfile
Write-Host "Step 5: Rebuilding admin-dashboard service..." -ForegroundColor Yellow
docker-compose -f docker-compose.yml -f docker-compose.dev.yml build --no-cache admin-dashboard

# Step 6: Start the admin-dashboard service
Write-Host "Step 6: Starting admin-dashboard service..." -ForegroundColor Yellow
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d admin-dashboard

# Step 7: Show logs to verify successful startup
Write-Host "Step 7: Showing admin-dashboard logs..." -ForegroundColor Yellow
Start-Sleep -Seconds 5
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs admin-dashboard

Write-Host "=== Reset Complete! ===" -ForegroundColor Green
Write-Host "Admin Dashboard should now be accessible at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "To continue monitoring logs: docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f admin-dashboard" -ForegroundColor Cyan