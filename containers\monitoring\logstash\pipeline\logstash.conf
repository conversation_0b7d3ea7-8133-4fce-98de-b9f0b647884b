# Logstash pipeline configuration for AI Coding Agent
input {
  # Docker logs input
  file {
    path => "/var/lib/docker/containers/*/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => "json"
  }

  # Syslog input (if needed)
  # syslog {
  #   port => 514
  # }

  # Beats input (if needed)
  # beats {
  #   port => 5044
  # }
}

filter {
  # Parse Docker logs
  if [log] {
    json {
      source => "log"
      target => "docker_log"
    }
  }

  # Add container metadata
  if [docker][container][name] {
    mutate {
      add_field => {
        "container_name" => "%{[docker][container][name]}"
        "container_id" => "%{[docker][container][id]}"
      }
    }
  }

  # Parse timestamp
  date {
    match => [ "time", "ISO8601" ]
    target => "@timestamp"
  }

  # Add log level parsing
  if [message] =~ /\[(DEBUG|INFO|WARNING|ERROR|CRITICAL)\]/ {
    grok {
      match => { "message" => "\[(?<log_level>(DEBUG|INFO|WARNING|ERROR|CRITICAL))\] %{GREEDYDATA:message}" }
    }
  }

  # Remove unnecessary fields
  mutate {
    remove_field => [ "host", "path", "tags", "log", "stream", "time" ]
  }
}

output {
  # Send to Elasticsearch
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "logs-%{+YYYY.MM.dd}"
  }

  # Also output to stdout for debugging
  stdout {
    codec => rubydebug
  }
}
