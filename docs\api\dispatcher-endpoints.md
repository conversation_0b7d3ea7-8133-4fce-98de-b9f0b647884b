# Dispatcher and Task Management API

## Overview

Provides endpoints to create tasks and trigger the Di<PERSON>atcher to process queued work using the new Sequential Agent Framework (architect, backend, frontend, shell).

## Base URL

```
http://localhost:8000
```

## Authentication

- If your deployment requires auth globally, include the appropriate Authorization header.
- These endpoints do not enforce auth within the router by default; integrate with your auth layer as needed.

## Endpoints

### 1) Create Task

- **Endpoint**: `POST /tasks/`
- **Description**: Create a new task in "pending" status for the specified project and agent role.
- **Request Body**:
```json
{
  "project_id": 1,
  "agent_role": "architect",
  "input_data": {"goal": "Plan service structure"}
}
```
- **Request Schema**:
  - **project_id**: integer, required, >= 1
  - **agent_role**: string, required (e.g., "architect", "backend", "frontend", "shell")
  - **input_data**: object, optional, defaults to {}

- **Response 201**:
```json
{
  "id": 12,
  "project_id": 1,
  "status": "pending",
  "agent_role": "architect",
  "input_data": {"goal": "Plan service structure"},
  "output_data": null,
  "created_at": "2025-08-26T12:34:56.000000",
  "updated_at": "2025-08-26T12:34:56.000000"
}
```

- **Error Responses**:
  - 400/422: Validation errors (e.g., invalid body)
  - 500: Failed to create task

### 2) Trigger Dispatcher (Single Cycle)

- **Endpoint**: `POST /dispatch/run-once`
- **Description**: Runs one dispatcher cycle:
  - Finds projects with pending tasks
  - Acquires a per-project Redis lock
  - Selects next pending task
  - Marks it as running in AgentState
  - Executes the mapped agent (architect/backend/frontend/shell)
  - Stores output and marks task completed
  - Releases the lock

- **Response 200**:
```json
{"status": "dispatcher cycle complete"}
```

- **Error Responses**:
  - 500: Dispatcher cycle failed

## Agent Roles

- **architect**: Uses LLM to produce structured plans
- **backend**: Placeholder; returns a success payload
- **frontend**: Placeholder; returns a success payload
- **shell**: Placeholder; returns a success payload

Extend roles by adding new agents under `src/sequential_agents/` and registering them in `src/services/dispatcher.py`.

## Usage Examples

### Create an Architect Task (cURL)

```bash
curl -X POST http://localhost:8000/tasks/ \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": 1,
    "agent_role": "architect",
    "input_data": {"goal": "Design a FastAPI module layout"}
  }'
```

### Run Dispatcher Once (cURL)

```bash
curl -X POST http://localhost:8000/dispatch/run-once
```

## Implementation Notes

- Router file: `src/routers/dispatcher_router.py`
- DI factory: `src/services/__init__.py#get_dispatcher`
- Dispatcher service: `src/services/dispatcher.py`
- Task repository: `src/repository/task_repository.py`
- Agents:
  - Base: `src/sequential_agents/base.py`
  - Architect: `src/sequential_agents/architect.py`
  - Backend: `src/sequential_agents/backend.py`
  - Frontend: `src/sequential_agents/frontend.py`
  - Shell: `src/sequential_agents/shell.py`

## Integration

- The router is included in `src/main.py` and exposes endpoints without an additional prefix.
- If you want to namespace these endpoints (e.g., `/api/v1`), add a prefix when including the router:

```python
from .routers.dispatcher_router import router as dispatcher_router
app.include_router(dispatcher_router, prefix="/api/v1")
```

## Future Enhancements

- Add authentication/authorization requirements per endpoint
- Add GET endpoints to list tasks and their statuses
- Add webhook or background scheduler to run dispatcher periodically
- Add rich result schemas per agent with standardized output fields