# BuildKit Configuration for AI Coding Agent
# Place this file at ~/.docker/buildx/default/buildkitd.toml

[worker.oci]
enabled = true
# snapshotter = "native"
gc = true
gckeepstorage = 20000 # 20GB

[worker.containerd]
enabled = true
platforms = ["linux/amd64", "linux/arm64"]
namespace = "buildkit"

# Registry configuration
[registry."docker.io"]
mirrors = ["mirror.gcr.io"]

[registry."gcr.io"]
http = false
insecure = false

# Cache configuration for faster builds

[[worker.oci.gcpolicy]]
keepBytes = 2000000000 # 2GB
keepDuration = 604800 # 7 days
filters = [
  "type==source.local",
  "type==exec.cachemount",
  "type==source.git.checkout",
]

[[worker.oci.gcpolicy]]
all = true
keepBytes = 4000000000 # 4GB

# Network configuration
[dns]
nameservers = ["*******", "*******"]
searchDomains = ["ai-coding-agent.local"]

# Security settings
[grpc]
address = ["unix:///run/buildkit/buildkitd.sock"]

[grpc.tls]
cert = ""
key = ""
ca = ""

# Frontend configuration
[frontend."dockerfile.v0"]
enabled = true

[frontend."gateway.v0"]
enabled = true
