/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['example.com'],
  },
  // Handle ESLint configuration issues during Docker builds
  eslint: {
    // Only run ESLint on specific directories during production builds
    dirs: ['src'],
    // Continue build even if ESLint errors occur (for containerized deployments)
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },
  // TypeScript configuration
  typescript: {
    // Continue build even if TypeScript errors occur (handle via separate lint step)
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },
  // Output configuration for better containerization
  output: 'standalone',
};

module.exports = nextConfig;
