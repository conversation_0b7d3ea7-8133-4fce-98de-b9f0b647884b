#!/bin/bash

# JWT and Supabase Key Generation Script
# Generates secure JWT secrets and Supabase API keys for production deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Output file
ENV_KEYS_FILE="$PROJECT_ROOT/.env.supabase.keys"

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

generate_jwt_secret() {
    print_info "Generating JWT secret..."
    openssl rand -base64 32
}

generate_supabase_keys() {
    local jwt_secret="$1"

    print_info "Generating Supabase API keys..."

    # Anon key (for public access)
    local anon_payload='{"iss":"supabase","ref":"your-project-ref","role":"anon","iat":1641774000,"exp":2147483647}'
    local anon_key=$(echo -n "$anon_payload" | \
        python3 -c "
import sys
import json
import jwt

payload = json.loads(sys.stdin.read())
secret = '$jwt_secret'
token = jwt.encode(payload, secret, algorithm='HS256')
print(token)
")

    # Service role key (for admin/backend operations)
    local service_payload='{"iss":"supabase","ref":"your-project-ref","role":"service_role","iat":1641774000,"exp":2147483647}'
    local service_key=$(echo -n "$service_payload" | \
        python3 -c "
import sys
import json
import jwt

payload = json.loads(sys.stdin.read())
secret = '$jwt_secret'
token = jwt.encode(payload, secret, algorithm='HS256')
print(token)
")

    echo "$anon_key|$service_key"
}

check_dependencies() {
    print_info "Checking dependencies..."

    # Check for required tools
    local missing_deps=()

    if ! command -v openssl &> /dev/null; then
        missing_deps+=("openssl")
    fi

    if ! command -v python3 &> /dev/null; then
        missing_deps+=("python3")
    fi

    # Check for PyJWT
    if ! python3 -c "import jwt" &> /dev/null; then
        print_warning "PyJWT not found, installing..."
        pip3 install PyJWT
    fi

    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        print_info "Please install the missing dependencies and run this script again."
        exit 1
    fi

    print_success "All dependencies are available"
}

generate_production_secrets() {
    print_info "Generating production secrets..."

    # Generate main JWT secret
    local jwt_secret=$(generate_jwt_secret)
    print_success "JWT secret generated"

    # Generate Supabase API keys
    local keys_result=$(generate_supabase_keys "$jwt_secret")
    local anon_key=$(echo "$keys_result" | cut -d'|' -f1)
    local service_key=$(echo "$keys_result" | cut -d'|' -f2)
    print_success "Supabase API keys generated"

    # Generate other secrets
    local postgres_password=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    local grafana_secret=$(openssl rand -base64 32)
    local grafana_password=$(openssl rand -base64 16)

    print_success "Additional secrets generated"

    # Create output file
    cat > "$ENV_KEYS_FILE" << EOF
# Generated Supabase Keys - $(date)
# IMPORTANT: Keep these keys secure and never commit to version control!

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=${jwt_secret}

# =============================================================================
# SUPABASE API KEYS
# =============================================================================
ANON_KEY=${anon_key}
SERVICE_ROLE_KEY=${service_key}

# =============================================================================
# DATABASE PASSWORDS
# =============================================================================
POSTGRES_PASSWORD=${postgres_password}

# =============================================================================
# MONITORING SECRETS
# =============================================================================
GRAFANA_SECRET_KEY=${grafana_secret}
GRAFANA_ADMIN_PASSWORD=${grafana_password}

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================
# 1. Copy these values to your .env file
# 2. For production, use Docker secrets or environment injection
# 3. Never commit these keys to version control
# 4. Rotate keys regularly for security

# =============================================================================
# DOCKER SECRETS COMMANDS (for production)
# =============================================================================
# Create Docker secrets with these commands:
# echo "${jwt_secret}" | docker secret create ai_coding_agent_jwt_secret -
# echo "${anon_key}" | docker secret create ai_coding_agent_anon_key -
# echo "${service_key}" | docker secret create ai_coding_agent_service_key -
# echo "${postgres_password}" | docker secret create ai_coding_agent_postgres_password -
# echo "${grafana_secret}" | docker secret create ai_coding_agent_grafana_secret -
# echo "${grafana_password}" | docker secret create ai_coding_agent_grafana_password -

EOF

    print_success "Keys saved to: $ENV_KEYS_FILE"
}

create_docker_secrets() {
    print_info "Creating Docker secrets for production..."

    if [ ! -f "$ENV_KEYS_FILE" ]; then
        print_error "Keys file not found. Please run the script to generate keys first."
        exit 1
    fi

    # Source the keys file
    source "$ENV_KEYS_FILE"

    # Create Docker secrets
    echo "$JWT_SECRET" | docker secret create ai_coding_agent_jwt_secret - 2>/dev/null || print_warning "JWT secret already exists"
    echo "$ANON_KEY" | docker secret create ai_coding_agent_anon_key - 2>/dev/null || print_warning "Anon key already exists"
    echo "$SERVICE_ROLE_KEY" | docker secret create ai_coding_agent_service_key - 2>/dev/null || print_warning "Service key already exists"
    echo "$POSTGRES_PASSWORD" | docker secret create ai_coding_agent_postgres_password - 2>/dev/null || print_warning "Postgres password already exists"
    echo "$GRAFANA_SECRET_KEY" | docker secret create ai_coding_agent_grafana_secret - 2>/dev/null || print_warning "Grafana secret already exists"
    echo "$GRAFANA_ADMIN_PASSWORD" | docker secret create ai_coding_agent_grafana_password - 2>/dev/null || print_warning "Grafana password already exists"

    print_success "Docker secrets created successfully"
}

show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  generate     Generate JWT and Supabase keys"
    echo "  secrets      Create Docker secrets for production"
    echo "  help         Show this help message"
    echo
    echo "Examples:"
    echo "  $0 generate          # Generate new keys"
    echo "  $0 secrets           # Create Docker secrets from generated keys"
}

main() {
    case "${1:-generate}" in
        "generate")
            check_dependencies
            generate_production_secrets
            print_info "Next steps:"
            print_info "1. Copy the generated keys to your .env file"
            print_info "2. For production, run: $0 secrets"
            print_info "3. Never commit the generated keys file to version control"
            ;;
        "secrets")
            create_docker_secrets
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi