#!/bin/sh
# Exit immediately if a command exits with a non-zero status.
set -e

echo "Entrypoint: Ensuring .next directory exists and has correct permissions..."
# Create the .next directory if it doesn't exist.
mkdir -p /home/<USER>/app/.next

# Try to change ownership, but don't fail if it doesn't work (Windows/bind mount issues)
echo "Entrypoint: Attempting to fix directory ownership..."
if chown -R node:node /home/<USER>/app/.next 2>/dev/null; then
    echo "Successfully changed ownership of .next directory"
else
    echo "Could not change ownership (likely Windows/bind mount limitation), making directory world-writable..."
    # If we can't change ownership, make it writable by everyone
    chmod -R 777 /home/<USER>/app/.next 2>/dev/null || echo "Warning: Could not change permissions either"
fi

echo "Entrypoint: Handing off to user 'node' to run the application..."
# Try to use 'gosu' to drop privileges and execute as the 'node' user
# If that fails (Windows/Docker Desktop limitation), run as root
if gosu node true 2>/dev/null; then
    echo "Successfully switching to node user"
    exec gosu node "$@"
else
    echo "Cannot switch to node user (Windows/Docker Desktop limitation), running as root..."
    echo "Warning: Application will run as root user"
    exec "$@"
fi
