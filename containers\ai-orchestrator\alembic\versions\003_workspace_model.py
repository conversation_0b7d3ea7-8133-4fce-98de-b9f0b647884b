"""Add workspace model for multi-tenant support

Revision ID: 003_workspace_model
Revises: 002_vector_functions
Create Date: 2025-08-25 16:50:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003_workspace_model'
down_revision = '002_vector_functions'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create workspaces table for multi-tenant workspace management."""
    op.create_table('workspaces',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('container_id', sa.String(), nullable=True),
        sa.Column('container_name', sa.String(), nullable=False),
        sa.Column('subdomain', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('workspace_config', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # Create indexes for performance
    op.create_index(op.f('ix_workspaces_id'), 'workspaces', ['id'], unique=False)
    op.create_index(op.f('ix_workspaces_user_id'), 'workspaces', ['user_id'], unique=False)
    op.create_index(op.f('ix_workspaces_container_id'), 'workspaces', ['container_id'], unique=True)
    op.create_index(op.f('ix_workspaces_container_name'), 'workspaces', ['container_name'], unique=True)
    op.create_index(op.f('ix_workspaces_subdomain'), 'workspaces', ['subdomain'], unique=True)
    op.create_index(op.f('ix_workspaces_status'), 'workspaces', ['status'], unique=False)


def downgrade() -> None:
    """Drop workspaces table."""
    op.drop_index(op.f('ix_workspaces_status'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_subdomain'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_container_name'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_container_id'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_user_id'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_id'), table_name='workspaces')
    op.drop_table('workspaces')