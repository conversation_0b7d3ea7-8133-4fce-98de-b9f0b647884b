"""
Sequential Agents - Backend<PERSON><PERSON> (Supabase Master)

Implements autonomous backend generation using FastAPI, SQLAlchemy, Alembic,
pytest validation, and the project's LLM service. The agent follows a
sequential plan:

1) Analyze request (natural language feature description)
2) Design schema via LLM (SQLAlchemy models)
3) Write model file
4) Generate and apply Alembic migration
5) Design CRUD API via LLM (FastAPI router)
6) Write router file
7) Run tests
8) Return structured result
"""
from __future__ import annotations

import asyncio
import json
import os
import re
import textwrap
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from .base import BaseAgent
from ..router.llm_router import get_llm_service
from ..models.llm_models import GenerateRequest, LLMResponse

logger = logging.getLogger(__name__)


class BackendAgent(BaseAgent):
    """Backend agent specialized in Supabase/FastAPI backend automation."""

    def __init__(self) -> None:
        super().__init__()
        # Project roots
        self._src_dir: Path = Path(__file__).resolve().parents[1]
        self._project_root: Path = Path(__file__).resolve().parents[2]
        # Lazy LLM handle
        self._llm = None

    async def _get_llm(self):
        if self._llm is None:
            self._llm = await get_llm_service()
        return self._llm

    async def _run_alembic_command(self, command: List[str]) -> Tuple[int, str, str]:
        """Run Alembic command in project context.

        Args:
            command: Alembic CLI arguments, e.g., ["revision", "--autogenerate", "-m", "msg"]

        Returns:
            Tuple of (returncode, stdout, stderr)
        """
        cmd = ["alembic", *command]
        proc = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=str(self._project_root),
            env=os.environ.copy(),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        out_b, err_b = await proc.communicate()
        return proc.returncode, out_b.decode(), err_b.decode()

    async def _read_file(self, path: str) -> str:
        """Read a text file asynchronously.

        Args:
            path: Absolute or project-relative path
        """
        p = Path(path)
        if not p.is_absolute():
            p = self._project_root / p
        return await asyncio.to_thread(p.read_text, encoding="utf-8")

    async def _write_file(self, path: str, content: str) -> None:
        """Write text to file, creating parent directories if missing.

        Args:
            path: Absolute or project-relative path
            content: File content
        """
        p = Path(path)
        if not p.is_absolute():
            p = self._project_root / p
        await asyncio.to_thread(p.parent.mkdir, parents=True, exist_ok=True)
        await asyncio.to_thread(p.write_text, content, "utf-8")

    async def _run_tests(self, target: str = "tests") -> Tuple[int, str, str]:
        """Run pytest against a target path.

        Args:
            target: Test target path relative to ai-orchestrator root (default: tests)

        Returns:
            Tuple of (returncode, stdout, stderr)
        """
        cmd = ["pytest", "-q", target]
        proc = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=str(self._project_root),
            env=os.environ.copy(),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        out_b, err_b = await proc.communicate()
        return proc.returncode, out_b.decode(), err_b.decode()

    @staticmethod
    def _slugify(name: str) -> str:
        s = re.sub(r"[^a-zA-Z0-9_]+", "_", name.strip().lower()).strip("_")
        return s or "feature"

    @staticmethod
    def _extract_code(text: str) -> str:
        """Extract code from LLM response fenced blocks if present."""
        fence = re.findall(r"```[a-zA-Z0-9_\-]*\n(.*?)```", text, flags=re.DOTALL)
        if fence:
            return fence[0].strip()
        return text.strip()

    def _model_prompt(self, feature: str, requirements: str) -> str:
        return textwrap.dedent(
            f"""
            You are a senior backend engineer. Design a minimal, production-ready SQLAlchemy model
            for the feature: "{feature}".

            Requirements (natural language):
            {requirements}

            Constraints:
            - Use SQLAlchemy 2.x style where appropriate
            - Import Base from src.models.database: `from src.models.database import Base`
            - Define clear __tablename__ and indexes
            - Include created_at/updated_at timestamps where sensible
            - Only output a single complete Python module, no explanations
            - Do not include Alembic code here

            Example import block:
            from __future__ import annotations
            from typing import Optional
            from datetime import datetime
            from sqlalchemy import String, Integer, DateTime, ForeignKey, Text, Boolean
            from sqlalchemy.orm import Mapped, mapped_column, relationship
            from src.models.database import Base

            Implement the data model(s) needed for the described feature in one file.
            """
        ).strip()

    def _router_prompt(self, feature: str, model_class_names: List[str], route_prefix: str) -> str:
        classes = ", ".join(model_class_names)
        module_import = f"from src.models.{feature} import {classes}" if classes else f"from src.models.{feature} import *"
        return textwrap.dedent(
            f"""
            Create a FastAPI router module with CRUD endpoints for model(s): {classes or feature.title()}.

            Constraints:
            - Import FastAPI and SQLAlchemy session dependency from src.models.database
              `from src.models.database import get_db`
            - Import models using: `{module_import}`
            - Use Pydantic request/response schemas inline if needed
            - Prefix all routes with "{route_prefix}"
            - Use standard CRUD: list, get by id, create, update, delete
            - Handle 404 properly
            - Only output a single complete Python module, no explanations

            Example imports:
            from __future__ import annotations
            from typing import List
            from fastapi import APIRouter, Depends, HTTPException
            from sqlalchemy.orm import Session
            from src.models.database import get_db
            {module_import}
            """
        ).strip()

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the autonomous backend generation workflow.

        Args:
            task_input: A dict containing at least a natural language description under
                        keys like "feature", "title", or "description".

        Returns:
            Structured dict with generation details, files created, and test results.
        """
        # 1) Analyze request
        self.logger.info("BackendAgent received task input for backend generation")
        feature_desc: str = (
            str(task_input.get("feature") or task_input.get("title") or task_input.get("description") or "New feature")
        )
        feature_slug = self._slugify(feature_desc.split("\n")[0])

        llm = await self._get_llm()

        created_files: List[str] = []
        alembic_logs: List[Dict[str, Any]] = []

        # Paths
        models_pkg = self._src_dir / "models"
        routers_pkg = self._src_dir / "routers"
        model_path = models_pkg / f"{feature_slug}.py"
        router_path = routers_pkg / f"{feature_slug}_router.py"

        # 2) Design schema (LLM)
        model_prompt = self._model_prompt(feature_slug, feature_desc)
        model_req = GenerateRequest(prompt=model_prompt)
        model_resp: LLMResponse = await llm.generate(model_req, user_id="backend-agent")
        model_code = self._extract_code(model_resp.content)

        # 3) Write model file
        await self._write_file(str(model_path), model_code)
        created_files.append(str(model_path.relative_to(self._project_root)))

        # 4) Generate Alembic revision (autogenerate)
        rc, out, err = await self._run_alembic_command(["revision", "--autogenerate", "-m", f"add_{feature_slug}"])
        alembic_logs.append({"step": "revision", "returncode": rc, "stdout": out, "stderr": err})
        if rc != 0:
            return {
                "agent": "backend",
                "status": "alembic_revision_failed",
                "files": created_files,
                "alembic": alembic_logs,
                "error": err or out,
            }

        # 5) Apply migration
        rc, out, err = await self._run_alembic_command(["upgrade", "head"])
        alembic_logs.append({"step": "upgrade", "returncode": rc, "stdout": out, "stderr": err})
        if rc != 0:
            return {
                "agent": "backend",
                "status": "alembic_upgrade_failed",
                "files": created_files,
                "alembic": alembic_logs,
                "error": err or out,
            }

        # Determine model class names by simple heuristic (class X(Base))
        model_class_names = sorted(set(re.findall(r"class\s+([A-Za-z_][A-Za-z0-9_]*)\(Base\)", model_code)))
        if not model_class_names:
            # fallback: try class definitions
            model_class_names = sorted(set(re.findall(r"class\s+([A-Za-z_][A-Za-z0-9_]*)\:", model_code)))

        # 6) Design API router (LLM)
        route_prefix = f"/api/{feature_slug}"
        router_prompt = self._router_prompt(feature_slug, model_class_names or [feature_slug.title()], route_prefix)
        router_req = GenerateRequest(prompt=router_prompt)
        router_resp: LLMResponse = await llm.generate(router_req, user_id="backend-agent")
        router_code = self._extract_code(router_resp.content)

        # 7) Write router file
        await self._write_file(str(router_path), router_code)
        created_files.append(str(router_path.relative_to(self._project_root)))

        # 8) Validate via tests
        test_rc, test_out, test_err = await self._run_tests("tests")

        return {
            "agent": "backend",
            "status": "success" if test_rc == 0 else "tests_failed",
            "feature": feature_slug,
            "files": created_files,
            "alembic": alembic_logs,
            "tests": {
                "returncode": test_rc,
                "stdout": test_out,
                "stderr": test_err,
            },
            "model": {
                "model_file": str(model_path.relative_to(self._project_root)),
                "classes": model_class_names,
            },
            "router": {
                "router_file": str(router_path.relative_to(self._project_root)),
                "prefix": route_prefix,
            },
        }