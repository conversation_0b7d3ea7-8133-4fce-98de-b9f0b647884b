#!/bin/bash
# <PERSON><PERSON>t to run backend tests for AI Orchestrator

set -e

echo "🧪 Running AI Orchestrator Backend Tests"

# Check if we're in the right directory
if [ ! -d "tests" ]; then
    echo "❌ Error: tests directory not found. Please run this script from the project root."
    exit 1
fi

# Run all tests
echo "🔍 Running all tests..."
docker-compose exec ai-orchestrator pytest tests/ -v

echo "✅ Tests completed!"
