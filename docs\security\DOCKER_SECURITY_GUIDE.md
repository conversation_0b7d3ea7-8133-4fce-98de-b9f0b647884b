# Docker-in-Docker & Container Security Guide for AI Coding Agent

## Overview

This guide covers Docker-in-Docker (DinD) implementation and container security for the AI Coding Agent's user workspace isolation system in Phase 6, focusing on secure multi-tenant container management and security hardening.

## Table of Contents

1. [Security Architecture](#security-architecture)
2. [Docker-in-Docker Implementation](#docker-in-docker-implementation)
3. [User Workspace Isolation](#user-workspace-isolation)
4. [Container Security Hardening](#container-security-hardening)
5. [Network & Resource Isolation](#network--resource-isolation)
6. [Security Monitoring & Compliance](#security-monitoring--compliance)
7. [Production Deployment](#production-deployment)

## Security Architecture

### Multi-Tenant Container Architecture

```
Host Docker Engine
├── User A Workspace (Isolated Container)
├── User B Workspace (Isolated Container)
└── User C Workspace (Isolated Container)
    ├── Rootless Docker Engine
    ├── Dedicated Network Namespace
    ├── Isolated Volume Storage
    └── Resource Limits (CPU/Memory/Disk)
```

### Security Principles

1. **Defense in Depth**: Multiple security layers (namespaces, cgroups, capabilities)
2. **Least Privilege**: Minimal capabilities and permissions
3. **Zero Trust**: Complete isolation between user workspaces
4. **Resource Controls**: CPU, memory, and disk limits per user
5. **Audit Trail**: Comprehensive logging and monitoring

## Docker-in-Docker Implementation

### Secure DinD Service Configuration

```yaml
# docker-compose.dind.yml
version: '3.8'

services:
  workspace-manager:
    build: ./containers/workspace-manager
    privileged: false
    cap_add: [SYS_ADMIN]  # Required for container management
    cap_drop: [ALL]
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-workspace
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - workspace_data:/workspaces
    environment:
      - MAX_USER_CONTAINERS=5
      - CONTAINER_CPU_LIMIT=2.0
      - CONTAINER_MEMORY_LIMIT=2g
    networks: [workspace-management]

volumes:
  workspace_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/ai-coding-agent/workspaces

networks:
  workspace-management:
    driver: bridge
    ipam:
      config: [subnet: **********/16]
```

### Workspace Manager Core Implementation

```python
# containers/workspace-manager/src/workspace_manager.py
import docker
import uuid
from typing import Dict, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@dataclass
class UserWorkspace:
    user_id: str
    workspace_id: str
    container_id: Optional[str]
    network_id: str
    volume_name: str
    created_at: datetime
    resource_limits: Dict[str, Any]
    status: str

class WorkspaceSecurityManager:
    """Manages secure user workspace containers."""

    def __init__(self):
        self.docker_client = docker.from_env()
        self.workspaces: Dict[str, UserWorkspace] = {}
        self.max_containers_per_user = 5
        self.container_timeout = timedelta(hours=8)

    async def create_user_workspace(
        self,
        user_id: str,
        base_image: str = "python:3.10-slim",
        resource_limits: Optional[Dict[str, Any]] = None
    ) -> UserWorkspace:
        """Create isolated workspace for user."""

        workspace_id = f"workspace-{user_id}-{uuid.uuid4().hex[:8]}"

        # Check user container limits
        user_containers = [ws for ws in self.workspaces.values()
                          if ws.user_id == user_id and ws.status == 'running']

        if len(user_containers) >= self.max_containers_per_user:
            raise ValueError(f"User {user_id} has reached container limit")

        # Set default resource limits
        if not resource_limits:
            resource_limits = {
                'mem_limit': '2g',
                'cpu_quota': 200000,  # 2 CPU cores
                'cpu_period': 100000,
                'pids_limit': 1024
            }

        try:
            # Create isolated network
            network = self._create_user_network(workspace_id)

            # Create dedicated volume
            volume = self._create_user_volume(workspace_id)

            # Create secure container
            container = await self._create_secure_container(
                workspace_id=workspace_id,
                user_id=user_id,
                base_image=base_image,
                network_id=network.id,
                volume_name=volume.name,
                resource_limits=resource_limits
            )

            workspace = UserWorkspace(
                user_id=user_id,
                workspace_id=workspace_id,
                container_id=container.id,
                network_id=network.id,
                volume_name=volume.name,
                created_at=datetime.utcnow(),
                resource_limits=resource_limits,
                status='running'
            )

            self.workspaces[workspace_id] = workspace
            logger.info(f"Created workspace {workspace_id} for user {user_id}")
            return workspace

        except Exception as e:
            logger.error(f"Failed to create workspace: {e}")
            await self._cleanup_workspace_resources(workspace_id)
            raise

    async def _create_secure_container(
        self,
        workspace_id: str,
        user_id: str,
        base_image: str,
        network_id: str,
        volume_name: str,
        resource_limits: Dict[str, Any]
    ) -> docker.models.containers.Container:
        """Create hardened container with security controls."""

        # Security configuration
        container_config = {
            'image': base_image,
            'name': workspace_id,
            'detach': True,
            'user': '1000:1000',  # Non-root user
            'working_dir': '/workspace',
            'environment': {
                'USER_ID': user_id,
                'WORKSPACE_ID': workspace_id,
                'HOME': '/workspace',
                'PYTHONUNBUFFERED': '1'
            },

            # Resource limits
            'mem_limit': resource_limits['mem_limit'],
            'cpu_quota': resource_limits['cpu_quota'],
            'cpu_period': resource_limits['cpu_period'],
            'pids_limit': resource_limits['pids_limit'],

            # Security options
            'security_opt': [
                'no-new-privileges:true',
                'apparmor:docker-workspace'
            ],
            'cap_drop': ['ALL'],
            'cap_add': ['CHOWN', 'DAC_OVERRIDE', 'SETGID', 'SETUID'],

            # Volume and network
            'volumes': {
                volume_name: {'bind': '/workspace', 'mode': 'rw'}
            },
            'tmpfs': {
                '/tmp': 'rw,noexec,nosuid,size=100m'
            },

            # Network security
            'sysctls': {
                'net.ipv4.ip_forward': '0',
                'net.ipv4.conf.all.send_redirects': '0'
            },

            'command': ['/bin/bash', '-c', 'tail -f /dev/null']
        }

        container = self.docker_client.containers.run(**container_config)

        # Connect to isolated network
        user_network = self.docker_client.networks.get(network_id)
        user_network.connect(container)

        return container

    def _create_user_network(self, workspace_id: str):
        """Create isolated network for user workspace."""
        return self.docker_client.networks.create(
            name=f"net-{workspace_id}",
            driver='bridge',
            options={
                'com.docker.network.bridge.enable_icc': 'false',  # Disable inter-container communication
                'com.docker.network.driver.mtu': '1500'
            }
        )

    def _create_user_volume(self, workspace_id: str):
        """Create dedicated volume for user workspace."""
        return self.docker_client.volumes.create(
            name=f"vol-{workspace_id}",
            driver='local',
            driver_opts={
                'type': 'tmpfs',
                'device': 'tmpfs',
                'o': 'size=5g,uid=1000,gid=1000'
            }
        )

    async def destroy_workspace(self, workspace_id: str) -> bool:
        """Safely destroy user workspace."""
        if workspace_id not in self.workspaces:
            return False

        workspace = self.workspaces[workspace_id]

        try:
            # Stop and remove container
            if workspace.container_id:
                container = self.docker_client.containers.get(workspace.container_id)
                container.stop(timeout=10)
                container.remove(force=True)

            # Clean up resources
            await self._cleanup_workspace_resources(workspace_id)
            del self.workspaces[workspace_id]

            logger.info(f"Destroyed workspace {workspace_id}")
            return True

        except Exception as e:
            logger.error(f"Error destroying workspace {workspace_id}: {e}")
            return False

    async def _cleanup_workspace_resources(self, workspace_id: str):
        """Clean up network and volume resources."""
        try:
            # Remove network
            try:
                network = self.docker_client.networks.get(f"net-{workspace_id}")
                network.remove()
            except docker.errors.NotFound:
                pass

            # Remove volume
            try:
                volume = self.docker_client.volumes.get(f"vol-{workspace_id}")
                volume.remove(force=True)
            except docker.errors.NotFound:
                pass
        except Exception as e:
            logger.error(f"Error cleaning up resources: {e}")
```

## User Workspace Isolation

### Security Profiles

```yaml
# AppArmor Profile: /etc/apparmor.d/docker-workspace
profile docker-workspace flags=(attach_disconnected,mediate_deleted) {
  #include <abstractions/base>

  # Allow workspace operations
  /workspace/** rw,
  /tmp/** rw,

  # Deny sensitive system areas
  deny /boot/** rw,
  deny /etc/shadow rw,
  deny /proc/sys/** w,
  deny /sys/** w,

  # Network and capability restrictions
  network inet tcp,
  network inet udp,
  deny network inet raw,
  deny capability sys_admin,
  deny capability sys_module,
  deny mount,
  deny umount,
}
```

### Resource Management

```python
# Resource allocation by user tier
def calculate_user_limits(user_tier: str = 'standard') -> Dict[str, Any]:
    """Calculate resource limits based on user tier."""

    tier_configs = {
        'basic': {
            'cpu_ratio': 0.5,     # 0.5 CPU cores
            'memory_limit': '1g',
            'disk_limit': '2g'
        },
        'standard': {
            'cpu_ratio': 1.0,     # 1 CPU core
            'memory_limit': '2g',
            'disk_limit': '5g'
        },
        'premium': {
            'cpu_ratio': 2.0,     # 2 CPU cores
            'memory_limit': '4g',
            'disk_limit': '10g'
        }
    }

    config = tier_configs.get(user_tier, tier_configs['standard'])

    return {
        'mem_limit': config['memory_limit'],
        'cpu_quota': int(config['cpu_ratio'] * 100000),
        'cpu_period': 100000,
        'pids_limit': 1024,
        'disk_limit': config['disk_limit']
    }
```

## Container Security Hardening

### Secure Dockerfile Template

```dockerfile
# containers/secure-workspace/Dockerfile
FROM python:3.10-slim

# Security: Create non-root user
RUN groupadd -r workspace && useradd -r -g workspace -u 1000 workspace

# Security: Install updates and minimal packages
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        ca-certificates curl git ssh-client && \
    apt-get upgrade -y && \
    rm -rf /var/lib/apt/lists/*

# Security: Create workspace with proper permissions
RUN mkdir -p /workspace && \
    chown workspace:workspace /workspace && \
    chmod 755 /workspace

# Install Python packages as non-root
USER workspace
WORKDIR /workspace

RUN pip install --user --no-cache-dir \
    jupyter ipython requests pandas numpy fastapi uvicorn

# Security: Set secure environment
ENV PATH="/home/<USER>/.local/bin:$PATH"
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --retries=3 \
    CMD python3 -c "import sys; sys.exit(0)"

CMD ["/bin/bash"]
```

## Network & Resource Isolation

### Network Security Configuration

```python
# Network isolation and security
def create_secure_network(workspace_id: str):
    """Create isolated network with security controls."""

    network_config = {
        'name': f"net-{workspace_id}",
        'driver': 'bridge',
        'options': {
            # Disable inter-container communication
            'com.docker.network.bridge.enable_icc': 'false',
            # Enable IP masquerading for outbound traffic
            'com.docker.network.bridge.enable_ip_masquerade': 'true',
            # Set MTU
            'com.docker.network.driver.mtu': '1500'
        },
        # Isolated subnet per workspace
        'ipam': docker.types.IPAMConfig(
            pool_configs=[
                docker.types.IPAMPool(
                    subnet=f"172.21.{hash(workspace_id) % 255}.0/24"
                )
            ]
        )
    }

    return docker_client.networks.create(**network_config)
```

### Resource Monitoring

```python
# Monitor container resource usage
async def monitor_container_resources(container_id: str) -> Dict[str, Any]:
    """Monitor container resource usage."""

    container = docker_client.containers.get(container_id)
    stats = container.stats(stream=False)

    # Calculate metrics
    memory_usage = stats['memory_stats']['usage']
    memory_limit = stats['memory_stats']['limit']
    memory_percent = (memory_usage / memory_limit) * 100

    cpu_delta = (stats['cpu_stats']['cpu_usage']['total_usage'] -
                stats['precpu_stats']['cpu_usage']['total_usage'])
    system_delta = (stats['cpu_stats']['system_cpu_usage'] -
                   stats['precpu_stats']['system_cpu_usage'])
    cpu_percent = (cpu_delta / system_delta) * 100.0 if system_delta > 0 else 0

    return {
        'memory_usage_mb': memory_usage / (1024 * 1024),
        'memory_limit_mb': memory_limit / (1024 * 1024),
        'memory_percent': memory_percent,
        'cpu_percent': cpu_percent,
        'network_rx_bytes': stats['networks']['eth0']['rx_bytes'],
        'network_tx_bytes': stats['networks']['eth0']['tx_bytes']
    }
```

## Security Monitoring & Compliance

### Audit Logging Configuration

```python
# Security audit logging
import logging
import json
from datetime import datetime

class SecurityAuditLogger:
    """Audit logger for security events."""

    def __init__(self):
        self.logger = logging.getLogger('security_audit')
        handler = logging.FileHandler('/var/log/ai-coding-agent/security.log')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def log_workspace_created(self, user_id: str, workspace_id: str, resource_limits: dict):
        """Log workspace creation."""
        event = {
            'event_type': 'workspace_created',
            'user_id': user_id,
            'workspace_id': workspace_id,
            'resource_limits': resource_limits,
            'timestamp': datetime.utcnow().isoformat()
        }
        self.logger.info(json.dumps(event))

    def log_security_violation(self, user_id: str, workspace_id: str, violation_type: str, details: dict):
        """Log security violations."""
        event = {
            'event_type': 'security_violation',
            'user_id': user_id,
            'workspace_id': workspace_id,
            'violation_type': violation_type,
            'details': details,
            'timestamp': datetime.utcnow().isoformat()
        }
        self.logger.warning(json.dumps(event))

    def log_resource_limit_exceeded(self, user_id: str, workspace_id: str, resource_type: str, usage: float, limit: float):
        """Log resource limit violations."""
        event = {
            'event_type': 'resource_limit_exceeded',
            'user_id': user_id,
            'workspace_id': workspace_id,
            'resource_type': resource_type,
            'usage': usage,
            'limit': limit,
            'timestamp': datetime.utcnow().isoformat()
        }
        self.logger.warning(json.dumps(event))
```

### Security Scanning Integration

```bash
#!/bin/bash
# scripts/security-scan.sh

# Container image security scanning
scan_workspace_image() {
    local image_name=$1

    echo "Scanning image: $image_name"

    # Docker Scout scan
    docker scout cves $image_name --format sarif --output scout-results.sarif

    # Trivy scan
    trivy image --format sarif --output trivy-results.sarif $image_name

    # Combine results
    echo "Security scan completed for $image_name"
}

# Runtime container scanning
scan_running_containers() {
    docker ps --format "table {{.Names}}\t{{.Image}}" | grep "workspace-" | while read name image; do
        echo "Scanning running container: $name"

        # Check for privilege escalation
        docker inspect $name | jq '.[] | .HostConfig.Privileged' | grep -q true && \
            echo "WARNING: Container $name running with privileged mode"

        # Check for excessive capabilities
        docker inspect $name | jq '.[] | .HostConfig.CapAdd[]?' | grep -E "(SYS_ADMIN|NET_ADMIN|SYS_MODULE)" && \
            echo "WARNING: Container $name has excessive capabilities"
    done
}

# Main security scan
main() {
    echo "Starting security scan..."
    scan_workspace_image "ai-coding-agent/workspace:latest"
    scan_running_containers
    echo "Security scan completed"
}

main "$@"
```

## Production Deployment

### Production Security Configuration

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  workspace-manager:
    image: ai-coding-agent/workspace-manager:latest
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-workspace
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
    volumes:
      - workspace_data:/workspaces:rw
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO
      - SECURITY_SCAN_ENABLED=true
    networks:
      - workspace-management
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  workspace-management:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: br-workspace
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
```

### Security Checklist

**Pre-deployment Security Checklist:**

- [ ] All containers run as non-root users
- [ ] AppArmor/SELinux profiles applied
- [ ] Resource limits configured for all containers
- [ ] Network isolation implemented
- [ ] Security scanning integrated in CI/CD
- [ ] Audit logging enabled
- [ ] Secrets management configured
- [ ] Regular security updates scheduled
- [ ] Vulnerability scanning automated
- [ ] Incident response procedures documented

## Key Takeaways

1. **Multi-layered Security**: Use namespaces, cgroups, capabilities, and AppArmor for defense in depth
2. **User Isolation**: Each user gets dedicated containers, networks, and volumes
3. **Resource Controls**: CPU, memory, and disk limits prevent resource exhaustion
4. **Network Isolation**: Dedicated networks prevent cross-user communication
5. **Security Monitoring**: Comprehensive logging and audit trails
6. **Rootless Containers**: All user containers run as non-root users
7. **Automated Scanning**: Regular security scans for images and running containers
8. **Compliance Ready**: Audit logs and security controls for compliance requirements

This guide provides a secure foundation for implementing multi-tenant container workspaces in the AI Coding Agent while maintaining strong security boundaries between users.