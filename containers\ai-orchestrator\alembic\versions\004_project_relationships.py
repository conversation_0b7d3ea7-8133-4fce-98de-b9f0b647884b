"""Add project and user_project_association tables

Revision ID: 004_project_relationships
Revises: 003_workspace_model
Create Date: 2025-01-23 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004_project_relationships'
down_revision = '003_workspace_model'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create projects and user_project_associations tables."""

    # Create projects table
    op.create_table('projects',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('owner_id', sa.Integer(), nullable=False),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('project_type', sa.String(), nullable=True),
        sa.Column('repository_url', sa.String(), nullable=True),
        sa.Column('deployment_url', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('project_config', sa.Text(), nullable=True),
        sa.Column('is_public', sa.Boolean(), nullable=False),
        sa.Column('tags', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # Create indexes for projects table
    op.create_index(op.f('ix_projects_id'), 'projects', ['id'], unique=False)
    op.create_index(op.f('ix_projects_name'), 'projects', ['name'], unique=False)
    op.create_index(op.f('ix_projects_owner_id'), 'projects', ['owner_id'], unique=False)
    op.create_index(op.f('ix_projects_status'), 'projects', ['status'], unique=False)
    op.create_index(op.f('ix_projects_project_type'), 'projects', ['project_type'], unique=False)

    # Create user_project_associations table
    op.create_table('user_project_associations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.String(), nullable=False),
        sa.Column('role', sa.String(), nullable=False),
        sa.Column('permissions', sa.Text(), nullable=True),
        sa.Column('joined_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create indexes for user_project_associations table
    op.create_index(op.f('ix_user_project_associations_id'), 'user_project_associations', ['id'], unique=False)
    op.create_index(op.f('ix_user_project_associations_user_id'), 'user_project_associations', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_project_associations_project_id'), 'user_project_associations', ['project_id'], unique=False)

    # Create unique constraint for active user-project associations
    op.create_index(
        'ix_unique_active_user_project',
        'user_project_associations',
        ['user_id', 'project_id'],
        unique=True,
        postgresql_where=sa.text('is_active = true')
    )


def downgrade() -> None:
    """Drop projects and user_project_associations tables."""

    # Drop user_project_associations table and indexes
    op.drop_index('ix_unique_active_user_project', table_name='user_project_associations')
    op.drop_index(op.f('ix_user_project_associations_project_id'), table_name='user_project_associations')
    op.drop_index(op.f('ix_user_project_associations_user_id'), table_name='user_project_associations')
    op.drop_index(op.f('ix_user_project_associations_id'), table_name='user_project_associations')
    op.drop_table('user_project_associations')

    # Drop projects table and indexes
    op.drop_index(op.f('ix_projects_project_type'), table_name='projects')
    op.drop_index(op.f('ix_projects_status'), table_name='projects')
    op.drop_index(op.f('ix_projects_owner_id'), table_name='projects')
    op.drop_index(op.f('ix_projects_name'), table_name='projects')
    op.drop_index(op.f('ix_projects_id'), table_name='projects')
    op.drop_table('projects')