import os
import sys
import uuid
from pathlib import Path
from typing import Generator

import pytest

# Ensure the AI orchestrator src is importable
AI_SRC_PATH = os.path.join(
    os.path.dirname(__file__), "..", "..", "containers", "ai-orchestrator", "src"
)
sys.path.insert(0, os.path.abspath(AI_SRC_PATH))

# IMPORTANT: set DATABASE_URL before importing database module so it uses SQLite for tests
os.environ["DATABASE_URL"] = os.environ.get("TEST_DATABASE_URL", "sqlite:///./test_project_repo.db")

from src.models.database import create_tables, SessionLocal  # type: ignore  # noqa: E402
from src.models.user import User  # type: ignore  # noqa: E402
from src.models.project import Project  # type: ignore  # noqa: E402
from src.repository.project_repository import ProjectRepository  # type: ignore  # noqa: E402
from src.repository.user_repository import UserRepository  # type: ignore  # noqa: E402
from src.core.config import settings  # type: ignore  # noqa: E402


@pytest.fixture(scope="session", autouse=True)
def setup_database() -> None:
    """Create all tables in the SQLite test database once per session."""
    create_tables()


@pytest.fixture()
def db_session() -> Generator:
    """Provide a database session for tests."""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture()
def temp_workspace(tmp_path: Path, monkeypatch: pytest.MonkeyPatch) -> Path:
    """Use a temporary directory for WORKSPACE_PATH to avoid touching real FS."""
    monkeypatch.setattr(settings, "WORKSPACE_PATH", tmp_path)
    return tmp_path


def _create_test_user(db) -> User:
    u = User.create_from_supabase_user(
        supabase_user_id=uuid.uuid4(),
        email="<EMAIL>",
        username="dbtestuser",
        full_name="DB Test User",
    )
    # Provide required hashed_password field if constructor didn't
    if not getattr(u, "hashed_password", None):
        u.hashed_password = "test"
    db.add(u)
    db.commit()
    db.refresh(u)
    return u


@pytest.mark.asyncio
async def test_create_project_persists_db_and_fs(db_session, temp_workspace: Path) -> None:
    repo = ProjectRepository()

    # Create test user in DB
    user = _create_test_user(db_session)

    # Create project via repository (filesystem + DB)
    project_name = "proj_alpha"
    project = await repo.create_project(
        db=db_session,
        user=user,
        project_name=project_name,
        description="Alpha project",
    )

    # Verify DB record
    assert isinstance(project, Project)
    assert project.id is not None
    assert project.name == project_name

    # Verify FS directory
    user_dir = temp_workspace / f"user_{user.id}"
    assert (user_dir / project_name).exists()

    # Verify relationship via ORM
    db_session.refresh(user)
    user_projects = [p.name for p in user.projects]
    assert project_name in user_projects


@pytest.mark.asyncio
async def test_user_repository_get_user_projects_returns_persisted(db_session, temp_workspace: Path) -> None:
    user_repo = UserRepository()
    project_repo = ProjectRepository()

    # Create test user and one project
    user = _create_test_user(db_session)
    project_name = "proj_beta"
    await project_repo.create_project(
        db=db_session,
        user=user,
        project_name=project_name,
        description=None,
    )

    # Fetch projects through UserRepository
    projects = user_repo.get_user_projects(db_session, user.id)
    names = [p.name for p in projects]
    assert project_name in names