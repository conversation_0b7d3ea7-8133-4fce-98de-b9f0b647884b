# Comprehensive .dockerignore for Code Server
# Reduces build context size and prevents sensitive files from being copied

# Version control
.git
.gitignore
.gitattributes
.gitmodules

# Environment files (sensitive)
.env
.env.*
!.env.example

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
docs/
*.md
!README.md

# Secrets and certificates
secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Backup files
*.bak
*.backup
*.orig

# Code-server specific
.config/
.cache/
.local/

# Extensions cache (but allow our custom extensions)
extensions/
!extensions/ai-chat-extension/
!extensions/project-importer/

# Application data
data/

# Node.js (if any extensions need it)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python cache (if any)
__pycache__/
*.py[cod]
*$py.class

# Workspace files (these will be mounted as volumes)
workspace/