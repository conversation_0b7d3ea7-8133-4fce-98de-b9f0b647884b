# Network Security Configuration for AI Coding Agent
# This file contains network security configurations and best practices

## Docker Network Isolation

The project uses custom bridge networks with:
- Subnet isolation: **********/24
- IP range restriction: **********/26 (64 IPs)
- Host binding to localhost only
- Inter-container communication enabled for services

## Service Communication Matrix

| Service | Ports | Access Level | External Access |
|---------|-------|-------------|-----------------|
| ai-orchestrator | 8000 | Internal + External | Yes (API) |
| admin-dashboard | 3000 | Internal + External | Yes (Web UI) |
| code-server | 8080 | External | Yes (IDE) |
| postgresql | 5432 | Internal Only | No |
| redis | 6379 | Internal Only | No |
| ollama | 11434 | Internal Only | No |

## Production Security Recommendations

### 1. Firewall Rules (iptables/ufw)

```bash
# Allow only necessary ports
sudo ufw allow 8000/tcp  # API
sudo ufw allow 3000/tcp  # Dashboard
sudo ufw allow 8080/tcp  # Code Server
sudo ufw deny 5432/tcp   # Block external PostgreSQL
sudo ufw deny 6379/tcp   # Block external Redis
sudo ufw deny 11434/tcp  # Block external Ollama
```

### 2. Docker Daemon Security

```json
{
  "icc": false,
  "userland-proxy": false,
  "iptables": true,
  "ip-forward": false,
  "bridge": "none",
  "default-gateway": "**********"
}
```

### 3. Container Security Labels

All containers include security labels:
- Project identification
- Network isolation
- Access control metadata

### 4. Network Policies (for Kubernetes deployment)

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ai-coding-agent-network-policy
spec:
  podSelector:
    matchLabels:
      app: ai-coding-agent
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ai-coding-agent
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: ai-coding-agent
```

## Monitoring and Alerting

1. **Network Traffic Monitoring**: Monitor unusual network patterns
2. **Port Scanning Detection**: Alert on port scanning attempts
3. **Connection Logging**: Log all inter-service connections
4. **Rate Limiting**: Implement rate limiting on public endpoints

## Security Checklist

- [x] Custom network isolation
- [x] Minimal port exposure
- [x] Host binding restrictions
- [x] Service-to-service authentication
- [ ] TLS termination (add HTTPS)
- [ ] VPN access for management
- [ ] Network intrusion detection
- [ ] DDoS protection