# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: FastAPI router for LLM service endpoints

from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
import redis.asyncio as redis  # type: ignore[import-untyped]
import os
import logging

from ..utils.auth import get_current_user
from ..services.enhanced_llm_service import EnhancedLLMService
from ..models.llm_models import (
    GenerateRequest, LLMProvider, ModelPullRequest,
    RateLimitExceededError, ProviderUnavailableError, GenerationError
)

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(prefix="/api/llm", tags=["LLM"])

# Initialize Redis connection for rate limiting (optional)
async def get_redis_client() -> Optional[redis.Redis]:
    """Get Redis client for caching and rate limiting."""
    try:
        # Use the shared Redis service instead of creating a new connection
        from ..services.redis_service import get_redis_client as get_shared_redis_client
        return await get_shared_redis_client()
    except Exception as e:
        logger.warning(f"Redis not available for rate limiting: {str(e)}")
        return None

# Global LLM service instance (will be initialized on startup)
_llm_service: Optional[EnhancedLLMService] = None

async def get_llm_service() -> EnhancedLLMService:
    """Get or create LLM service instance."""
    global _llm_service
    if _llm_service is None:
        redis_client = await get_redis_client()
        _llm_service = EnhancedLLMService(redis_client=redis_client)
    return _llm_service


@router.get("/health")
async def health_check():
    """Comprehensive health check for all LLM providers."""
    try:
        llm_service = await get_llm_service()
        health = await llm_service.health_check()

        # Convert to dict for JSON serialization
        return {
            "status": health.status,
            "providers": [
                {
                    "provider": status.provider.value,
                    "available": status.available,
                    "api_key_configured": status.api_key_configured,
                    "error_message": status.error_message,
                    "response_time_ms": status.response_time_ms,
                    "last_checked": status.last_checked
                }
                for status in health.providers
            ],
            "total_requests": health.total_requests,
            "failed_requests": health.failed_requests,
            "average_response_time_ms": health.average_response_time_ms
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


@router.get("/providers")
async def list_providers():
    """List all available LLM providers and their status."""
    try:
        llm_service = await get_llm_service()
        providers_info = []

        for provider in LLMProvider:
            status = await llm_service.test_provider_connection(provider)
            config = llm_service.providers.get(provider, {})

            providers_info.append({
                "provider": provider.value,
                "enabled": config.get("enabled", False),
                "available": status.available,
                "api_key_configured": status.api_key_configured,
                "default_model": config.get("default_model"),
                "base_url": config.get("base_url"),
                "timeout": config.get("timeout"),
                "response_time_ms": status.response_time_ms,
                "error_message": status.error_message
            })

        return {"providers": providers_info}
    except Exception as e:
        logger.error(f"Failed to list providers: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models")
async def list_models(
    provider: Optional[LLMProvider] = None,
    current_user=Depends(get_current_user)
):
    """List available models from all or specific providers."""
    try:
        llm_service = await get_llm_service()
        models = await llm_service.list_available_models(provider)

        # Convert to dict for JSON serialization
        models_data = []
        for model in models:
            models_data.append({
                "name": model.name,
                "provider": model.provider.value,
                "status": model.status.value,
                "size": model.size,
                "modified_at": model.modified_at,
                "parameters": model.parameters,
                "context_length": model.context_length,
                "cost_per_token": model.cost_per_token
            })

        return {"models": models_data}
    except Exception as e:
        logger.error(f"Failed to list models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate")
async def generate_text(
    request: GenerateRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user)
):
    """Generate text using LLM with comprehensive error handling and monitoring."""
    try:
        llm_service = await get_llm_service()

        # Extract user ID from current_user (adjust based on your auth implementation)
        user_id = getattr(current_user, 'id', 'default')

        response = await llm_service.generate(request, user_id=user_id)

        # Log successful generation in background
        background_tasks.add_task(
            _log_generation_success,
            user_id, request.provider or "auto", request.model or "auto",
            len(request.prompt), response.usage.completion_tokens
        )

        return {
            "content": response.content,
            "model": response.model,
            "provider": response.provider.value,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens,
                "cost": response.usage.cost,
                "duration_ms": response.usage.duration_ms
            },
            "request_id": response.request_id,
            "finish_reason": response.finish_reason,
            "created_at": response.created_at
        }

    except RateLimitExceededError as e:
        logger.warning(f"Rate limit exceeded for user {current_user}: {str(e)}")
        raise HTTPException(
            status_code=429,
            detail={
                "error": "Rate limit exceeded",
                "message": str(e),
                "provider": e.provider
            }
        )
    except ProviderUnavailableError as e:
        logger.error(f"Provider unavailable: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail={
                "error": "Provider unavailable",
                "message": str(e),
                "provider": e.provider
            }
        )
    except GenerationError as e:
        logger.error(f"Generation failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Generation failed",
                "message": str(e),
                "provider": e.provider
            }
        )
    except Exception as e:
        logger.error(f"Unexpected error in text generation: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/rate-limits/{provider}")
async def get_rate_limits(
    provider: LLMProvider,
    current_user=Depends(get_current_user)
):
    """Get current rate limit status for a provider."""
    try:
        llm_service = await get_llm_service()
        user_id = getattr(current_user, 'id', 'default')

        rate_limit = await llm_service.check_rate_limit(provider, user_id)

        return {
            "provider": provider.value,
            "requests_per_minute": rate_limit.requests_per_minute,
            "requests_remaining": rate_limit.requests_remaining,
            "reset_time": rate_limit.reset_time,
            "cost_limit_usd": rate_limit.cost_limit_usd,
            "cost_used_usd": rate_limit.cost_used_usd
        }
    except Exception as e:
        logger.error(f"Failed to get rate limits: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/models/pull")
async def pull_model(
    request: ModelPullRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user)
):
    """Pull a model from the provider (currently only supports Ollama)."""
    try:
        llm_service = await get_llm_service()
        result = await llm_service.pull_model(request)

        if result.success:
            # Log successful pull in background
            background_tasks.add_task(
                _log_model_pull,
                getattr(current_user, 'id', 'default'),
                request.model_name,
                request.provider.value,
                "success"
            )

            return {
                "success": True,
                "message": result.message,
                "model_name": result.model_name,
                "provider": result.provider.value
            }
        else:
            # Log failed pull in background
            background_tasks.add_task(
                _log_model_pull,
                getattr(current_user, 'id', 'default'),
                request.model_name,
                request.provider.value,
                "failed"
            )

            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Model pull failed",
                    "message": result.message
                }
            )

    except ProviderUnavailableError as e:
        raise HTTPException(
            status_code=400,
            detail={
                "error": "Provider not supported for model pulling",
                "message": str(e)
            }
        )
    except Exception as e:
        logger.error(f"Failed to pull model: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_usage_statistics(
    time_window: str = "1h",
    current_user=Depends(get_current_user)
):
    """Get usage statistics for monitoring and analytics."""
    try:
        llm_service = await get_llm_service()
        stats = await llm_service.get_usage_statistics(time_window)

        return stats
    except Exception as e:
        logger.error(f"Failed to get statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate-keys")
async def validate_api_keys(current_user=Depends(get_current_user)):
    """Validate API keys for all configured providers."""
    try:
        llm_service = await get_llm_service()
        results = {}

        for provider in LLMProvider:
            try:
                is_valid = await llm_service.validate_api_key(provider)
                results[provider.value] = {
                    "valid": is_valid,
                    "configured": bool(llm_service.providers.get(provider, {}).get('api_key')) or provider == LLMProvider.OLLAMA
                }
            except Exception as e:
                results[provider.value] = {
                    "valid": False,
                    "configured": False,
                    "error": str(e)
                }

        return {"api_keys": results}
    except Exception as e:
        logger.error(f"Failed to validate API keys: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Background task functions
async def _log_generation_success(user_id: str, provider: str, model: str,
                                prompt_length: int, completion_tokens: int):
    """Log successful generation for analytics."""
    logger.info(
        f"Generation success - User: {user_id}, Provider: {provider}, "
        f"Model: {model}, Prompt length: {prompt_length}, "
        f"Completion tokens: {completion_tokens}"
    )


async def _log_model_pull(user_id: str, model_name: str, provider: str, status: str):
    """Log model pull operation."""
    logger.info(
        f"Model pull {status} - User: {user_id}, Model: {model_name}, "
        f"Provider: {provider}"
    )


# Startup function to initialize the service
async def initialize_llm_service():
    """Initialize the LLM service on startup."""
    global _llm_service
    try:
        redis_client = await get_redis_client()
        _llm_service = EnhancedLLMService(redis_client=redis_client)
        logger.info("Enhanced LLM service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize LLM service: {str(e)}")
        # Create service without Redis as fallback
        _llm_service = EnhancedLLMService(redis_client=None)
        logger.warning("LLM service initialized without Redis (rate limiting disabled)")