# Code-Server Setup Reference Guide

## Overview

This guide provides comprehensive instructions for setting up code-server using the Linux Server Docker image for the AI Coding Agent project. Code-server allows you to run VS Code in a browser, providing a consistent development environment accessible from anywhere.

## Key Features

- **Browser-based VS Code**: Full VS Code experience in your browser
- **Multi-platform support**: Works on Chromebooks, tablets, laptops
- **Remote development**: Leverage powerful cloud servers for development
- **Battery preservation**: Computation runs on server, not local device
- **Consistent environment**: Same development setup across all devices

## Supported Architectures

| Architecture | Available | Tag |
|--------------|-----------|-----|
| x86-64       | ✅        | amd64-\<version\> |
| arm64        | ✅        | arm64v8-\<version\> |

## Quick Start

### Docker Compose (Recommended)

Create a `docker-compose.yml` file:

```yaml
---
services:
  code-server:
    image: lscr.io/linuxserver/code-server:latest
    container_name: code-server
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Etc/UTC
      - PASSWORD=your_secure_password
      - DEFAULT_WORKSPACE=/config/workspace
      - PWA_APPNAME=AI-Coding-Agent
    volumes:
      - ./data/code-server:/config
      - ./workspace:/config/workspace
    ports:
      - "8443:8443"
    restart: unless-stopped
    networks:
      - ai-coding-network
```

### Docker CLI

```bash
docker run -d \
  --name=code-server \
  -e PUID=1000 \
  -e PGID=1000 \
  -e TZ=Etc/UTC \
  -e PASSWORD=your_secure_password \
  -e DEFAULT_WORKSPACE=/config/workspace \
  -e PWA_APPNAME=AI-Coding-Agent \
  -p 8443:8443 \
  -v ./data/code-server:/config \
  -v ./workspace:/config/workspace \
  --restart unless-stopped \
  lscr.io/linuxserver/code-server:latest
```

## Configuration Parameters

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `PUID` | User ID for file permissions | Yes | 1000 |
| `PGID` | Group ID for file permissions | Yes | 1000 |
| `TZ` | Timezone setting | Yes | Etc/UTC |
| `PASSWORD` | Web GUI password | Optional | No auth |
| `HASHED_PASSWORD` | Hashed password (overrides PASSWORD) | Optional | - |
| `SUDO_PASSWORD` | Sudo access password | Optional | - |
| `SUDO_PASSWORD_HASH` | Hashed sudo password | Optional | - |
| `PROXY_DOMAIN` | Domain for subdomain proxying | Optional | - |
| `DEFAULT_WORKSPACE` | Default workspace directory | Optional | /config/workspace |
| `PWA_APPNAME` | Progressive Web App name | Optional | code-server |

### Ports

| Port | Function |
|------|----------|
| 8443 | Web GUI access |

### Volumes

| Volume | Function |
|--------|----------|
| `/config` | Configuration files and user data |
| `/config/workspace` | Default workspace (recommended) |

## Integration with AI Coding Agent

### Recommended Configuration for AI Coding Agent

```yaml
services:
  code-server:
    image: lscr.io/linuxserver/code-server:latest
    container_name: ai-coding-code-server
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=America/New_York
      - PASSWORD=${CODE_SERVER_PASSWORD:-ai_coding_agent_2024}
      - DEFAULT_WORKSPACE=/config/workspace
      - PWA_APPNAME=AI-Coding-Agent
      - SUDO_PASSWORD=${SUDO_PASSWORD:-admin_2024}
    volumes:
      # Configuration and user data
      - ${DATA_PATH:-./data}/code-server:/config
      # Project workspace
      - ${WORKSPACE_PATH:-./workspace}:/config/workspace
      # SSH keys for Git integration
      - ${SSH_KEY_PATH:-~/.ssh}:/config/.ssh:ro
      # AI orchestrator source code (for development)
      - ./src:/config/workspace/ai-orchestrator/src
      # Admin dashboard source code
      - ./admin-dashboard:/config/workspace/admin-dashboard
    ports:
      - "${CODE_SERVER_PORT:-8443}:8443"
    restart: unless-stopped
    networks:
      - ai-coding-network
    depends_on:
      - ai-orchestrator
      - postgresql
    labels:
      - "com.ai-coding-agent.service=code-server"
      - "com.ai-coding-agent.environment=${ENVIRONMENT:-development}"
```

### Environment Variables (.env)

Add to your `.env` file:

```bash
# Code-Server Configuration
CODE_SERVER_PASSWORD=your_secure_password_here
SUDO_PASSWORD=your_sudo_password_here
CODE_SERVER_PORT=8443
WORKSPACE_PATH=./workspace
SSH_KEY_PATH=~/.ssh

# Optional: Custom domain for proxying
# PROXY_DOMAIN=code-server.your-domain.com
```

## Security Configuration

### Password Security

#### Option 1: Plain Password
```yaml
environment:
  - PASSWORD=your_secure_password
```

#### Option 2: Hashed Password (Recommended)
```bash
# Generate hashed password
echo -n 'your_password' | npx argon2-cli -e

# Use in docker-compose.yml
environment:
  - HASHED_PASSWORD=$argon2i$v=19$m=4096,t=3,p=1$hash_here
```

### Docker Secrets (Production)

For production environments, use Docker secrets:

```yaml
services:
  code-server:
    image: lscr.io/linuxserver/code-server:latest
    environment:
      - FILE__PASSWORD=/run/secrets/code_server_password
      - FILE__SUDO_PASSWORD=/run/secrets/sudo_password
    secrets:
      - code_server_password
      - sudo_password

secrets:
  code_server_password:
    external: true
  sudo_password:
    external: true
```

## Git Integration Setup

### SSH Key Configuration

1. **Copy SSH keys to the container**:
   ```bash
   # Option 1: Mount existing SSH keys
   volumes:
     - ~/.ssh:/config/.ssh:ro

   # Option 2: Copy keys manually after container start
   docker cp ~/.ssh/id_rsa code-server:/config/.ssh/
   docker cp ~/.ssh/id_rsa.pub code-server:/config/.ssh/
   ```

2. **Configure Git user**:
   ```bash
   # Access container terminal
   docker exec -it code-server /bin/bash

   # Configure Git
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"

   # Test SSH connection
   ssh -T **************
   ```

## Development Workflow

### Accessing Code-Server

1. **Web Interface**: http://localhost:8443
2. **Enter password** (if configured)
3. **Open workspace**: Should automatically open `/config/workspace`

### Recommended Extensions

Install these VS Code extensions for AI development:

- Python
- Pylance
- Docker
- YAML
- REST Client
- Git Graph
- Python Docstring Generator
- Python Type Hint
- pytest IntelliSense

### Project Structure in Code-Server

```
/config/workspace/
├── ai-orchestrator/          # FastAPI backend
│   ├── src/
│   ├── tests/
│   └── requirements.txt
├── admin-dashboard/          # Next.js frontend
│   ├── src/
│   ├── package.json
│   └── next.config.js
├── shared/                   # Shared utilities
├── docs/                     # Documentation
└── scripts/                  # Development scripts
```

## Networking Configuration

### Docker Network Integration

```yaml
networks:
  ai-coding-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

services:
  code-server:
    networks:
      ai-coding-network:
        ipv4_address: ***********
```

### Proxy Configuration

For subdomain proxying (e.g., with Traefik):

```yaml
environment:
  - PROXY_DOMAIN=code-server.ai-coding-agent.local
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.code-server.rule=Host(\`code-server.ai-coding-agent.local\`)"
  - "traefik.http.services.code-server.loadbalancer.server.port=8443"
```

## Advanced Configuration

### Read-Only Operation

For enhanced security:

```yaml
services:
  code-server:
    read_only: true
    tmpfs:
      - /tmp
    environment:
      # Sudo will not be available in read-only mode
```

### Non-Root Operation

```yaml
services:
  code-server:
    user: "1000:1000"
    # Sudo will not be available
```

### Custom User/Group IDs

```bash
# Find your user/group IDs
id your_username

# Use in docker-compose.yml
environment:
  - PUID=1001
  - PGID=1001
```

## Monitoring and Maintenance

### Container Management

```bash
# View logs
docker logs -f code-server

# Access shell
docker exec -it code-server /bin/bash

# Check container status
docker inspect code-server

# Monitor resource usage
docker stats code-server
```

### Updates

```bash
# Update via Docker Compose
docker-compose pull code-server
docker-compose up -d code-server

# Update via Docker CLI
docker pull lscr.io/linuxserver/code-server:latest
docker stop code-server
docker rm code-server
# Recreate with same parameters
```

### Backup

```bash
# Backup configuration
tar -czf code-server-backup-$(date +%Y%m%d).tar.gz ./data/code-server

# Backup workspace
tar -czf workspace-backup-$(date +%Y%m%d).tar.gz ./workspace
```

## Troubleshooting

### Common Issues

1. **Permission Issues**:
   ```bash
   # Fix ownership
   sudo chown -R 1000:1000 ./data/code-server
   sudo chown -R 1000:1000 ./workspace
   ```

2. **Port Conflicts**:
   ```bash
   # Check port usage
   netstat -tulpn | grep 8443

   # Use different port
   ports:
     - "8444:8443"
   ```

3. **Password Not Working**:
   ```bash
   # Check environment variables
   docker exec code-server env | grep -i password

   # Restart container
   docker-compose restart code-server
   ```

4. **Git SSH Issues**:
   ```bash
   # Check SSH key permissions
   docker exec code-server ls -la /config/.ssh/

   # Fix permissions
   docker exec code-server chmod 600 /config/.ssh/id_rsa
   docker exec code-server chmod 644 /config/.ssh/id_rsa.pub
   ```

### Performance Optimization

1. **Resource Limits**:
   ```yaml
   deploy:
     resources:
       limits:
         memory: 2G
         cpus: '1.0'
       reservations:
         memory: 1G
         cpus: '0.5'
   ```

2. **Volume Optimization**:
   ```yaml
   volumes:
     # Use named volumes for better performance
     - code-server-data:/config
     - workspace-data:/config/workspace
   ```

## Integration Examples

### Complete AI Coding Agent Stack

```yaml
version: '3.8'

services:
  # AI Orchestrator
  ai-orchestrator:
    build: ./containers/ai-orchestrator
    environment:
      - DATABASE_URL=********************************************/ai_coding_agent
    depends_on:
      - postgresql
    networks:
      - ai-coding-network

  # Code Server
  code-server:
    image: lscr.io/linuxserver/code-server:latest
    container_name: ai-coding-code-server
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=America/New_York
      - PASSWORD=${CODE_SERVER_PASSWORD}
      - DEFAULT_WORKSPACE=/config/workspace
      - PWA_APPNAME=AI-Coding-Agent
    volumes:
      - ./data/code-server:/config
      - ./workspace:/config/workspace
      - ./src:/config/workspace/ai-orchestrator/src
    ports:
      - "8443:8443"
    networks:
      - ai-coding-network
    depends_on:
      - ai-orchestrator

  # PostgreSQL
  postgresql:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=ai_coding_agent
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=ai_pass
    volumes:
      - ./data/postgresql:/var/lib/postgresql/data
    networks:
      - ai-coding-network

networks:
  ai-coding-network:
    driver: bridge
```

## Security Best Practices

1. **Use strong passwords** and consider password hashing
2. **Limit network exposure** using Docker networks
3. **Mount SSH keys read-only** when possible
4. **Regular updates** of the code-server image
5. **Monitor access logs** for suspicious activity
6. **Use HTTPS** in production with proper certificates
7. **Implement proper backup strategies** for important data

## Conclusion

This guide provides a comprehensive setup for code-server integration with your AI Coding Agent project. The configuration ensures:

- Secure access to your development environment
- Proper integration with the existing Docker stack
- Git workflow support
- Scalable and maintainable setup
- Production-ready security practices

For additional help, refer to the [Linux Server documentation](https://docs.linuxserver.io/images/docker-code-server/) or check the container logs for specific issues.