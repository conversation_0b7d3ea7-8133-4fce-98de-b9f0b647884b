from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import os
import json
import base64
import hmac
import hashlib
import time
from typing import Optional, Any, Dict, Union
from datetime import datetime, timedelta, timezone
import logging
import uuid

# Import centralized configuration
from ..core.config import settings

# Import secrets utility for secure configuration
from .secrets import read_secret

# Optional dependencies with graceful fallback
try:
    from supabase import create_client, Client  # type: ignore[import-untyped]
    SUPABASE_AVAILABLE = True
    ClientType = Client
except ImportError:
    SUPABASE_AVAILABLE = False
    ClientType = None  # type: ignore
    def create_client(*args, **kwargs):
        raise NotImplementedError("Supabase not available")

try:
    from jose import JWTError, jwt  # type: ignore[import-untyped]
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False
    class JWTError(Exception): pass
    class jwt:
        @staticmethod
        def encode(*args, **kwargs):
            raise NotImplementedError("JWT not available")
        @staticmethod
        def decode(*args, **kwargs):
            raise NotImplementedError("JWT not available")

logger = logging.getLogger(__name__)

# Initialize Supabase client with secrets support
SUPABASE_URL = read_secret("supabase_url", "SUPABASE_URL")
SUPABASE_KEY = read_secret("supabase_key", "SUPABASE_ANON_KEY")
SUPABASE_SERVICE_KEY = read_secret("supabase_service_key", "SUPABASE_SERVICE_KEY")

supabase: Optional[Any] = None

# =====================================================================================
# LOCAL JWT FALLBACK IMPLEMENTATION
# =====================================================================================

class LocalJWTError(Exception):
    """Custom exception for local JWT operations."""
    pass


class LocalJWT:
    """
    Simple, secure local JWT implementation for fallback authentication.

    This implementation provides basic JWT functionality when external libraries
    are not available, using only Python standard library components.
    """

    @staticmethod
    def _base64url_encode(data: bytes) -> str:
        """Base64URL encode data without padding."""
        return base64.urlsafe_b64encode(data).decode('utf-8').rstrip('=')

    @staticmethod
    def _base64url_decode(data: str) -> bytes:
        """Base64URL decode data, adding padding if necessary."""
        # Add padding if needed
        padding = 4 - (len(data) % 4)
        if padding != 4:
            data += '=' * padding
        return base64.urlsafe_b64decode(data)

    @staticmethod
    def _create_signature(header_payload: str, secret: str) -> str:
        """Create HMAC-SHA256 signature for JWT."""
        signature = hmac.new(
            secret.encode('utf-8'),
            header_payload.encode('utf-8'),
            hashlib.sha256
        ).digest()
        return LocalJWT._base64url_encode(signature)

    @staticmethod
    def encode(payload: Dict[str, Any], secret: str, algorithm: str = "HS256") -> str:
        """
        Encode a JWT token using local implementation.

        Args:
            payload: Token payload data
            secret: Secret key for signing
            algorithm: Signing algorithm (only HS256 supported)

        Returns:
            JWT token string

        Raises:
            LocalJWTError: If encoding fails
        """
        if algorithm != "HS256":
            raise LocalJWTError(f"Unsupported algorithm: {algorithm}")

        try:
            # Create header
            header = {
                "typ": "JWT",
                "alg": algorithm
            }

            # Encode header and payload
            header_encoded = LocalJWT._base64url_encode(json.dumps(header).encode('utf-8'))
            payload_encoded = LocalJWT._base64url_encode(json.dumps(payload).encode('utf-8'))

            # Create signature
            header_payload = f"{header_encoded}.{payload_encoded}"
            signature = LocalJWT._create_signature(header_payload, secret)

            return f"{header_payload}.{signature}"

        except Exception as e:
            raise LocalJWTError(f"Failed to encode JWT: {str(e)}")

    @staticmethod
    def decode(token: str, secret: str, algorithms: list = None) -> Dict[str, Any]:
        """
        Decode and verify a JWT token using local implementation.

        Args:
            token: JWT token string
            secret: Secret key for verification
            algorithms: Allowed algorithms (only HS256 supported)

        Returns:
            Decoded payload

        Raises:
            LocalJWTError: If decoding or verification fails
        """
        if algorithms is None:
            algorithms = ["HS256"]

        if "HS256" not in algorithms:
            raise LocalJWTError("Only HS256 algorithm is supported")

        try:
            # Split token
            parts = token.split('.')
            if len(parts) != 3:
                raise LocalJWTError("Invalid token format")

            header_encoded, payload_encoded, signature_encoded = parts

            # Decode header
            header = json.loads(LocalJWT._base64url_decode(header_encoded).decode('utf-8'))

            # Verify algorithm
            if header.get('alg') != 'HS256':
                raise LocalJWTError(f"Unsupported algorithm: {header.get('alg')}")

            # Verify signature
            header_payload = f"{header_encoded}.{payload_encoded}"
            expected_signature = LocalJWT._create_signature(header_payload, secret)

            if not hmac.compare_digest(signature_encoded, expected_signature):
                raise LocalJWTError("Invalid signature")

            # Decode payload
            payload = json.loads(LocalJWT._base64url_decode(payload_encoded).decode('utf-8'))

            # Check expiration
            if 'exp' in payload:
                exp_timestamp = payload['exp']
                current_timestamp = time.time()
                if current_timestamp >= exp_timestamp:
                    raise LocalJWTError("Token has expired")

            return payload

        except LocalJWTError:
            raise
        except Exception as e:
            raise LocalJWTError(f"Failed to decode JWT: {str(e)}")


# =====================================================================================
# FALLBACK USER MANAGEMENT
# =====================================================================================

class LocalUser:
    """Simple user representation for local authentication."""

    def __init__(self, user_id: str, email: str = None, metadata: Dict[str, Any] = None):
        self.id = user_id
        self.email = email or f"user-{user_id}@local.auth"
        self.metadata = metadata or {}
        self.created_at = datetime.now(timezone.utc)

    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary representation."""
        return {
            "id": self.id,
            "email": self.email,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat()
        }


# In-memory user store for fallback (in production, use a proper database)
_local_users: Dict[str, LocalUser] = {}
_local_sessions: Dict[str, str] = {}  # token -> user_id mapping

def init_supabase():
    """Initialize Supabase client with proper error handling and fallback support."""
    global supabase

    if not SUPABASE_AVAILABLE:
        logger.warning("Supabase client not available - using local JWT fallback")
        return

    if not SUPABASE_URL or not SUPABASE_KEY:
        logger.warning(
            "Supabase configuration incomplete. Using local JWT fallback. "
            "Set SUPABASE_URL and SUPABASE_ANON_KEY environment variables for full functionality."
        )
        return

    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info("Supabase client initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize Supabase client: {e}. Using local JWT fallback.")
        supabase = None

def get_supabase() -> Optional[Any]:
    """Get Supabase client instance with proper validation and fallback support."""
    if not SUPABASE_AVAILABLE:
        logger.debug("Supabase not available, using local fallback")
        return None

    if supabase is None:
        init_supabase()

    return supabase  # Can be None, which triggers fallback


def is_supabase_available() -> bool:
    """Check if Supabase is available and properly configured."""
    return SUPABASE_AVAILABLE and bool(SUPABASE_URL and SUPABASE_KEY) and supabase is not None


# JWT configuration using centralized settings
security = HTTPBearer()


class TokenData:
    """Token data container for authentication information."""

    def __init__(self, username: Optional[str] = None, user_id: Optional[str] = None,
                 email: Optional[str] = None, is_local: bool = False):
        self.username = username
        self.user_id = user_id
        self.email = email
        self.is_local = is_local  # Flag to indicate local vs Supabase auth

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenData:
    """
    Verify JWT token with Supabase primary and local JWT fallback.

    This function first attempts to verify the token with Supabase. If Supabase
    is unavailable or the token verification fails, it falls back to local JWT verification.

    Args:
        credentials: HTTP Bearer token credentials

    Returns:
        TokenData: User information from token

    Raises:
        HTTPException: If token is invalid or verification fails
    """
    token = credentials.credentials

    # Try Supabase authentication first
    if is_supabase_available():
        try:
            supabase_client = get_supabase()
            user = supabase_client.auth.get_user(token)
            if user and user.user:
                return TokenData(
                    username=user.user.email,
                    user_id=user.user.id,
                    email=user.user.email,
                    is_local=False
                )
        except Exception as e:
            logger.warning(f"Supabase token verification failed: {e}. Trying local fallback.")

    # Fallback to local JWT verification
    try:
        payload = LocalJWT.decode(token, settings.JWT_SECRET, algorithms=["HS256"])

        user_id = payload.get("sub") or payload.get("user_id")
        if not user_id:
            raise LocalJWTError("Token missing user identifier")

        # Check if user exists in local store
        if user_id in _local_users:
            local_user = _local_users[user_id]
            return TokenData(
                username=local_user.email,
                user_id=user_id,
                email=local_user.email,
                is_local=True
            )
        else:
            # Create minimal user info from token
            return TokenData(
                username=payload.get("email", f"user-{user_id}"),
                user_id=user_id,
                email=payload.get("email"),
                is_local=True
            )

    except LocalJWTError as e:
        logger.error(f"Local JWT verification failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token with Supabase primary and local JWT fallback.

    This function creates a JWT token using the best available method:
    1. If jose library is available, use it
    2. Otherwise, use local JWT implementation

    Args:
        data: Token payload data
        expires_delta: Optional expiration time delta

    Returns:
        str: JWT token

    Raises:
        HTTPException: If token creation fails
    """
    try:
        to_encode = data.copy()

        # Set expiration
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)

        to_encode.update({
            "exp": int(expire.timestamp()),
            "iat": int(datetime.now(timezone.utc).timestamp()),
            "jti": str(uuid.uuid4())  # JWT ID for uniqueness
        })

        # Try jose library first if available
        if JWT_AVAILABLE:
            try:
                encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET, algorithm=settings.JWT_ALGORITHM)
                logger.debug("Token created using jose library")
                return encoded_jwt
            except Exception as e:
                logger.warning(f"Jose JWT creation failed: {e}. Using local fallback.")

        # Fallback to local JWT implementation
        encoded_jwt = LocalJWT.encode(to_encode, settings.JWT_SECRET, algorithm="HS256")
        logger.debug("Token created using local JWT implementation")
        return encoded_jwt

    except Exception as e:
        logger.error(f"Token creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create access token"
        )

# =====================================================================================
# LOCAL USER MANAGEMENT FUNCTIONS
# =====================================================================================

def create_local_user(user_id: str = None, email: str = None, metadata: Dict[str, Any] = None) -> LocalUser:
    """
    Create a local user for fallback authentication.

    Args:
        user_id: Optional user ID (generated if not provided)
        email: User email
        metadata: Additional user metadata

    Returns:
        LocalUser: Created user instance
    """
    if not user_id:
        user_id = str(uuid.uuid4())

    user = LocalUser(user_id=user_id, email=email, metadata=metadata)
    _local_users[user_id] = user

    logger.info(f"Created local user: {user_id}")
    return user


def get_local_user(user_id: str) -> Optional[LocalUser]:
    """Get local user by ID."""
    return _local_users.get(user_id)


def create_local_session(user_id: str, token: str) -> None:
    """Create a local session mapping."""
    _local_sessions[token] = user_id


def get_user_from_session(token: str) -> Optional[str]:
    """Get user ID from session token."""
    return _local_sessions.get(token)


# =====================================================================================
# MAIN AUTHENTICATION FUNCTIONS
# =====================================================================================

async def get_current_user(token_data: TokenData = Depends(verify_token)) -> TokenData:
    """
    Get current authenticated user with fallback support.

    Args:
        token_data: Verified token data

    Returns:
        TokenData: Current user information
    """
    return token_data


def hash_password(password: str) -> str:
    """
    Hash password for secure storage using SHA-256.

    Args:
        password: Plain text password

    Returns:
        str: Hashed password
    """
    # Add salt for better security
    salt = settings.JWT_SECRET[:16]  # Use part of JWT secret as salt
    salted_password = salt + password
    return hashlib.sha256(salted_password.encode()).hexdigest()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password against hash.

    Args:
        plain_password: Plain text password to verify
        hashed_password: Stored hashed password

    Returns:
        bool: True if password matches
    """
    return hash_password(plain_password) == hashed_password


def authenticate_local_user(email: str, password: str) -> Optional[LocalUser]:
    """
    Authenticate user with local credentials (fallback only).

    Args:
        email: User email
        password: User password

    Returns:
        LocalUser: Authenticated user or None
    """
    # This is a simple implementation for fallback
    # In production, you'd have proper user storage with hashed passwords
    for user in _local_users.values():
        if user.email == email:
            # For demo purposes, accept any password for local users
            # In production, implement proper password verification
            logger.info(f"Local user authenticated: {email}")
            return user
    return None


def get_auth_status() -> dict:
    """
    Get comprehensive authentication service status for health checks.

    Returns:
        dict: Authentication status information
    """
    return {
        "supabase_available": SUPABASE_AVAILABLE,
        "jwt_available": JWT_AVAILABLE,
        "local_jwt_available": True,  # Always available
        "supabase_configured": bool(SUPABASE_URL and SUPABASE_KEY),
        "supabase_initialized": supabase is not None,
        "supabase_ready": is_supabase_available(),
        "local_fallback_ready": True,
        "auth_ready": True,  # Always ready with fallback
        "local_users_count": len(_local_users),
        "active_sessions": len(_local_sessions),
        "jwt_algorithm": settings.JWT_ALGORITHM,
        "token_expire_minutes": settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
    }
