# Monitoring & Observability Stack Guide for AI Coding Agent

## Overview

This guide covers the Prometheus and Grafana monitoring stack implementation for comprehensive observability in the AI Coding Agent system, providing metrics collection, alerting, dashboards, and performance monitoring.

## Table of Contents

1. [Monitoring Architecture](#monitoring-architecture)
2. [Prometheus Setup & Configuration](#prometheus-setup--configuration)
3. [Grafana Integration](#grafana-integration)
4. [Custom Metrics Collection](#custom-metrics-collection)
5. [Alerting & Notification](#alerting--notification)
6. [Performance Dashboards](#performance-dashboards)
7. [Production Deployment](#production-deployment)

## Monitoring Architecture

### Complete Observability Stack

```
┌─────────────────────────────────────────────────────────────────┐
│                    Monitoring Dashboard Layer                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │ Grafana     │  │ Alertmanager│  │ Custom      │            │
│  │ Dashboards  │  │ Alerts      │  │ Monitoring  │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
├─────────────────────────────────────────────────────────────────┤
│                    Metrics Collection Layer                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │ Prometheus  │  │ Node        │  │ Custom      │            │
│  │ Server      │  │ Exporter    │  │ Exporters   │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
├─────────────────────────────────────────────────────────────────┤
│                    Application Services                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │ AI          │  │ Sequential  │  │ Validation  │            │
│  │ Orchestrator│  │ Agents      │  │ Pipeline    │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │ Redis       │  │ Supabase    │  │ Code-Server │            │
│  │ Cache       │  │ Database    │  │ Workspaces  │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
```

### Key Monitoring Components

- **Prometheus**: Time-series database and metrics collection
- **Grafana**: Visualization and dashboard platform
- **Alertmanager**: Alert routing and notification management
- **Node Exporter**: System metrics collection
- **Custom Exporters**: Application-specific metrics

## Prometheus Setup & Configuration

### Docker Compose Configuration

```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
    networks:
      - monitoring
      - ai-network

  grafana:
    image: grafana/grafana-enterprise:latest
    container_name: ai-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,redis-datasource
      - GF_SERVER_ROOT_URL=http://localhost:3000
      - GF_ANALYTICS_REPORTING_ENABLED=false
    networks:
      - monitoring
      - ai-network

  alertmanager:
    image: prom/alertmanager:latest
    container_name: ai-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - monitoring

  node-exporter:
    image: prom/node-exporter:latest
    container_name: ai-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:
  alertmanager_data:

networks:
  monitoring:
    driver: bridge
  ai-network:
    external: true
```

### Prometheus Configuration

```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'ai-coding-agent'
    cluster: 'development'

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter - System metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # AI Orchestrator metrics
  - job_name: 'ai-orchestrator'
    static_configs:
      - targets: ['ai-orchestrator:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'

  # Supabase PostgreSQL metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['supabase-db:5432']
    metrics_path: '/metrics'

  # Code-server metrics
  - job_name: 'code-server'
    static_configs:
      - targets: ['code-server:8080']
    metrics_path: '/metrics'

  # Docker daemon metrics
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    metrics_path: '/metrics'

  # Agent-specific metrics
  - job_name: 'agent-metrics'
    static_configs:
      - targets: ['ai-orchestrator:8001']
    metrics_path: '/agent-metrics'
    scrape_interval: 5s
```

### Alert Rules Configuration

```yaml
# monitoring/prometheus/rules/ai-agent-alerts.yml
groups:
  - name: ai-coding-agent-alerts
    rules:
      # High CPU usage alert
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

      # High memory usage alert
      - alert: HighMemoryUsage
        expr: (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes) * 100 < 20
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 80% on {{ $labels.instance }}"

      # AI Orchestrator down
      - alert: AIServiceDown
        expr: up{job="ai-orchestrator"} == 0
        for: 1m
        labels:
          severity: critical
          service: ai-orchestrator
        annotations:
          summary: "AI Orchestrator service is down"
          description: "AI Orchestrator has been down for more than 1 minute"

      # High task queue length
      - alert: HighTaskQueueLength
        expr: ai_agent_task_queue_length > 50
        for: 5m
        labels:
          severity: warning
          service: task-queue
        annotations:
          summary: "High task queue length"
          description: "Task queue length is {{ $value }}, indicating potential bottleneck"

      # Agent execution failures
      - alert: HighAgentFailureRate
        expr: rate(ai_agent_execution_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: agents
        annotations:
          summary: "High agent failure rate"
          description: "Agent failure rate is {{ $value | humanize }} failures per second"

      # Database connection issues
      - alert: DatabaseConnectionIssues
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "Database connection issues"
          description: "PostgreSQL database is unreachable"

      # Redis cache issues
      - alert: RedisCacheDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: cache
        annotations:
          summary: "Redis cache is down"
          description: "Redis cache service is unreachable"

  - name: performance-alerts
    rules:
      # Slow response times
      - alert: SlowAPIResponse
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: api
        annotations:
          summary: "Slow API response times"
          description: "95th percentile response time is {{ $value }}s"

      # High error rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: api
        annotations:
          summary: "High error rate"
          description: "Error rate is {{ $value | humanizePercentage }}"
```

## Grafana Integration

### Data Source Configuration

```yaml
# monitoring/grafana/provisioning/datasources/prometheus.yml
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true

  - name: AlertManager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    editable: true
```

### Dashboard Provisioning

```yaml
# monitoring/grafana/provisioning/dashboards/dashboard.yml
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
```

## Custom Metrics Collection

### FastAPI Metrics Implementation

```python
# src/monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from prometheus_client.core import CollectorRegistry
from typing import Dict, Any
import time
import asyncio
from functools import wraps

# Create custom registry
registry = CollectorRegistry()

# Define metrics
HTTP_REQUESTS_TOTAL = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status'],
    registry=registry
)

HTTP_REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint'],
    registry=registry
)

AGENT_EXECUTIONS_TOTAL = Counter(
    'ai_agent_executions_total',
    'Total agent executions',
    ['agent_type', 'status'],
    registry=registry
)

AGENT_EXECUTION_DURATION = Histogram(
    'ai_agent_execution_duration_seconds',
    'Agent execution duration in seconds',
    ['agent_type'],
    registry=registry
)

AGENT_TASK_QUEUE_LENGTH = Gauge(
    'ai_agent_task_queue_length',
    'Current task queue length',
    registry=registry
)

ACTIVE_AGENT_EXECUTIONS = Gauge(
    'ai_agent_active_executions',
    'Number of currently executing agents',
    ['agent_type'],
    registry=registry
)

LLM_API_CALLS_TOTAL = Counter(
    'llm_api_calls_total',
    'Total LLM API calls',
    ['provider', 'model', 'status'],
    registry=registry
)

LLM_RESPONSE_TIME = Histogram(
    'llm_response_time_seconds',
    'LLM API response time in seconds',
    ['provider', 'model'],
    registry=registry
)

VALIDATION_CHECKS_TOTAL = Counter(
    'validation_checks_total',
    'Total validation checks',
    ['stage', 'result'],
    registry=registry
)

MEMORY_USAGE_BYTES = Gauge(
    'process_memory_usage_bytes',
    'Process memory usage in bytes',
    registry=registry
)

class MetricsCollector:
    """Centralized metrics collection service."""

    def __init__(self, redis_client=None):
        self.redis_client = redis_client
        self.start_time = time.time()

    async def start_background_collection(self):
        """Start background metrics collection."""
        asyncio.create_task(self._collect_system_metrics())
        asyncio.create_task(self._collect_queue_metrics())

    async def _collect_system_metrics(self):
        """Collect system metrics periodically."""
        import psutil

        while True:
            try:
                # Memory usage
                process = psutil.Process()
                MEMORY_USAGE_BYTES.set(process.memory_info().rss)

                await asyncio.sleep(15)
            except Exception as e:
                print(f"Error collecting system metrics: {e}")
                await asyncio.sleep(15)

    async def _collect_queue_metrics(self):
        """Collect task queue metrics."""
        if not self.redis_client:
            return

        while True:
            try:
                # Task queue length
                queue_length = await self.redis_client.zcard("task_queue")
                AGENT_TASK_QUEUE_LENGTH.set(queue_length)

                await asyncio.sleep(5)
            except Exception as e:
                print(f"Error collecting queue metrics: {e}")
                await asyncio.sleep(5)

    def record_http_request(self, method: str, endpoint: str, status: int, duration: float):
        """Record HTTP request metrics."""
        HTTP_REQUESTS_TOTAL.labels(method=method, endpoint=endpoint, status=status).inc()
        HTTP_REQUEST_DURATION.labels(method=method, endpoint=endpoint).observe(duration)

    def record_agent_execution(self, agent_type: str, status: str, duration: float):
        """Record agent execution metrics."""
        AGENT_EXECUTIONS_TOTAL.labels(agent_type=agent_type, status=status).inc()
        AGENT_EXECUTION_DURATION.labels(agent_type=agent_type).observe(duration)

    def record_llm_call(self, provider: str, model: str, status: str, duration: float):
        """Record LLM API call metrics."""
        LLM_API_CALLS_TOTAL.labels(provider=provider, model=model, status=status).inc()
        LLM_RESPONSE_TIME.labels(provider=provider, model=model).observe(duration)

    def record_validation_check(self, stage: str, result: str):
        """Record validation check metrics."""
        VALIDATION_CHECKS_TOTAL.labels(stage=stage, result=result).inc()

    def increment_active_agents(self, agent_type: str):
        """Increment active agent counter."""
        ACTIVE_AGENT_EXECUTIONS.labels(agent_type=agent_type).inc()

    def decrement_active_agents(self, agent_type: str):
        """Decrement active agent counter."""
        ACTIVE_AGENT_EXECUTIONS.labels(agent_type=agent_type).dec()

# Global metrics collector
metrics_collector = MetricsCollector()

# Decorator for automatic HTTP metrics
def monitor_http_requests(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        status = 200

        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            status = 500
            raise
        finally:
            duration = time.time() - start_time
            # Extract method and endpoint from request context
            # This would need to be adapted based on your FastAPI setup
            metrics_collector.record_http_request("GET", "/api", status, duration)

    return wrapper

# Decorator for agent execution monitoring
def monitor_agent_execution(agent_type: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            metrics_collector.increment_active_agents(agent_type)

            try:
                result = await func(*args, **kwargs)
                metrics_collector.record_agent_execution(agent_type, "success", time.time() - start_time)
                return result
            except Exception as e:
                metrics_collector.record_agent_execution(agent_type, "failure", time.time() - start_time)
                raise
            finally:
                metrics_collector.decrement_active_agents(agent_type)

        return wrapper
    return decorator
```

### FastAPI Metrics Endpoint

```python
# src/api/metrics_endpoints.py
from fastapi import APIRouter, Response
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from ..monitoring.metrics import registry, metrics_collector

router = APIRouter()

@router.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint."""
    return Response(
        generate_latest(registry),
        media_type=CONTENT_TYPE_LATEST
    )

@router.get("/agent-metrics")
async def get_agent_metrics():
    """Custom agent metrics endpoint."""
    # Return agent-specific metrics in Prometheus format
    return Response(
        generate_latest(registry),
        media_type=CONTENT_TYPE_LATEST
    )

@router.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "uptime": time.time() - metrics_collector.start_time
    }
```

## Alerting & Notification

### Alertmanager Configuration

```yaml
# monitoring/alertmanager/alertmanager.yml
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        service: ai-orchestrator
      receiver: 'ai-service-alerts'
    - match:
        service: agents
      receiver: 'agent-alerts'

receivers:
  - name: 'default'
    webhook_configs:
      - url: 'http://ai-orchestrator:8000/api/v1/alerts/webhook'
        title: 'AI Coding Agent Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: 'CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ .Labels }}
          {{ end }}
    webhook_configs:
      - url: 'http://ai-orchestrator:8000/api/v1/alerts/critical'

  - name: 'ai-service-alerts'
    webhook_configs:
      - url: 'http://ai-orchestrator:8000/api/v1/alerts/service'

  - name: 'agent-alerts'
    webhook_configs:
      - url: 'http://ai-orchestrator:8000/api/v1/alerts/agents'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']
```

## Performance Dashboards

### AI Coding Agent Dashboard JSON

```json
{
  "dashboard": {
    "id": null,
    "title": "AI Coding Agent - System Overview",
    "tags": ["ai-coding-agent"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "System Resources",
        "type": "stat",
        "targets": [
          {
            "expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          },
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "Memory Usage %"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 70},
                {"color": "red", "value": 85}
              ]
            }
          }
        }
      },
      {
        "id": 2,
        "title": "Agent Execution Status",
        "type": "timeseries",
        "targets": [
          {
            "expr": "ai_agent_active_executions",
            "legendFormat": "{{agent_type}} Active"
          },
          {
            "expr": "rate(ai_agent_executions_total[5m])",
            "legendFormat": "{{agent_type}} Rate"
          }
        ]
      },
      {
        "id": 3,
        "title": "Task Queue Length",
        "type": "stat",
        "targets": [
          {
            "expr": "ai_agent_task_queue_length",
            "legendFormat": "Queue Length"
          }
        ]
      },
      {
        "id": 4,
        "title": "API Response Times",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m])) * 1000",
            "legendFormat": "50th percentile"
          },
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) * 1000",
            "legendFormat": "95th percentile"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "ms"
          }
        }
      }
    ]
  }
}
```

## Production Deployment

### Production Configuration

```bash
#!/bin/bash
# scripts/deploy-monitoring.sh

# Deploy monitoring stack
echo "Deploying AI Coding Agent monitoring stack..."

# Create monitoring directories
mkdir -p monitoring/{prometheus,grafana,alertmanager}
mkdir -p monitoring/grafana/{provisioning,dashboards}

# Set permissions for Grafana
sudo chown -R 472:472 monitoring/grafana

# Deploy with Docker Compose
docker-compose -f docker-compose.monitoring.yml up -d

# Wait for services to start
echo "Waiting for services to start..."
sleep 30

# Import dashboards
echo "Importing Grafana dashboards..."
curl -X POST \
  ************************************/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @monitoring/dashboards/ai-agent-overview.json

echo "Monitoring stack deployed successfully!"
echo "Access points:"
echo "- Grafana: http://localhost:3000 (admin/admin123)"
echo "- Prometheus: http://localhost:9090"
echo "- Alertmanager: http://localhost:9093"
```

### Maintenance Scripts

```bash
#!/bin/bash
# scripts/monitoring-maintenance.sh

# Backup Grafana dashboards
backup_grafana_dashboards() {
    echo "Backing up Grafana dashboards..."
    docker exec ai-grafana grafana-cli admin data-migration extract \
        --folder /var/lib/grafana/backups
}

# Clean old Prometheus data
cleanup_prometheus_data() {
    echo "Cleaning up old Prometheus data..."
    docker exec ai-prometheus promtool tsdb cleanup \
        --retention.time=30d /prometheus
}

# Restart monitoring services
restart_monitoring() {
    echo "Restarting monitoring services..."
    docker-compose -f docker-compose.monitoring.yml restart
}

# Main maintenance routine
case "$1" in
    backup)
        backup_grafana_dashboards
        ;;
    cleanup)
        cleanup_prometheus_data
        ;;
    restart)
        restart_monitoring
        ;;
    *)
        echo "Usage: $0 {backup|cleanup|restart}"
        exit 1
        ;;
esac
```

## Key Takeaways

1. **Comprehensive Monitoring**: Full observability stack with Prometheus, Grafana, and Alertmanager
2. **Custom Metrics**: Application-specific metrics for agent performance and task execution
3. **Real-time Dashboards**: Visual monitoring of system health and agent activities
4. **Automated Alerting**: Proactive notifications for system issues and performance degradation
5. **Scalable Architecture**: Designed for multi-service containerized environments
6. **Production Ready**: Includes backup, maintenance, and deployment automation
7. **Performance Tracking**: Detailed metrics for LLM API calls, validation stages, and response times
8. **Resource Monitoring**: CPU, memory, and disk usage tracking across all services
9. **Historical Data**: 30-day retention for trend analysis and capacity planning
10. **Integration Points**: Seamless integration with existing AI Coding Agent services

This monitoring stack provides the foundation for maintaining optimal performance and reliability in the AI Coding Agent system.