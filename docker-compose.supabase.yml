# Supabase Integration for AI Coding Agent
# This file adds Supabase services to replace PostgreSQL with enhanced auth and vector capabilities
# Usage: docker-compose -f docker-compose.yml -f docker-compose.supabase.yml up -d

version: '3.8'

services:
  # Supabase Database (replaces postgresql service)
  supabase-db:
    image: supabase/postgres:*********
    container_name: ai-coding-supabase-db
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ai_coding_agent}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_super_secret_password}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
      # Supabase specific settings
      POSTGRES_HOST_AUTH_METHOD: scram-sha-256
    volumes:
      - supabase_db_data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d:ro
      - ./supabase/config/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    ports:
      - "5433:5432"  # Different port to avoid conflict with existing PostgreSQL
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-ai_coding_agent}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    command: >
      postgres
        -c config_file=/etc/postgresql/postgresql.conf
        -c shared_preload_libraries='vector'
        -c max_connections=200
        -c shared_buffers=256MB
        -c effective_cache_size=1GB

  # Supabase Auth Service
  supabase-auth:
    image: supabase/gotrue:v2.132.3
    container_name: ai-coding-supabase-auth
    depends_on:
      supabase-db:
        condition: service_healthy
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: postgres://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-your_super_secret_password}@supabase-db:5432/${POSTGRES_DB:-ai_coding_agent}?search_path=auth
      GOTRUE_SITE_URL: ${SITE_URL:-http://localhost:3000}
      GOTRUE_URI_ALLOW_LIST: ${URI_ALLOW_LIST:-http://localhost:3000,http://localhost:8000,http://localhost:8080}
      GOTRUE_JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      GOTRUE_JWT_EXP: ${JWT_EXPIRY:-3600}
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_DISABLE_SIGNUP: ${DISABLE_SIGNUP:-false}
      GOTRUE_EXTERNAL_EMAIL_ENABLED: true
      GOTRUE_MAILER_AUTOCONFIRM: ${MAILER_AUTOCONFIRM:-true}
      API_EXTERNAL_URL: ${API_EXTERNAL_URL:-http://localhost:8000}
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9999/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Supabase REST API (PostgREST)
  supabase-rest:
    image: postgrest/postgrest:v12.0.1
    container_name: ai-coding-supabase-rest
    depends_on:
      supabase-db:
        condition: service_healthy
    environment:
      PGRST_DB_URI: postgres://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-your_super_secret_password}@supabase-db:5432/${POSTGRES_DB:-ai_coding_agent}
      PGRST_DB_SCHEMAS: ${PGRST_DB_SCHEMAS:-public,auth,storage}
      PGRST_DB_ANON_ROLE: ${PGRST_DB_ANON_ROLE:-anon}
      PGRST_JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_APP_SETTINGS_JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      PGRST_APP_SETTINGS_JWT_EXP: ${JWT_EXPIRY:-3600}
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Supabase Realtime (for WebSocket subscriptions)
  supabase-realtime:
    image: supabase/realtime:v2.25.50
    container_name: ai-coding-supabase-realtime
    depends_on:
      supabase-db:
        condition: service_healthy
    environment:
      PORT: 4000
      DB_HOST: supabase-db
      DB_PORT: 5432
      DB_USER: ${POSTGRES_USER:-postgres}
      DB_PASSWORD: ${POSTGRES_PASSWORD:-your_super_secret_password}
      DB_NAME: ${POSTGRES_DB:-ai_coding_agent}
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      FLY_ALLOC_ID: fly123
      FLY_APP_NAME: realtime
      SECRET_KEY_BASE: UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8etQoXz3gZv1/u2oq
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Kong Gateway (API Gateway and Load Balancer)
  supabase-kong:
    image: kong:2.8.1
    container_name: ai-coding-supabase-kong
    depends_on:
      - supabase-auth
      - supabase-rest
      - supabase-realtime
    ports:
      - "${KONG_HTTP_PORT:-8000}:8000"
      - "${KONG_HTTPS_PORT:-8443}:8443"
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth,rate-limiting
      KONG_NGINX_HTTP_KEEPALIVE_REQUESTS: 10000
      KONG_NGINX_HTTP_KEEPALIVE_TIMEOUT: 60s
      KONG_NGINX_HTTP_KEEPALIVE: 32
    volumes:
      - ./supabase/config/kong.yml:/var/lib/kong/kong.yml:ro
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Updated AI Orchestrator to use Supabase
  ai-orchestrator:
    build:
      context: ./containers/ai-orchestrator
      dockerfile: Dockerfile
    container_name: ai-coding-orchestrator-supabase
    depends_on:
      supabase-kong:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Supabase Configuration
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_KEY: ${SERVICE_ROLE_KEY}
      SUPABASE_DB_URL: postgres://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-your_super_secret_password}@supabase-db:5432/${POSTGRES_DB:-ai_coding_agent}

      # Legacy database URL for backward compatibility during migration
      DATABASE_URL: postgres://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-your_super_secret_password}@supabase-db:5432/${POSTGRES_DB:-ai_coding_agent}

      # Other services
      REDIS_URL: redis://redis:6379/0
      OLLAMA_URL: ${OLLAMA_BASE_URL:-http://host.docker.internal:11434}

      # AI Service API Keys
      OPENROUTER_API_KEY: ${OPENROUTER_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}

      # Authentication
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      JWT_ALGORITHM: HS256
      JWT_ACCESS_TOKEN_EXPIRE_MINUTES: 30

      # Feature flags
      USE_SUPABASE: true
      ENVIRONMENT: ${ENVIRONMENT:-development}
      DEBUG: ${DEBUG:-true}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "8001:8000"  # Different port to avoid conflict during migration
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5).raise_for_status()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# Additional volumes for Supabase
volumes:
  supabase_db_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${SUPABASE_DATA_PATH:-./volumes/supabase-data}

# Use existing network
networks:
  ai-coding-agent-network:
    external: true