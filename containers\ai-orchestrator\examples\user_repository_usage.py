# Project: AI Coding Agent
# Purpose: UserRepository usage examples
# Author: AI Coding Agent Team

"""
UserRepository Usage Examples.

This file demonstrates how to use the UserRepository class for common
operations in the AI Coding Agent system, particularly showing the
integration between Supabase Auth and our custom user management.
"""

from datetime import datetime
from uuid import uuid4
from typing import Optional

# FastAPI and SQLAlchemy imports
from fastapi import FastAPI, Depends, HTTPException, status
from sqlalchemy.orm import Session

# Internal imports
from ..models.database import get_db
from ..repository.user_repository import UserRepository, get_user_repository
from ..schemas.user_schemas import SupabaseUser, UserProfileUpdateSchema, UserResponse

# Example FastAPI app
app = FastAPI(title="UserRepository Example")


# ==================================================================================
# EXAMPLE 1: SUPABASE AUTH INTEGRATION
# ==================================================================================

@app.post("/auth/supabase-login", response_model=UserResponse)
async def handle_supabase_login(
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
):
    """
    Example endpoint showing how to handle Supabase authentication.

    In a real application, this would:
    1. Verify JWT token from Supabase
    2. Extract user data from token
    3. Get or create user in local database
    4. Return user profile for application use
    """

    # Example Supabase user data (normally from JWT token)
    supabase_user_data = SupabaseUser(
        id=uuid4(),
        email="<EMAIL>",
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        user_metadata={
            "full_name": "John Doe",
            "username": "johndoe"
        },
        app_metadata={}
    )

    # Use repository to get or create user
    result = user_repo.get_or_create_user_from_supabase(db, supabase_user_data)

    return result.user


# ==================================================================================
# EXAMPLE 2: USER PROFILE MANAGEMENT
# ==================================================================================

@app.put("/users/{user_id}/profile", response_model=UserResponse)
async def update_user_profile(
    user_id: int,
    profile_update: UserProfileUpdateSchema,
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
):
    """
    Example endpoint showing how to update user profiles.
    """

    # Get existing user
    user = user_repo.get_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Update profile using repository
    updated_user = user_repo.update_profile(db, user, profile_update)

    return UserResponse.from_user_model(updated_user)


# ==================================================================================
# EXAMPLE 3: USER LOOKUP OPERATIONS
# ==================================================================================

@app.get("/users/by-email/{email}", response_model=Optional[UserResponse])
async def get_user_by_email(
    email: str,
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
):
    """Example of looking up users by email."""

    user = user_repo.get_by_email(db, email)
    if user:
        return UserResponse.from_user_model(user)
    return None


@app.get("/users/{user_id}", response_model=UserResponse)
async def get_user_by_id(
    user_id: int,
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
):
    """Example of getting user by ID."""

    user = user_repo.get_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return UserResponse.from_user_model(user)


# ==================================================================================
# EXAMPLE 4: PAGINATION AND SEARCH
# ==================================================================================

@app.get("/users")
async def list_users(
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
):
    """Example of paginated user listing with search."""

    return user_repo.get_users_paginated(
        db=db,
        page=page,
        page_size=page_size,
        search=search,
        is_active=is_active
    )


# ==================================================================================
# EXAMPLE 5: PROJECT RELATIONSHIPS (PLACEHOLDER)
# ==================================================================================

@app.get("/users/{user_id}/projects")
async def get_user_projects(
    user_id: int,
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
):
    """Example of getting user's projects."""

    # Verify user exists
    user = user_repo.get_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Get user's projects
    projects = user_repo.get_user_projects(db, user_id)

    return {
        "user_id": user_id,
        "username": user.username,
        "projects": projects
    }


# ==================================================================================
# EXAMPLE 6: ERROR HANDLING
# ==================================================================================

from ..repository.user_repository import (
    UserRepositoryError,
    UserNotFoundError,
    UserAlreadyExistsError
)

@app.exception_handler(UserRepositoryError)
async def handle_user_repository_error(request, exc: UserRepositoryError):
    """Example of handling repository errors."""
    return {
        "error": "User Repository Error",
        "detail": str(exc),
        "type": type(exc).__name__
    }


# ==================================================================================
# EXAMPLE STANDALONE USAGE
# ==================================================================================

def example_standalone_usage():
    """
    Example of using UserRepository outside of FastAPI context.
    """
    from ..models.database import SessionLocal

    # Create database session
    db = SessionLocal()
    user_repo = UserRepository()

    try:
        # Example: Create user from Supabase data
        supabase_user = SupabaseUser(
            id=uuid4(),
            email="<EMAIL>",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            user_metadata={"full_name": "Jane Smith"},
            app_metadata={}
        )

        # Get or create user
        result = user_repo.get_or_create_user_from_supabase(db, supabase_user)
        print(f"User: {result.user.username} (Created: {result.created_from_supabase})")

        # Update user profile
        profile_update = UserProfileUpdateSchema(
            full_name="Jane M. Smith",
            preferences={"theme": "dark", "language": "en"}
        )

        updated_user = user_repo.update_profile(db, result.user, profile_update)
        print(f"Updated user: {updated_user.full_name}")

        # Search users
        users_result = user_repo.get_users_paginated(db, search="jane")
        print(f"Found {users_result.total} users matching 'jane'")

    except UserRepositoryError as e:
        print(f"Repository error: {e}")

    finally:
        db.close()


if __name__ == "__main__":
    # Run standalone example
    example_standalone_usage()