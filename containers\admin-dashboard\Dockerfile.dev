# syntax=docker/dockerfile:1
# Development Dockerfile for Next.js application
# Optimized for development with proper symlink handling

FROM node:20-alpine

# Install necessary packages for development
RUN apk add --no-cache \
    libc6-compat \
    curl \
    bash \
    gosu

# Node user already exists in base image, just ensure proper permissions

# Set working directory and fix permissions
WORKDIR /home/<USER>/app
RUN chown -R node:node /home/<USER>/app

# Copy package files first for better caching
COPY --chown=node:node package*.json ./
COPY --chown=node:node .npmrc* ./

# Keep as root user for entrypoint script to work
# USER node - removed so entrypoint can run chown as root

# Install dependencies with explicit symlink creation
# Use npm install instead of npm ci for development flexibility
RUN npm install --legacy-peer-deps && \
    # Verify symlinks were created
    ls -la node_modules/.bin/ && \
    # Ensure next executable exists and is executable
    test -x node_modules/.bin/next

# Copy source code
COPY --chown=node:node . .

# Copy and set up entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Expose development port
EXPOSE 3000

# Set development environment
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Set entrypoint script
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]

# Development command with symlink verification
CMD ["sh", "-c", "ls -la node_modules/.bin/ && npm run dev"]
