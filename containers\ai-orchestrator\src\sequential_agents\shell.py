"""
Sequential Agents - ShellAgent (Enhanced Safe Command Executor)

Conforms to the simple BaseAgent interface. Validates commands against an allowlist
and performs comprehensive argument security validation to prevent command injection
and other security vulnerabilities.
"""
from __future__ import annotations

import asyncio
import re
import logging
from pathlib import Path
from typing import Any, Dict, List, Set

from .base import BaseAgent

logger = logging.getLogger(__name__)


class ShellAgent(BaseAgent):
    """
    Execute whitelisted shell commands safely with comprehensive security validation.

    This agent provides secure command execution with multiple layers of protection:
    1. Executable allowlist validation
    2. Dangerous argument pattern detection
    3. Command injection prevention
    4. Path traversal protection
    5. Resource limit enforcement
    """

    def __init__(self) -> None:
        super().__init__()

        # Define allowed executables (expandable as needed)
        self.allowed_executables: Set[str] = {
            "git", "pip", "npm", "yarn", "pnpm", "alembic", "pytest",
            "python", "python3", "node", "docker", "docker-compose"
        }

        # Define dangerous argument patterns that could enable command injection
        self.dangerous_patterns: List[str] = [
            # Command chaining and piping
            "&&", "||", ";", "|",
            # Redirection operators
            ">", ">>", "<", "<<",
            # Background execution
            "&",
            # Command substitution
            "`", "$(", "${",
            # Dangerous commands (even as arguments)
            "rm -rf", "rm -r", "rm -f",
            "sudo", "su", "chmod +x",
            # Network operations that could be dangerous
            "curl", "wget", "nc", "netcat",
            # Process manipulation
            "kill", "killall", "pkill",
            # System modification
            "mount", "umount", "fdisk",
            # File operations that could be dangerous
            "dd", "shred",
        ]

        # Define dangerous argument regex patterns
        self.dangerous_regex_patterns: List[re.Pattern] = [
            re.compile(r'[;&|`$]'),  # Shell metacharacters
            re.compile(r'\.\.\/'),   # Directory traversal
            re.compile(r'\/etc\/'),  # System directory access
            re.compile(r'\/root\/'), # Root directory access
            re.compile(r'\/proc\/'), # Process filesystem access
            re.compile(r'\/sys\/'),  # System filesystem access
            re.compile(r'--eval'),   # Code evaluation flags
            re.compile(r'-e\s+'),    # Execute flags
            re.compile(r'--exec'),   # Execute flags
        ]

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and execute a shell command with comprehensive security checks.

        Expected task_input:
        - command: List[str] (e.g., ["pip", "install", "requests"])
        - cwd: Optional[str] working directory (repo-relative or absolute)

        Returns:
            dict with keys: returncode, stdout, stderr (and error on security failure)
        """
        # Validate command structure
        command = task_input.get("command")
        if not isinstance(command, list) or not command:
            return {
                "error": "Security violation: 'command' must be a non-empty list",
                "security_violation": True,
                "violation_type": "invalid_command_structure"
            }

        if not all(isinstance(x, str) for x in command):
            return {
                "error": "Security violation: All command elements must be strings",
                "security_violation": True,
                "violation_type": "invalid_command_types"
            }

        # Validate executable against allowlist
        exe = command[0].lower().strip()
        if not exe:
            return {
                "error": "Security violation: Empty executable name",
                "security_violation": True,
                "violation_type": "empty_executable"
            }

        if exe not in self.allowed_executables:
            logger.warning(f"ShellAgent: Blocked execution of disallowed executable: {exe}")
            return {
                "error": f"Security violation: Executable '{exe}' is not in the allowlist",
                "security_violation": True,
                "violation_type": "disallowed_executable",
                "blocked_executable": exe,
                "allowed_executables": sorted(list(self.allowed_executables))
            }

        # Perform comprehensive argument security validation
        security_result = self._validate_command_security(command)
        if security_result:
            logger.warning(f"ShellAgent: Blocked command due to security violation: {security_result}")
            return security_result

        # Validate and resolve working directory
        cwd_result = self._resolve_working_directory(task_input.get("cwd"))
        if "error" in cwd_result:
            return cwd_result

        cwd_path = cwd_result["cwd_path"]

        # Log the command execution for security auditing
        logger.info(f"ShellAgent: Executing command: {' '.join(command)} in {cwd_path}")

        try:
            # Execute the command with security constraints
            proc = await asyncio.create_subprocess_exec(
                *command,
                cwd=str(cwd_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                # Security: Limit environment variables
                env={
                    "PATH": "/usr/local/bin:/usr/bin:/bin",
                    "HOME": "/tmp",
                    "USER": "agent",
                    "SHELL": "/bin/sh"
                }
            )

            # Wait for command completion with timeout
            try:
                out_b, err_b = await asyncio.wait_for(proc.communicate(), timeout=300)  # 5 minute timeout
            except asyncio.TimeoutError:
                proc.kill()
                await proc.wait()
                return {
                    "error": "Command execution timed out after 5 minutes",
                    "returncode": -1,
                    "stdout": "",
                    "stderr": "Execution timeout"
                }

            result = {
                "returncode": proc.returncode,
                "stdout": out_b.decode('utf-8', errors='replace'),
                "stderr": err_b.decode('utf-8', errors='replace'),
                "command": command,
                "cwd": str(cwd_path)
            }

            # Log execution result for auditing
            if proc.returncode == 0:
                logger.info(f"ShellAgent: Command completed successfully: {' '.join(command)}")
            else:
                logger.warning(f"ShellAgent: Command failed with code {proc.returncode}: {' '.join(command)}")

            return result

        except Exception as e:
            logger.error(f"ShellAgent: Command execution failed: {e}")
            return {
                "error": f"Command execution failed: {str(e)}",
                "returncode": -1,
                "stdout": "",
                "stderr": str(e)
            }

    def _validate_command_security(self, command: List[str]) -> Dict[str, Any]:
        """
        Perform comprehensive security validation on command arguments.

        Args:
            command: List of command and arguments

        Returns:
            Dict containing error information if security violation found, empty dict if safe
        """
        # Join all arguments for pattern matching
        full_command = ' '.join(command)

        # Check for dangerous literal patterns
        for pattern in self.dangerous_patterns:
            if pattern in full_command:
                return {
                    "error": f"Security violation: Dangerous pattern '{pattern}' detected in command",
                    "security_violation": True,
                    "violation_type": "dangerous_pattern",
                    "blocked_pattern": pattern,
                    "command_preview": full_command[:100] + "..." if len(full_command) > 100 else full_command
                }

        # Check for dangerous regex patterns
        for pattern in self.dangerous_regex_patterns:
            if pattern.search(full_command):
                return {
                    "error": f"Security violation: Dangerous pattern detected matching regex: {pattern.pattern}",
                    "security_violation": True,
                    "violation_type": "dangerous_regex_pattern",
                    "blocked_pattern": pattern.pattern,
                    "command_preview": full_command[:100] + "..." if len(full_command) > 100 else full_command
                }

        # Check individual arguments for security issues
        for i, arg in enumerate(command[1:], 1):  # Skip executable (index 0)
            # Check for suspicious file paths
            if self._is_suspicious_path(arg):
                return {
                    "error": f"Security violation: Suspicious file path detected: {arg}",
                    "security_violation": True,
                    "violation_type": "suspicious_path",
                    "blocked_argument": arg,
                    "argument_index": i
                }

            # Check for encoded or obfuscated content
            if self._is_potentially_encoded(arg):
                return {
                    "error": f"Security violation: Potentially encoded or obfuscated argument: {arg[:50]}...",
                    "security_violation": True,
                    "violation_type": "encoded_content",
                    "argument_index": i
                }

        # All checks passed
        return {}

    def _is_suspicious_path(self, path: str) -> bool:
        """Check if a path contains suspicious patterns."""
        suspicious_paths = [
            "/etc/", "/root/", "/proc/", "/sys/", "/dev/",
            "../", "./", "~", "/tmp/../", "/var/log/",
            "/usr/bin/", "/bin/", "/sbin/", "/usr/sbin/"
        ]

        path_lower = path.lower()
        return any(suspicious in path_lower for suspicious in suspicious_paths)

    def _is_potentially_encoded(self, arg: str) -> bool:
        """Check if an argument might contain encoded or obfuscated content."""
        # Check for base64-like patterns (long strings of alphanumeric + / =)
        if len(arg) > 50 and re.match(r'^[A-Za-z0-9+/=]+$', arg):
            return True

        # Check for hex-encoded patterns
        if len(arg) > 20 and re.match(r'^[0-9a-fA-F]+$', arg):
            return True

        # Check for URL-encoded patterns
        if '%' in arg and len(re.findall(r'%[0-9a-fA-F]{2}', arg)) > 3:
            return True

        return False

    def _resolve_working_directory(self, cwd_input: Any) -> Dict[str, Any]:
        """
        Safely resolve and validate the working directory.

        Args:
            cwd_input: Working directory input from task

        Returns:
            Dict with cwd_path or error information
        """
        # Determine repository root
        here = Path(__file__).resolve()
        repo_root = here.parents[4]  # Adjust based on actual file structure

        if isinstance(cwd_input, str) and cwd_input.strip():
            cwd_input = cwd_input.strip()

            # Security: Block absolute paths outside repo
            if cwd_input.startswith('/') and not cwd_input.startswith(str(repo_root)):
                return {
                    "error": f"Security violation: Absolute path outside repository not allowed: {cwd_input}",
                    "security_violation": True,
                    "violation_type": "path_outside_repo"
                }

            # Security: Block directory traversal
            if '../' in cwd_input or '..' in cwd_input:
                return {
                    "error": f"Security violation: Directory traversal not allowed: {cwd_input}",
                    "security_violation": True,
                    "violation_type": "directory_traversal"
                }

            cwd_path = Path(cwd_input)
            if not cwd_path.is_absolute():
                cwd_path = repo_root / cwd_path
        else:
            cwd_path = repo_root

        # Ensure the path exists and is within the repository
        try:
            cwd_path = cwd_path.resolve()
            if not str(cwd_path).startswith(str(repo_root.resolve())):
                return {
                    "error": f"Security violation: Working directory outside repository: {cwd_path}",
                    "security_violation": True,
                    "violation_type": "path_outside_repo"
                }
        except (OSError, ValueError) as e:
            return {
                "error": f"Invalid working directory: {e}",
                "security_violation": False,
                "violation_type": "invalid_path"
            }

        return {"cwd_path": cwd_path}