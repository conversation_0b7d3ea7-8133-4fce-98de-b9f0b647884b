#!/bin/bash
# <PERSON><PERSON>t to install VS Code extensions in code-server

set -e

echo "Installing VS Code extensions..."

# Check if extensions requirements file exists
if [ ! -f "/home/<USER>/extensions/requirements.txt" ]; then
    echo "No extensions requirements file found, skipping extension installation"
    exit 0
fi

# Read extensions from requirements.txt and install them
while IFS= read -r extension || [[ -n "$extension" ]]; do
    # Skip comments and empty lines
    if [[ $extension =~ ^#.*$ ]] || [[ -z "$extension" ]]; then
        continue
    fi

    echo "Installing extension: $extension"
    # Use explicit path to avoid shell interpretation issues
    /usr/lib/code-server/bin/code-server --install-extension "$extension" || echo "Failed to install $extension"
done < /home/<USER>/extensions/requirements.txt

echo "Extension installation completed!"
