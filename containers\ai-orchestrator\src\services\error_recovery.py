# Project: AI Coding Agent
# Purpose: Intelligent error recovery system with automatic detection and fixing capabilities

import asyncio
import json
import logging
import re
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

from ..models.validation_models import (
    Task, RecoveryResult, ErrorType, ValidationResult, TaskResult, RetryPolicy
)
from ..core.circuit_breaker import circuit_breaker, CircuitBreakerConfig
from ..core.retry_strategies import with_retry, RetryStrategy, retry_manager
# Note: LLM service import should be handled at runtime to avoid circular imports
# from ..utils.llm_service import LLMService


class ErrorRecoverySystem:
    """
    Intelligent error recovery system that automatically detects and fixes common issues.

    Provides recovery strategies for:
    - Syntax errors with LLM-assisted fixing
    - Import/dependency errors with automatic resolution
    - Configuration errors with environment setup
    - Network errors with retry mechanisms
    - Database errors with connection recovery

    Enhanced with circuit breakers and advanced retry strategies.
    """

    def __init__(self, project_root: str = "/workspace"):
        self.project_root = Path(project_root)
        self._llm_service = None  # Lazy-loaded to avoid circular imports
        self.logger = logging.getLogger("error_recovery")

        # Recovery statistics
        self._recovery_attempts = 0
        self._successful_recoveries = 0
        self._recovery_history: List[Dict[str, Any]] = []

        # Recovery configuration
        self.max_recovery_attempts = 3
        self.recovery_timeout = 300  # 5 minutes

        # Initialize retry policies for different recovery types
        self._setup_retry_policies()

        # Initialize circuit breakers for external services
        self._setup_circuit_breakers()

        self.logger.info("Enhanced Error Recovery System initialized")

    def _setup_retry_policies(self):
        """Set up specialized retry policies for different recovery operations"""

        # LLM-based recovery policy
        retry_manager.create_policy(
            "llm_recovery",
            max_attempts=3,
            base_delay=2.0,
            max_delay=10.0,
            backoff_multiplier=1.5,
            retry_on_errors=["ConnectionError", "TimeoutError", "APIError"]
        )

        # Package installation policy
        retry_manager.create_policy(
            "package_install",
            max_attempts=5,
            base_delay=1.0,
            max_delay=30.0,
            backoff_multiplier=2.0,
            retry_on_errors=["ConnectionError", "TimeoutError", "HTTPError"]
        )

        # File system operations policy
        retry_manager.create_policy(
            "filesystem",
            max_attempts=3,
            base_delay=0.5,
            max_delay=5.0,
            backoff_multiplier=2.0,
            retry_on_errors=["PermissionError", "FileNotFoundError"]
        )

        # Network operations policy
        retry_manager.create_policy(
            "network_recovery",
            max_attempts=10,
            base_delay=1.0,
            max_delay=60.0,
            backoff_multiplier=1.5,
            retry_on_errors=["ConnectionError", "TimeoutError", "NetworkError"]
        )

    def _setup_circuit_breakers(self):
        """Set up circuit breakers for external services"""
        from ..core.circuit_breaker import circuit_breaker_registry, CircuitBreakerConfig

        # LLM service circuit breaker
        circuit_breaker_registry.create_circuit_breaker(
            "llm_service",
            CircuitBreakerConfig(
                failure_threshold=3,
                recovery_timeout_seconds=120,
                success_threshold=2
            )
        )

        # Package repository circuit breaker
        circuit_breaker_registry.create_circuit_breaker(
            "package_repository",
            CircuitBreakerConfig(
                failure_threshold=5,
                recovery_timeout_seconds=60,
                success_threshold=3
            )
        )

        # Network connectivity circuit breaker
        circuit_breaker_registry.create_circuit_breaker(
            "network_connectivity",
            CircuitBreakerConfig(
                failure_threshold=5,
                recovery_timeout_seconds=30,
                success_threshold=2
            )
        )

    @property
    def llm_service(self):
        """Lazy-load LLM service to avoid circular imports."""
        if self._llm_service is None:
            try:
                from ..utils.llm_service import LLMService
                self._llm_service = LLMService()
            except ImportError:
                self.logger.warning("LLM service not available for error recovery")
                self._llm_service = None
        return self._llm_service

    async def handle_task_failure(self, task: Task, error: Exception) -> RecoveryResult:
        """
        Main entry point for handling task failures with automatic recovery attempts.

        Args:
            task: The failed task
            error: The exception that caused the failure

        Returns:
            RecoveryResult: Result of recovery attempt with recommendations
        """
        self.logger.error(f"Handling task failure: {task.title} - Error: {str(error)}")

        self._recovery_attempts += 1
        recovery_start = time.time()

        try:
            # Classify the error type
            error_type = self._classify_error(str(error))
            self.logger.info(f"Classified error as: {error_type}")

            # Attempt appropriate recovery strategy
            recovery_result = await self._attempt_recovery_by_type(task, error, error_type)

            # Record recovery attempt
            recovery_time = time.time() - recovery_start
            self._record_recovery_attempt(task, error, error_type, recovery_result, recovery_time)

            if recovery_result.success:
                self._successful_recoveries += 1
                self.logger.info(f"Recovery successful for task: {task.title}")
            else:
                self.logger.warning(f"Recovery failed for task: {task.title}")

            return recovery_result

        except Exception as recovery_error:
            self.logger.error(f"Recovery system error: {str(recovery_error)}")
            return RecoveryResult(
                success=False,
                actions_taken=f"Recovery system encountered error: {str(recovery_error)}",
                retry_recommended=False,
                recovery_suggestions=["Manual intervention required", "Check system logs"]
            )

    def _classify_error(self, error_message: str) -> ErrorType:
        """
        Classify error type based on error message patterns.
        Enhanced classification with more specific pattern matching.
        """
        error_lower = error_message.lower()

        # Syntax error patterns
        syntax_patterns = [
            r'syntax error', r'invalid syntax', r'parsing error', r'unexpected token',
            r'indentationerror', r'tabError', r'missing \)', r'missing }', r'missing ]'
        ]
        if any(re.search(pattern, error_lower) for pattern in syntax_patterns):
            return ErrorType.SYNTAX_ERROR

        # Import error patterns
        import_patterns = [
            r'modulenotfounderror', r'importerror', r'cannot import', r'no module named',
            r'import.*failed', r'missing dependency', r'package not found'
        ]
        if any(re.search(pattern, error_lower) for pattern in import_patterns):
            return ErrorType.IMPORT_ERROR

        # Configuration error patterns
        config_patterns = [
            r'configuration', r'config.*error', r'missing.*config', r'invalid.*setting',
            r'environment.*variable', r'settings.*not.*found', r'missing.*key'
        ]
        if any(re.search(pattern, error_lower) for pattern in config_patterns):
            return ErrorType.CONFIGURATION_ERROR

        # Network error patterns
        network_patterns = [
            r'connection.*refused', r'timeout', r'network.*error', r'dns.*resolution',
            r'unreachable', r'connection.*reset', r'ssl.*error', r'certificate.*error'
        ]
        if any(re.search(pattern, error_lower) for pattern in network_patterns):
            return ErrorType.NETWORK_ERROR

        # Database error patterns
        database_patterns = [
            r'database.*error', r'sql.*error', r'connection.*database', r'table.*not.*exist',
            r'column.*not.*exist', r'constraint.*violation', r'deadlock', r'transaction.*failed'
        ]
        if any(re.search(pattern, error_lower) for pattern in database_patterns):
            return ErrorType.DATABASE_ERROR

        # Permission error patterns
        permission_patterns = [
            r'permission.*denied', r'access.*denied', r'forbidden', r'unauthorized',
            r'authentication.*failed', r'not.*allowed', r'insufficient.*privileges'
        ]
        if any(re.search(pattern, error_lower) for pattern in permission_patterns):
            return ErrorType.PERMISSION_ERROR

        # Dependency error patterns
        dependency_patterns = [
            r'dependency.*error', r'version.*conflict', r'incompatible.*version',
            r'missing.*requirement', r'package.*conflict', r'requirement.*not.*satisfied'
        ]
        if any(re.search(pattern, error_lower) for pattern in dependency_patterns):
            return ErrorType.DEPENDENCY_ERROR

        return ErrorType.UNKNOWN_ERROR

    async def _attempt_recovery_by_type(self, task: Task, error: Exception, error_type: ErrorType) -> RecoveryResult:
        """Route error to appropriate recovery handler based on type"""
        recovery_handlers = {
            ErrorType.SYNTAX_ERROR: self._recover_syntax_error,
            ErrorType.IMPORT_ERROR: self._recover_import_error,
            ErrorType.CONFIGURATION_ERROR: self._recover_configuration_error,
            ErrorType.DEPENDENCY_ERROR: self._recover_dependency_error,
            ErrorType.NETWORK_ERROR: self._recover_network_error,
            ErrorType.DATABASE_ERROR: self._recover_database_error,
            ErrorType.PERMISSION_ERROR: self._recover_permission_error,
            ErrorType.VALIDATION_ERROR: self._recover_validation_error
        }

        handler = recovery_handlers.get(error_type, self._recover_unknown_error)
        return await handler(task, str(error))

    @with_retry("llm_recovery", RetryStrategy.EXPONENTIAL_BACKOFF)
    async def _recover_syntax_error(self, task: Task, error: str) -> RecoveryResult:
        """Automatically fix common syntax errors using LLM assistance with retry and circuit breaker"""
        self.logger.info("Attempting syntax error recovery with enhanced error handling")

        try:
            # Parse error details from the error message
            error_details = self._parse_syntax_error(error)

            if not error_details.get('file_path'):
                return RecoveryResult(
                    success=False,
                    actions_taken="Could not identify file with syntax error",
                    retry_recommended=False,
                    recovery_suggestions=["Review error message manually", "Check file paths"]
                )

            file_path = Path(error_details['file_path'])

            # Read the problematic file with retry
            original_code = await self._read_file_with_retry(file_path)
            if not original_code:
                return RecoveryResult(
                    success=False,
                    actions_taken=f"Could not read file {file_path}",
                    retry_recommended=False
                )

            # Use LLM to fix the syntax error (with circuit breaker protection)
            corrected_code = await self._fix_syntax_with_llm(error_details, original_code)
            if not corrected_code:
                return RecoveryResult(
                    success=False,
                    actions_taken="LLM service unavailable for syntax fix",
                    retry_recommended=True,
                    recovery_suggestions=["Try again later", "Manual fix required"]
                )

            # Apply the fix with backup
            success = await self._apply_code_fix_with_backup(file_path, original_code, corrected_code)

            if success:
                # Validate the fix
                if await self._validate_syntax_fix(file_path):
                    return RecoveryResult(
                        success=True,
                        actions_taken=f"Fixed syntax error in {file_path} using LLM assistance",
                        retry_recommended=True,
                        recovery_suggestions=[
                            f"Backup created at {file_path}.backup",
                            "Re-run the task to validate fix"
                        ]
                    )
                else:
                    # Restore from backup if fix didn't work
                    await self._restore_from_backup(file_path)
                    return RecoveryResult(
                        success=False,
                        actions_taken=f"LLM fix for {file_path} was invalid, restored original",
                        retry_recommended=False,
                        recovery_suggestions=["Manual code review required", "Check syntax error details"]
                    )
            else:
                return RecoveryResult(
                    success=False,
                    actions_taken=f"Could not apply fix to {file_path}",
                    retry_recommended=False
                )

        except Exception as e:
            self.logger.error(f"Syntax error recovery failed: {str(e)}")
            return RecoveryResult(
                success=False,
                actions_taken=f"Syntax error recovery encountered error: {str(e)}",
                retry_recommended=False
            )

    @with_retry("package_install", RetryStrategy.JITTERED_EXPONENTIAL)
    async def _recover_import_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from import/dependency errors with enhanced retry and circuit breaker protection"""
        self.logger.info("Attempting enhanced import error recovery")

        try:
            # Extract missing module name from error
            missing_modules = self._extract_missing_modules(error)

            if not missing_modules:
                return RecoveryResult(
                    success=False,
                    actions_taken="Could not identify missing module from error message",
                    retry_recommended=False,
                    recovery_suggestions=["Review import statements", "Check package names"]
                )

            installation_results = []

            for module in missing_modules:
                # Attempt to install the missing module with enhanced retry
                install_result = await self._install_python_package_enhanced(module)
                installation_results.append((module, install_result))

            successful_installs = [module for module, success in installation_results if success]
            failed_installs = [module for module, success in installation_results if not success]

            if successful_installs:
                return RecoveryResult(
                    success=len(failed_installs) == 0,
                    actions_taken=f"Installed packages: {', '.join(successful_installs)}",
                    retry_recommended=True,
                    recovery_suggestions=[
                        "Re-run the task to validate imports",
                        f"Failed installs: {', '.join(failed_installs)}" if failed_installs else "All packages installed successfully"
                    ]
                )
            else:
                return RecoveryResult(
                    success=False,
                    actions_taken=f"Failed to install any of the required packages: {', '.join(missing_modules)}",
                    retry_recommended=False,
                    recovery_suggestions=[
                        "Check package names for typos",
                        "Verify internet connectivity",
                        "Consider manual installation",
                        "Check if packages exist in PyPI"
                    ]
                )

        except Exception as e:
            self.logger.error(f"Enhanced import error recovery failed: {str(e)}")
            return RecoveryResult(
                success=False,
                actions_taken=f"Import error recovery encountered error: {str(e)}",
                retry_recommended=True  # Retry might help with transient issues
            )

    async def _recover_configuration_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from configuration errors by setting up missing configurations"""
        self.logger.info("Attempting configuration error recovery")

        try:
            # Identify missing configuration items
            config_issues = self._parse_configuration_error(error)

            actions_taken = []
            recovery_success = True

            for issue_type, details in config_issues.items():
                if issue_type == 'missing_env_var':
                    # Handle missing environment variables
                    env_var = details
                    default_value = self._get_default_env_value(env_var)
                    if default_value:
                        # Set environment variable for current session
                        import os
                        os.environ[env_var] = default_value
                        actions_taken.append(f"Set environment variable {env_var} to default value")
                    else:
                        recovery_success = False
                        actions_taken.append(f"Could not determine default value for {env_var}")

                elif issue_type == 'missing_config_file':
                    # Handle missing configuration files
                    config_file = details
                    if await self._create_default_config_file(config_file):
                        actions_taken.append(f"Created default configuration file: {config_file}")
                    else:
                        recovery_success = False
                        actions_taken.append(f"Could not create default configuration file: {config_file}")

            return RecoveryResult(
                success=recovery_success,
                actions_taken="; ".join(actions_taken) if actions_taken else "No configuration issues could be automatically resolved",
                retry_recommended=recovery_success,
                recovery_suggestions=[
                    "Review configuration settings",
                    "Check environment variable documentation",
                    "Verify configuration file formats"
                ]
            )

        except Exception as e:
            self.logger.error(f"Configuration error recovery failed: {str(e)}")
            return RecoveryResult(
                success=False,
                actions_taken=f"Configuration error recovery encountered error: {str(e)}",
                retry_recommended=False
            )

    async def _recover_dependency_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from dependency conflicts and version issues"""
        self.logger.info("Attempting dependency error recovery")

        try:
            # This is a complex recovery that might involve updating requirements
            # For now, we'll implement a basic version

            return RecoveryResult(
                success=False,
                actions_taken="Dependency error recovery is not fully implemented",
                retry_recommended=False,
                recovery_suggestions=[
                    "Check requirements.txt for version conflicts",
                    "Consider updating package versions",
                    "Review dependency documentation",
                    "Try clean environment setup"
                ]
            )

        except Exception as e:
            self.logger.error(f"Dependency error recovery failed: {str(e)}")
            return RecoveryResult(
                success=False,
                actions_taken=f"Dependency error recovery encountered error: {str(e)}",
                retry_recommended=False
            )

    async def _recover_network_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from network-related errors with enhanced retry strategies and circuit breakers"""
        self.logger.info("Attempting enhanced network error recovery")

        try:
            # Test network connectivity with circuit breaker protection
            connectivity_restored = await self._test_network_connectivity_enhanced()

            if connectivity_restored:
                return RecoveryResult(
                    success=True,
                    actions_taken="Network connectivity verified and restored",
                    retry_recommended=True,
                    recovery_suggestions=["Re-run the task", "Monitor network stability"]
                )
            else:
                # Additional recovery strategies
                recovery_attempts = []

                # Try to flush DNS cache
                dns_flush_result = await self._flush_dns_cache()
                if dns_flush_result:
                    recovery_attempts.append("DNS cache flushed")

                # Test connectivity again after DNS flush
                if dns_flush_result:
                    connectivity_restored = await self._test_network_connectivity_enhanced()
                    if connectivity_restored:
                        return RecoveryResult(
                            success=True,
                            actions_taken="Network connectivity restored after DNS flush",
                            retry_recommended=True,
                            recovery_suggestions=["Re-run the task", "Monitor DNS stability"]
                        )

                return RecoveryResult(
                    success=False,
                    actions_taken=f"Network connectivity could not be restored. Attempted: {', '.join(recovery_attempts) if recovery_attempts else 'connectivity test'}",
                    retry_recommended=True,  # May recover later
                    recovery_suggestions=[
                        "Check network configuration",
                        "Verify internet connectivity",
                        "Check firewall settings",
                        "Contact network administrator",
                        "Try again in a few minutes"
                    ]
                )

        except Exception as e:
            self.logger.error(f"Network error recovery failed: {str(e)}")
            return RecoveryResult(
                success=False,
                actions_taken=f"Network error recovery encountered error: {str(e)}",
                retry_recommended=True
            )

    async def _recover_database_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from database-related errors"""
        self.logger.info("Attempting database error recovery")

        return RecoveryResult(
            success=False,
            actions_taken="Database error recovery is not fully implemented",
            retry_recommended=False,
            recovery_suggestions=[
                "Check database connection settings",
                "Verify database is running",
                "Check database credentials",
                "Review database schema"
            ]
        )

    async def _recover_permission_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from permission-related errors"""
        self.logger.info("Attempting permission error recovery")

        return RecoveryResult(
            success=False,
            actions_taken="Permission error recovery requires manual intervention",
            retry_recommended=False,
            recovery_suggestions=[
                "Check file permissions",
                "Verify user access rights",
                "Run with appropriate privileges",
                "Contact system administrator"
            ]
        )

    async def _recover_validation_error(self, task: Task, error: str) -> RecoveryResult:
        """Recover from validation errors"""
        self.logger.info("Attempting validation error recovery")

        return RecoveryResult(
            success=False,
            actions_taken="Validation errors typically require code fixes",
            retry_recommended=False,
            recovery_suggestions=[
                "Review validation requirements",
                "Check input data format",
                "Verify business logic",
                "Update validation rules if needed"
            ]
        )

    async def _recover_unknown_error(self, task: Task, error: str) -> RecoveryResult:
        """Handle unknown error types with generic recovery strategies"""
        self.logger.info("Attempting generic error recovery")

        return RecoveryResult(
            success=False,
            actions_taken="Error type not recognized, no specific recovery available",
            retry_recommended=True,  # Sometimes a retry helps
            recovery_suggestions=[
                "Review error message details",
                "Check system logs",
                "Try manual task execution",
                "Consider breaking task into smaller steps"
            ]
        )

    # Helper methods for error recovery

    def _parse_syntax_error(self, error: str) -> Dict[str, Any]:
        """Parse syntax error to extract useful information"""
        # Extract file path, line number, and error details
        patterns = {
            'file_path': r'File "([^"]+)"',
            'line_number': r'line (\d+)',
            'error_message': r'SyntaxError: (.+)'
        }

        result = {}
        for key, pattern in patterns.items():
            match = re.search(pattern, error)
            if match:
                result[key] = match.group(1)

        return result

    def _create_syntax_fix_prompt(self, error_details: Dict[str, Any], code: str) -> str:
        """Create LLM prompt for syntax error fixing"""
        prompt = f"""
Fix this syntax error in the following code:

Error: {error_details.get('error_message', 'Unknown syntax error')}
Line: {error_details.get('line_number', 'Unknown')}

Code:
```
{code}
```

Please provide only the corrected code without explanations or markdown formatting.
"""
        return prompt

    async def _validate_python_syntax_fix(self, file_path: Path) -> bool:
        """Validate that the Python syntax fix is correct"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            compile(code, str(file_path), 'exec')
            return True
        except SyntaxError:
            return False

    def _extract_missing_modules(self, error: str) -> List[str]:
        """Extract missing module names from import error"""
        patterns = [
            r"No module named '([^']+)'",
            r"No module named ([^\s]+)",
            r"ModuleNotFoundError: No module named '([^']+)'",
            r"ImportError: cannot import name '([^']+)'"
        ]

        modules = []
        for pattern in patterns:
            matches = re.findall(pattern, error)
            modules.extend(matches)

        return list(set(modules))  # Remove duplicates

    async def _install_python_package(self, package_name: str) -> bool:
        """Install Python package using pip"""
        try:
            result = subprocess.run(
                ['pip', 'install', package_name],
                capture_output=True,
                text=True,
                timeout=120
            )
            return result.returncode == 0
        except Exception as e:
            self.logger.error(f"Package installation failed for {package_name}: {str(e)}")
            return False

    def _parse_configuration_error(self, error: str) -> Dict[str, str]:
        """Parse configuration error to identify specific issues"""
        issues = {}

        # Look for environment variable issues
        env_var_patterns = [
            r"KeyError: '([^']+)'",
            r"Environment variable '([^']+)' not found",
            r"Missing environment variable: ([^\s]+)"
        ]

        for pattern in env_var_patterns:
            matches = re.findall(pattern, error)
            for match in matches:
                issues['missing_env_var'] = match
                break

        # Look for missing config file issues
        config_file_patterns = [
            r"No such file or directory: '([^']*\.(?:json|yaml|yml|ini|conf))'",
            r"Configuration file not found: ([^\s]+)"
        ]

        for pattern in config_file_patterns:
            matches = re.findall(pattern, error)
            for match in matches:
                issues['missing_config_file'] = match
                break

        return issues

    def _get_default_env_value(self, env_var: str) -> Optional[str]:
        """Get default value for common environment variables"""
        defaults = {
            'DEBUG': 'False',
            'PORT': '8000',
            'HOST': '0.0.0.0',
            'DATABASE_URL': 'sqlite:///./app.db',
            'SECRET_KEY': 'dev-secret-key-change-in-production',
            'REDIS_URL': 'redis://localhost:6379',
            'LOG_LEVEL': 'INFO'
        }
        return defaults.get(env_var)

    async def _create_default_config_file(self, config_file: str) -> bool:
        """Create a default configuration file"""
        try:
            config_path = Path(config_file)

            # Create default config based on file extension
            if config_path.suffix == '.json':
                default_content = '{\n    "debug": false,\n    "host": "0.0.0.0",\n    "port": 8000\n}'
            elif config_path.suffix in ['.yaml', '.yml']:
                default_content = 'debug: false\nhost: "0.0.0.0"\nport: 8000\n'
            elif config_path.suffix == '.ini':
                default_content = '[DEFAULT]\ndebug = False\nhost = 0.0.0.0\nport = 8000\n'
            else:
                return False

            config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(default_content)

            return True
        except Exception as e:
            self.logger.error(f"Could not create default config file {config_file}: {str(e)}")
            return False

    async def _test_network_connectivity(self) -> bool:
        """Test basic network connectivity (legacy method for compatibility)"""
        return await self._test_network_connectivity_enhanced()

    @with_retry("network_recovery", RetryStrategy.FIXED_INTERVAL)
    async def _flush_dns_cache(self) -> bool:
        """Attempt to flush DNS cache on the system"""
        try:
            import platform
            system = platform.system().lower()

            if system == "windows":
                result = subprocess.run(
                    ['ipconfig', '/flushdns'],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
            elif system == "darwin":  # macOS
                result = subprocess.run(
                    ['sudo', 'dscacheutil', '-flushcache'],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
            elif system == "linux":
                # Try systemd-resolved first, then other methods
                result = subprocess.run(
                    ['sudo', 'systemctl', 'restart', 'systemd-resolved'],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                if result.returncode != 0:
                    # Fallback to nscd
                    result = subprocess.run(
                        ['sudo', 'service', 'nscd', 'restart'],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )
            else:
                self.logger.warning(f"DNS flush not implemented for system: {system}")
                return False

            success = result.returncode == 0
            if success:
                self.logger.info(f"DNS cache flushed successfully on {system}")
            else:
                self.logger.warning(f"DNS cache flush failed on {system}: {result.stderr}")

            return success

        except Exception as e:
            self.logger.error(f"DNS cache flush error: {str(e)}")
            return False

    def _record_recovery_attempt(self, task: Task, error: Exception, error_type: ErrorType,
                                result: RecoveryResult, duration: float):
        """Record recovery attempt for analysis"""
        record = {
            "timestamp": time.time(),
            "task_id": task.id,
            "task_title": task.title,
            "task_type": task.type,
            "error_type": error_type,
            "error_message": str(error)[:500],  # Truncate long errors
            "recovery_success": result.success,
            "recovery_duration": duration,
            "actions_taken": result.actions_taken,
            "retry_recommended": result.retry_recommended
        }

        self._recovery_history.append(record)

        # Keep only last 50 records
        if len(self._recovery_history) > 50:
            self._recovery_history = self._recovery_history[-50:]

    def get_recovery_statistics(self) -> Dict[str, Any]:
        """Get recovery system statistics"""
        success_rate = (self._successful_recoveries / self._recovery_attempts
                       if self._recovery_attempts > 0 else 0.0)

        return {
            "total_attempts": self._recovery_attempts,
            "successful_recoveries": self._successful_recoveries,
            "success_rate": success_rate,
            "recent_history_count": len(self._recovery_history)
        }

    # Enhanced helper methods for robust error recovery

    @with_retry("filesystem", RetryStrategy.EXPONENTIAL_BACKOFF)
    async def _read_file_with_retry(self, file_path: Path) -> Optional[str]:
        """Read file with retry logic for transient failures"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.logger.error(f"Failed to read file {file_path}: {str(e)}")
            return None

    @circuit_breaker("llm_service")
    async def _fix_syntax_with_llm(self, error_details: Dict[str, Any], code: str) -> Optional[str]:
        """Fix syntax error using LLM with circuit breaker protection"""
        try:
            if not self.llm_service:
                return None

            fix_prompt = self._create_syntax_fix_prompt(error_details, code)
            response = await self.llm_service.generate(fix_prompt)
            corrected_code = response.content if response else None
            return corrected_code
        except Exception as e:
            self.logger.error(f"LLM syntax fix failed: {str(e)}")
            return None

    @with_retry("filesystem", RetryStrategy.LINEAR_BACKOFF)
    async def _apply_code_fix_with_backup(self, file_path: Path, original_code: str, corrected_code: str) -> bool:
        """Apply code fix with backup creation and retry logic"""
        try:
            backup_path = file_path.with_suffix(file_path.suffix + '.backup')

            # Create backup
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_code)

            # Apply fix
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(corrected_code)

            return True
        except Exception as e:
            self.logger.error(f"Failed to apply code fix to {file_path}: {str(e)}")
            return False

    async def _validate_syntax_fix(self, file_path: Path) -> bool:
        """Validate that syntax fix is correct"""
        if file_path.suffix == '.py':
            return await self._validate_python_syntax_fix(file_path)
        elif file_path.suffix in ['.js', '.jsx', '.ts', '.tsx']:
            return await self._validate_javascript_syntax_fix(file_path)
        else:
            return True  # Assume valid for unknown file types

    async def _validate_javascript_syntax_fix(self, file_path: Path) -> bool:
        """Validate JavaScript/TypeScript syntax fix"""
        try:
            result = subprocess.run(
                ['node', '-c', str(file_path)],
                capture_output=True,
                text=True,
                timeout=30
            )
            return result.returncode == 0
        except Exception:
            return True  # Assume valid if Node.js not available

    @with_retry("filesystem", RetryStrategy.FIXED_INTERVAL)
    async def _restore_from_backup(self, file_path: Path) -> bool:
        """Restore file from backup with retry logic"""
        try:
            backup_path = file_path.with_suffix(file_path.suffix + '.backup')
            if backup_path.exists():
                with open(backup_path, 'r', encoding='utf-8') as f:
                    original_code = f.read()
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(original_code)
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to restore from backup: {str(e)}")
            return False

    @with_retry("package_install", RetryStrategy.JITTERED_EXPONENTIAL)
    async def _install_python_package_enhanced(self, package_name: str) -> bool:
        """Enhanced package installation with retry and circuit breaker protection"""
        try:
            result = subprocess.run(
                ['pip', 'install', package_name],
                capture_output=True,
                text=True,
                timeout=120
            )
            return result.returncode == 0
        except Exception as e:
            self.logger.error(f"Enhanced package installation failed for {package_name}: {str(e)}")
            return False

    @with_retry("network_recovery", RetryStrategy.EXPONENTIAL_BACKOFF)
    @circuit_breaker("network_connectivity")
    async def _test_network_connectivity_enhanced(self) -> bool:
        """Enhanced network connectivity test with circuit breaker"""
        try:
            # Test multiple endpoints for better reliability
            test_urls = ['*******', '*******', 'google.com']

            for url in test_urls:
                result = subprocess.run(
                    ['ping', '-c', '1', '-W', '5', url],
                    capture_output=True,
                    timeout=10
                )
                if result.returncode == 0:
                    return True

            return False
        except Exception as e:
            self.logger.error(f"Network connectivity test failed: {str(e)}")
            return False

    def get_circuit_breaker_status(self) -> Dict[str, Any]:
        """Get status of all circuit breakers used by error recovery"""
        from ..core.circuit_breaker import circuit_breaker_registry
        return circuit_breaker_registry.get_all_metrics()

    def get_retry_statistics(self) -> Dict[str, Any]:
        """Get statistics from retry manager"""
        return retry_manager.get_all_metrics()

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of error recovery system"""
        health_status = {
            "error_recovery_system": True,
            "llm_service_available": self.llm_service is not None,
            "circuit_breakers": {},
            "retry_policies": {},
            "recent_performance": {}
        }

        try:
            # Check circuit breaker health
            cb_status = self.get_circuit_breaker_status()
            health_status["circuit_breakers"] = cb_status

            # Check retry statistics
            retry_stats = self.get_retry_statistics()
            health_status["retry_policies"] = retry_stats

            # Test basic functionality
            test_connectivity = await self._test_network_connectivity_enhanced()
            health_status["network_connectivity"] = test_connectivity

        except Exception as e:
            self.logger.error(f"Health check failed: {str(e)}")
            health_status["error_recovery_system"] = False
            health_status["health_check_error"] = str(e)

        return health_status