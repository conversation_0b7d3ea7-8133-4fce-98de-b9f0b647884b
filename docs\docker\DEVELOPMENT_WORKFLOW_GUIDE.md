# Development Workflow Guide

## Overview

The development environment has been optimized for maximum performance and reliability with the following improvements:

## Key Improvements

### 🏗️ Dedicated Development Dockerfile

- **Dockerfile.dev**: Pre-installs all dependencies including debug tools
- **Faster Startup**: No more runtime dependency installation
- **Production Parity**: Development build closely mirrors production

### 🔒 Enhanced Security

- **Localhost Binding**: All ports bound to `127.0.0.1` only
- **Secure Authentication**: code-server uses password authentication (not disabled)
- **Minimal Attack Surface**: No unnecessary external access

### 📁 Simplified Project Access

- **Complete Project Mount**: Entire project available in code-server
- **Unified Workspace**: Edit all files from one interface
- **No Redundant Mounts**: Cleaner, more maintainable configuration

## Usage

### Start Development Environment

```bash
# Modern development with watch mode
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch

# With secrets (most secure)
docker-compose -f docker-compose.yml -f docker-compose.dev.yml -f docker-compose.secrets.yml up --watch
```

### Access Points

- **AI Orchestrator**: http://localhost:8000
- **Admin Dashboard**: http://localhost:3000
- **Code Server**: http://localhost:8080 (password required)
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

### Debugging

- **Python Debugger**: Connect to `localhost:5678`
- **Hot Reload**: Automatic for all file changes
- **Debug Logs**: Enhanced logging enabled

## Development Features

### Hot Reloading

- **Python**: Source code changes trigger automatic reload
- **React**: Fast refresh and hot module replacement
- **Dependencies**: `requirements.txt` changes trigger container rebuild

### Code-Server Integration

- **Full Project Access**: Edit any file in the project
- **Extensions**: Auto-installed development extensions
- **Settings**: Persistent VS Code configuration

### Performance Optimizations

- **Anonymous Volumes**: Prevent cache conflicts
- **Delegated Mounts**: Optimized for development
- **Watch Filtering**: Ignores cache and build artifacts

## Troubleshooting

### Slow Startup

If containers take long to start:
```bash
# Rebuild development images
docker-compose -f docker-compose.yml -f docker-compose.dev.yml build --no-cache

# Check for dependency issues
docker-compose logs ai-orchestrator
```

### Port Access Issues

Ensure you're accessing via localhost:
- ✅ `http://localhost:8000`
- ❌ `http://0.0.0.0:8000`

### Code-Server Authentication

If you can't access code-server:
1. Check password in `.env` file: `CODE_SERVER_PASSWORD=your_password`
2. Use the password when prompted
3. Password is required for security

### Watch Mode Issues

If file changes aren't detected:
```bash
# Restart with fresh watch
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch
```

## Best Practices

### File Organization

- Keep all source code in appropriate container directories
- Use workspace for temporary development files
- Configuration files remain in project root

### Security

- Never disable authentication in production
- Use secrets for sensitive data
- Bind ports to localhost only in development

### Performance

- Use anonymous volumes for cache directories
- Avoid mounting large directories unnecessarily
- Let Docker Compose handle dependency management

## Migration from Previous Setup

If upgrading from older configuration:

1. **Rebuild images**: Use `--build` flag to ensure new Dockerfile.dev is used
2. **Update access URLs**: Use `localhost` instead of `0.0.0.0`
3. **Set code-server password**: Ensure `CODE_SERVER_PASSWORD` is set in `.env`
4. **Clear old volumes**: Remove old volumes if experiencing issues

```bash
# Clean migration
docker-compose down -v
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build --watch
```