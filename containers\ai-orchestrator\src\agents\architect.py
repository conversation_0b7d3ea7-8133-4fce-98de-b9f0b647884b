# Project: AI Coding Agent
# Purpose: Enhanced Architect Agent with comprehensive validation and error recovery

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

from .base_agent import BaseAgent
from ..models.validation_models import (
    AgentType, Task, TaskResult, ValidationResult, RecoveryResult,
    Step, Phase, Roadmap, ExecutionStatus, ApprovalRequest, Checkpoint,
    ErrorType, HealthCheckResult, ProductionReadinessResult
)
from ..services.task_validator import TaskValidator
from ..services.error_recovery import ErrorRecoverySystem
from ..utils.llm_service import LLMService
from ..services.enhanced_llm_service import EnhancedLLMService, GenerateRequest


class ArchitectAgent(BaseAgent):
    """
    Enhanced Architect Agent that serves as the master coordinator with built-in
    validation gates, error recovery, and user oversight capabilities.

    Responsibilities:
    - Master coordination of all other agents
    - Sequential task execution with validation gates
    - Error recovery and retry management
    - Progress monitoring and user approval workflows
    - Checkpoint management for rollback capability
    """

    def __init__(self):
        super().__init__(AgentType.ARCHITECT, max_concurrent_tasks=1)

        # Core systems
        self.task_validator = TaskValidator()
        self.error_recovery = ErrorRecoverySystem()

        # Execution control
        self.roadmap_lock = asyncio.Lock()
        self._current_roadmap: Optional[Roadmap] = None
        self._execution_context: Dict[str, Any] = {}

        # Agent registry (will be populated with other agents)
        self._agent_registry: Dict[AgentType, BaseAgent] = {}

        # Approval and checkpoint systems (placeholders for now)
        self._pending_approvals: Dict[str, ApprovalRequest] = {}
        self._checkpoints: Dict[str, Checkpoint] = {}

        # Chat conversation context
        self._conversation_history: Dict[str, List[Dict[str, str]]] = {}
        self._max_conversation_history = 10  # Keep last 10 messages per user

        # LLM service for chat
        self._llm_service: Optional[EnhancedLLMService] = None

        self.logger.info("Enhanced Architect Agent initialized with validation and recovery systems")

    async def initialize(self):
        """Initialize the Architect Agent with required services."""
        try:
            # Initialize LLM service for chat
            from ..router.llm_router import get_llm_service
            self._llm_service = await get_llm_service()

            self.logger.info("Architect Agent initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Architect Agent: {e}")
            raise

    async def register_agent(self, agent: BaseAgent):
        """Register another agent with this architect"""
        self._agent_registry[agent.agent_type] = agent
        self.logger.info(f"Registered agent: {agent.agent_type}")

    async def execute_roadmap_with_strict_validation(self, roadmap: Roadmap, user_id: str) -> bool:
        """
        Execute roadmap with comprehensive validation and user oversight.

        Args:
            roadmap: The roadmap to execute
            user_id: User ID for approval workflows

        Returns:
            bool: True if roadmap completed successfully, False otherwise
        """
        async with self.roadmap_lock:
            self._current_roadmap = roadmap
            roadmap.status = ExecutionStatus.IN_PROGRESS
            roadmap.started_at = datetime.now()
            initial_checkpoint_id = None  # Initialize to prevent unbound variable

            self.logger.info(f"Starting roadmap execution: {roadmap.title}")

            try:
                # Create initial checkpoint
                initial_checkpoint_id = await self._create_checkpoint(roadmap.id, 'initial')

                # Execute each phase sequentially
                for phase_index, phase in enumerate(roadmap.phases):
                    self.logger.info(f"Starting Phase {phase_index + 1}/{len(roadmap.phases)}: {phase.title}")

                    # Create phase checkpoint
                    phase_checkpoint_id = await self._create_checkpoint(roadmap.id, f'phase_{phase.id}')

                    try:
                        # Execute phase with comprehensive validation
                        await self._execute_phase_with_validation(phase, user_id)

                        # Require user approval if configured
                        if phase.requires_approval:
                            if not await self._request_user_approval('phase', phase.id, user_id,
                                                                   f"Phase completed: {phase.title}"):
                                # User rejected - rollback to phase start
                                await self._rollback_to_checkpoint(phase_checkpoint_id)
                                return False

                        self.logger.info(f"Phase completed: {phase.title}")

                    except Exception as e:
                        self.logger.error(f"Phase execution failed: {phase.title} - {str(e)}")

                        # Attempt phase-level error recovery
                        recovery_result = await self._handle_phase_failure(phase, e)

                        if not recovery_result.success:
                            # Recovery failed - rollback and abort
                            await self._rollback_to_checkpoint(phase_checkpoint_id)
                            raise Exception(f"Phase {phase.title} failed and could not be recovered: {e}")

                # All phases completed - run final validations
                final_validation = await self._run_final_validation(roadmap)

                if not final_validation.is_valid:
                    self.logger.error(f"Final validation failed: {final_validation.error}")
                    await self._rollback_to_checkpoint(initial_checkpoint_id)
                    return False

                # Mark roadmap as completed
                roadmap.status = ExecutionStatus.COMPLETED
                roadmap.completed_at = datetime.now()

                self.logger.info(f"Roadmap execution completed successfully: {roadmap.title}")
                return True

            except Exception as e:
                self.logger.error(f"Roadmap execution failed: {str(e)}")

                # Global rollback to initial state (only if checkpoint was created)
                if initial_checkpoint_id:
                    await self._rollback_to_checkpoint(initial_checkpoint_id)

                roadmap.status = ExecutionStatus.FAILED
                roadmap.completed_at = datetime.now()

                raise Exception(f"Roadmap execution failed: {e}")

            finally:
                self._current_roadmap = None
                self._execution_context.clear()

    async def _execute_phase_with_validation(self, phase: Phase, user_id: str):
        """Execute phase with comprehensive validation"""
        phase.status = ExecutionStatus.IN_PROGRESS
        phase.started_at = datetime.now()

        try:
            # Execute all steps in sequence
            for step_index, step in enumerate(phase.steps):
                self.logger.info(f"  Starting Step {step_index + 1}/{len(phase.steps)}: {step.title}")
                await self._execute_step_with_validation(step)

            # Phase-level validation
            phase_validation = await self._validate_phase_completion(phase)
            if not phase_validation.is_valid:
                raise Exception(f"Phase validation failed: {phase_validation.error}")

            phase.status = ExecutionStatus.COMPLETED
            phase.completed_at = datetime.now()

        except Exception as e:
            phase.status = ExecutionStatus.FAILED
            phase.completed_at = datetime.now()
            raise

    async def _execute_step_with_validation(self, step: Step):
        """Execute step with task-by-task validation"""
        step.status = ExecutionStatus.IN_PROGRESS
        step.started_at = datetime.now()

        try:
            # Execute each task in the step
            for task_index, task in enumerate(step.tasks):
                self.logger.info(f"    Executing Task {task_index + 1}/{len(step.tasks)}: {task.title}")
                await self._execute_task_with_retry(task)

            # Step-level validation
            step_validation = await self._validate_step_completion(step)
            if not step_validation.is_valid:
                raise Exception(f"Step validation failed: {step_validation.error}")

            step.status = ExecutionStatus.COMPLETED
            step.completed_at = datetime.now()

        except Exception as e:
            step.status = ExecutionStatus.FAILED
            step.completed_at = datetime.now()
            raise

    async def _execute_task_with_retry(self, task: Task) -> TaskResult:
        """Execute task with comprehensive retry logic and validation"""
        # Determine which agent should handle this task
        target_agent = self._agent_registry.get(task.agent_type)

        if not target_agent:
            # If no specific agent, execute with this architect agent
            return await self.execute_task_with_validation(task)

        # Delegate to appropriate agent
        for attempt in range(task.max_retries + 1):
            try:
                self.logger.info(f"Delegating task to {task.agent_type}: {task.title} (attempt {attempt + 1})")

                # Execute task through target agent
                result = await target_agent.execute_task_with_validation(task)

                if result.success:
                    # Additional validation by architect
                    validation = await self.task_validator.validate_task_completion(task, result)

                    if validation.is_valid:
                        return result
                    else:
                        # Validation failed, but task reported success
                        self.logger.warning(f"Task validation failed despite successful execution: {validation.error}")
                        result.success = False
                        result.error = f"Post-execution validation failed: {validation.error}"

                # Task failed, attempt recovery if not on last attempt
                if attempt < task.max_retries:
                    recovery_result = await self.error_recovery.handle_task_failure(task, Exception(result.error or "Unknown error"))

                    if recovery_result.retry_recommended:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                        continue

                return result

            except Exception as e:
                self.logger.error(f"Task execution attempt {attempt + 1} failed: {str(e)}")

                if attempt < task.max_retries:
                    # Attempt recovery
                    recovery_result = await self.error_recovery.handle_task_failure(task, e)
                    if recovery_result.retry_recommended:
                        await asyncio.sleep(2 ** attempt)
                        continue

                # All retries exhausted
                return TaskResult(
                    success=False,
                    error=f"Task failed after {task.max_retries + 1} attempts. Last error: {str(e)}"
                )

        # Fallback return if no execution path is taken (should not happen)
        return TaskResult(
            success=False,
            error="Task execution failed - no execution path taken"
        )

    async def _validate_phase_completion(self, phase: Phase) -> ValidationResult:
        """Validate that phase completed successfully"""
        validations = []

        # Check that all steps completed
        incomplete_steps = [step for step in phase.steps if step.status != ExecutionStatus.COMPLETED]
        if incomplete_steps:
            validations.append(ValidationResult.failure(
                f"Phase has {len(incomplete_steps)} incomplete steps"
            ))
        else:
            validations.append(ValidationResult.success("All steps completed"))

        # Run any phase-specific validation logic
        # This can be extended based on specific phase requirements

        return ValidationResult.from_checks(validations)

    async def _validate_step_completion(self, step: Step) -> ValidationResult:
        """Validate that step completed successfully"""
        validations = []

        # Check that all tasks completed
        incomplete_tasks = [task for task in step.tasks if task.status != ExecutionStatus.COMPLETED]
        if incomplete_tasks:
            validations.append(ValidationResult.failure(
                f"Step has {len(incomplete_tasks)} incomplete tasks"
            ))
        else:
            validations.append(ValidationResult.success("All tasks completed"))

        # Check for any failed tasks
        failed_tasks = [task for task in step.tasks if task.status == ExecutionStatus.FAILED]
        if failed_tasks:
            validations.append(ValidationResult.failure(
                f"Step has {len(failed_tasks)} failed tasks"
            ))

        return ValidationResult.from_checks(validations)

    async def _run_final_validation(self, roadmap: Roadmap) -> ValidationResult:
        """Run comprehensive final validation for the entire roadmap"""
        validations = []

        # Check that all phases completed
        incomplete_phases = [phase for phase in roadmap.phases if phase.status != ExecutionStatus.COMPLETED]
        if incomplete_phases:
            validations.append(ValidationResult.failure(
                f"Roadmap has {len(incomplete_phases)} incomplete phases"
            ))
        else:
            validations.append(ValidationResult.success("All phases completed"))

        # Run integration tests if configured
        # This would check that all components work together
        integration_validation = await self._run_integration_validation(roadmap)
        validations.append(integration_validation)

        # Run health check
        health_check = await self._run_health_check(roadmap)
        if health_check.is_healthy:
            validations.append(ValidationResult.success("System health check passed"))
        else:
            validations.append(ValidationResult.failure(
                f"System health check failed: {len(health_check.failed_checks)} issues"
            ))

        return ValidationResult.from_checks(validations)

    async def _run_integration_validation(self, roadmap: Roadmap) -> ValidationResult:
        """Run integration validation across all completed work"""
        # This is a placeholder for comprehensive integration testing
        # In a real implementation, this would:
        # - Test API endpoints
        # - Verify database connectivity
        # - Check frontend-backend communication
        # - Validate deployment configuration

        return ValidationResult.success("Integration validation placeholder - implement based on specific requirements")

    async def _run_health_check(self, roadmap: Roadmap) -> HealthCheckResult:
        """Run comprehensive health check on the system"""
        # This is a placeholder for system health checking
        # In a real implementation, this would:
        # - Check all services are running
        # - Verify database connectivity
        # - Test API responsiveness
        # - Check resource usage

        return HealthCheckResult(
            is_healthy=True,
            checks_passed=1,
            total_checks=1,
            failed_checks=[],
            performance_metrics={}
        )

    async def _handle_phase_failure(self, phase: Phase, error: Exception) -> RecoveryResult:
        """Handle phase-level failures with recovery attempts"""
        self.logger.error(f"Phase failure: {phase.title} - {str(error)}")

        # Attempt to recover from phase failure
        # This could involve:
        # - Retrying failed tasks
        # - Rolling back partial changes
        # - Adjusting configuration
        # - Requesting user intervention

        return RecoveryResult(
            success=False,
            actions_taken="Phase-level recovery not fully implemented",
            retry_recommended=False,
            recovery_suggestions=[
                "Review phase execution logs",
                "Check for incomplete tasks",
                "Consider manual intervention"
            ]
        )

    # Checkpoint management methods

    async def _create_checkpoint(self, roadmap_id: str, checkpoint_type: str) -> str:
        """Create a checkpoint of current project state"""
        checkpoint_id = f"{roadmap_id}_{checkpoint_type}_{int(time.time())}"

        # Create checkpoint (placeholder implementation)
        checkpoint = Checkpoint(
            id=checkpoint_id,
            roadmap_id=roadmap_id,
            type=checkpoint_type,
            project_hash="placeholder_hash",  # Would calculate actual hash
            metadata={
                "created_by": "architect_agent",
                "execution_context": dict(self._execution_context)
            }
        )

        self._checkpoints[checkpoint_id] = checkpoint
        self.logger.info(f"Created checkpoint: {checkpoint_id}")

        return checkpoint_id

    async def _rollback_to_checkpoint(self, checkpoint_id: str) -> bool:
        """Rollback project to a previous checkpoint"""
        checkpoint = self._checkpoints.get(checkpoint_id)
        if not checkpoint:
            self.logger.error(f"Checkpoint not found: {checkpoint_id}")
            return False

        try:
            self.logger.info(f"Rolling back to checkpoint: {checkpoint_id}")

            # Restore project state (placeholder implementation)
            # In a real implementation, this would:
            # - Restore file system state
            # - Rollback database changes
            # - Reset configuration
            # - Update execution status

            self._execution_context = checkpoint.metadata.get("execution_context", {})

            return True

        except Exception as e:
            self.logger.error(f"Rollback failed: {str(e)}")
            return False

    # User approval system methods

    async def _request_user_approval(self, item_type: str, item_id: str, user_id: str, description: str) -> bool:
        """Request user approval with timeout"""
        approval_request = ApprovalRequest(
            user_id=user_id,
            item_type=item_type,
            item_id=item_id,
            title=f"Approval required for {item_type}",
            description=description
        )

        self._pending_approvals[approval_request.id] = approval_request

        # In a real implementation, this would:
        # - Send notification to user via WebSocket
        # - Create UI for approval workflow
        # - Wait for user response with timeout

        # For now, auto-approve (placeholder)
        self.logger.info(f"Auto-approving {item_type} {item_id} (placeholder implementation)")
        return True

    # Agent-specific implementation of abstract methods

    async def _execute_core_task(self, task: Task) -> TaskResult:
        """
        Core task execution for architect-level tasks.
        These are typically coordination and planning tasks.
        """
        self.logger.info(f"Executing architect task: {task.title}")

        # Architect tasks are typically coordination tasks
        if task.type.value == "planning":
            return await self._execute_planning_task(task)
        elif task.type.value == "coordination":
            return await self._execute_coordination_task(task)
        else:
            # Use LLM for general architect tasks
            prompt = f"""
            As an architect agent, execute this task:

            Task: {task.title}
            Description: {task.description}
            Type: {task.type}
            Parameters: {json.dumps(task.parameters, indent=2)}

            Provide a structured response about what actions would be taken.
            """

            llm_response = await self.llm_service.generate(prompt)
            response = llm_response.content if llm_response else "No response generated"

            return TaskResult(
                success=True,
                output=response,
                metadata={"task_type": "architect_coordination"}
            )

    async def _execute_planning_task(self, task: Task) -> TaskResult:
        """Execute planning-related tasks"""
        return TaskResult(
            success=True,
            output="Planning task completed",
            metadata={"planning_result": "Task planning completed successfully"}
        )

    async def _execute_coordination_task(self, task: Task) -> TaskResult:
        """Execute coordination-related tasks"""
        return TaskResult(
            success=True,
            output="Coordination task completed",
            metadata={"coordination_result": "Agent coordination completed successfully"}
        )

    async def _validate_agent_specific_prerequisites(self, task: Task) -> ValidationResult:
        """Architect-specific prerequisite validation"""
        checks = []

        # Ensure other agents are available if needed
        if task.agent_type != AgentType.ARCHITECT and task.agent_type not in self._agent_registry:
            checks.append(ValidationResult.failure(
                f"Required agent not available: {task.agent_type}"
            ))
        else:
            checks.append(ValidationResult.success("Required agents available"))

        return ValidationResult.from_checks(checks)

    async def _validate_agent_specific_completion(self, task: Task, result: TaskResult) -> ValidationResult:
        """Architect-specific completion validation"""
        # Basic validation for architect tasks
        return ValidationResult.success("Architect task validation passed")

    # ==================================================================================
    # STATUS AND MONITORING METHODS
    # ==================================================================================

    async def get_roadmap_status(self) -> Optional[Dict[str, Any]]:
        """Get current roadmap execution status"""
        if not self._current_roadmap:
            return None

        return {
            "roadmap_id": self._current_roadmap.id,
            "roadmap_title": self._current_roadmap.title,
            "status": self._current_roadmap.status,
            "started_at": self._current_roadmap.started_at.isoformat() if self._current_roadmap.started_at else None,
            "completed_at": self._current_roadmap.completed_at.isoformat() if self._current_roadmap.completed_at else None,
            "total_phases": len(self._current_roadmap.phases),
            "completed_phases": sum(1 for phase in self._current_roadmap.phases if phase.status == ExecutionStatus.COMPLETED),
            "failed_phases": sum(1 for phase in self._current_roadmap.phases if phase.status == ExecutionStatus.FAILED),
            "current_phase": next((phase.title for phase in self._current_roadmap.phases if phase.status == ExecutionStatus.IN_PROGRESS), None)
        }

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        base_status = await self.get_status()

        return {
            **base_status,
            "roadmap_status": await self.get_roadmap_status(),
            "registered_agents": list(self._agent_registry.keys()),
            "pending_approvals": len(self._pending_approvals),
            "checkpoints_count": len(self._checkpoints),
            "recovery_stats": self.error_recovery.get_recovery_statistics()
        }

    # ==================================================================================
    # CHAT FUNCTIONALITY METHODS
    # ==================================================================================

    async def handle_chat_message(self, user_id: str, message: str) -> Dict[str, Any]:
        """
        Handle incoming chat messages from users.

        Args:
            user_id: ID of the user sending the message
            message: The chat message content

        Returns:
            Dict containing the agent's response and metadata
        """
        try:
            # Initialize conversation history if needed
            if user_id not in self._conversation_history:
                self._conversation_history[user_id] = []

            # Add user message to history
            self._conversation_history[user_id].append({
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat()
            })

            # Maintain conversation history limit
            if len(self._conversation_history[user_id]) > self._max_conversation_history:
                self._conversation_history[user_id] = self._conversation_history[user_id][-self._max_conversation_history:]

            # Generate response using LLM
            response = await self._generate_chat_response(user_id, message)

            # Add agent response to history
            self._conversation_history[user_id].append({
                "role": "assistant",
                "content": response,
                "timestamp": datetime.now().isoformat()
            })

            return {
                "response": response,
                "user_id": user_id,
                "timestamp": datetime.now().isoformat(),
                "agent_type": "architect",
                "conversation_length": len(self._conversation_history[user_id])
            }

        except Exception as e:
            self.logger.error(f"Error handling chat message from {user_id}: {e}")
            return {
                "response": "I apologize, but I encountered an error processing your message. Please try again.",
                "user_id": user_id,
                "timestamp": datetime.now().isoformat(),
                "agent_type": "architect",
                "error": str(e)
            }

    async def _generate_chat_response(self, user_id: str, message: str) -> str:
        """
        Generate a response using the LLM service.

        Args:
            user_id: ID of the user
            message: The user's message

        Returns:
            Generated response string
        """
        try:
            # Get conversation context
            conversation_context = self._get_conversation_context(user_id)

            # Build prompt with context
            system_prompt = """
You are an AI Architect Agent, a master coordinator in an AI coding assistant system. You are responsible for:

1. Understanding user requirements and breaking them down into actionable tasks
2. Coordinating with other specialized agents (Frontend, Backend, Shell, Issue Fix)
3. Providing architectural guidance and system design advice
4. Managing project workflows and ensuring quality standards
5. Helping users with coding questions and technical decisions

Your communication style should be:
- Professional but friendly
- Clear and concise
- Technical when appropriate, but accessible to users of varying skill levels
- Proactive in offering solutions and next steps

Current system status:
- Available agents: Frontend, Backend, Shell, Issue Fix
- Your role: Master coordinator and technical advisor
"""

            # Create the prompt with conversation history
            messages = [
                {"role": "system", "content": system_prompt}
            ]

            # Add recent conversation history
            for msg in conversation_context:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })

            # Add current message
            messages.append({
                "role": "user",
                "content": message
            })

            # Generate response using LLM service
            if self._llm_service:
                request = GenerateRequest(
                    prompt="\n\n".join([f"{msg['role']}: {msg['content']}" for msg in messages]),
                    max_tokens=1024,
                    temperature=0.7
                )

                llm_response = await self._llm_service.generate(request)
                return llm_response.content if llm_response and llm_response.content else "I'm having trouble generating a response right now. Please try again."
            else:
                return "LLM service is not available. Please check the system configuration."

        except Exception as e:
            self.logger.error(f"Error generating chat response: {e}")
            return "I encountered an error while processing your request. Please try again or contact support."

    def _get_conversation_context(self, user_id: str, max_messages: int = 6) -> List[Dict[str, str]]:
        """
        Get recent conversation context for a user.

        Args:
            user_id: ID of the user
            max_messages: Maximum number of recent messages to include

        Returns:
            List of recent conversation messages
        """
        if user_id not in self._conversation_history:
            return []

        # Return recent messages (excluding timestamps for LLM)
        recent_messages = self._conversation_history[user_id][-max_messages:]
        return [{
            "role": msg["role"],
            "content": msg["content"]
        } for msg in recent_messages]

    async def get_conversation_history(self, user_id: str) -> List[Dict[str, str]]:
        """
        Get full conversation history for a user.

        Args:
            user_id: ID of the user

        Returns:
            List of all conversation messages for the user
        """
        return self._conversation_history.get(user_id, [])

    async def clear_conversation_history(self, user_id: str) -> bool:
        """
        Clear conversation history for a user.

        Args:
            user_id: ID of the user

        Returns:
            True if history was cleared, False if user had no history
        """
        if user_id in self._conversation_history:
            del self._conversation_history[user_id]
            return True
        return False