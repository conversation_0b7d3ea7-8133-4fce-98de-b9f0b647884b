# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: FastAPI router for role-based LLM configuration management

import os
import json
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from fastapi.responses import JSONResponse
try:
    from slowapi import Limiter, _rate_limit_exceeded_handler  # type: ignore
    from slowapi.util import get_remote_address  # type: ignore
    from slowapi.errors import RateLimitExceeded  # type: ignore
    SLOWAPI_AVAILABLE = True
except ImportError:
    # Fallback when slowapi is not available
    SLOWAPI_AVAILABLE = False
    Limiter = None  # type: ignore
    RateLimitExceeded = Exception
    def get_remote_address(request):  # type: ignore
        return request.client.host if request.client else "unknown"
    def _rate_limit_exceeded_handler(request, exc):  # type: ignore
        return JSONResponse({"error": "Rate limit exceeded"}, status_code=429)

import aiofiles
import aiofiles.os
import os as sync_os

from ..models.role_config import (
    RoleConfiguration,
    RoleConfigurationUpdate,
    RoleConfigurationList,
    RoleConfigurationResponse,
    RoleListResponse,
    ProviderModelsResponse,
    InvalidRoleConfigError,
    ConfigPersistenceError
)
from ..models.llm_models import LLMProvider
from ..utils.auth import get_current_user

logger = logging.getLogger(__name__)

# Rate limiting
if SLOWAPI_AVAILABLE and Limiter:
    limiter = Limiter(key_func=get_remote_address)
else:
    limiter = None

# Conditional rate limiting decorator with better error handling
def rate_limit(limit_string: str):
    """Conditional rate limiting decorator with graceful fallback."""
    def decorator(func):
        if SLOWAPI_AVAILABLE and limiter:
            try:
                return limiter.limit(limit_string)(func)
            except Exception as e:
                logger.warning(f"Rate limiting setup failed: {e}. Proceeding without rate limiting.")
                return func
        else:
            logger.debug(f"Rate limiting not available for {func.__name__}")
            return func
    return decorator

# Configuration file path
CONFIG_FILE_PATH = os.getenv("ROLE_CONFIG_PATH", "/app/data/role_config.json")
CONFIG_LOCK = asyncio.Lock()

def create_default_config() -> Dict[str, Any]:
    """
    Create default configuration with API keys from environment variables.

    Returns:
        Dict: Default configuration with environment-based API keys
    """
    # Read API keys from environment variables
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")

    now = datetime.utcnow().isoformat()

    return {
        "roles": {
            "architect": {
                "provider": "openrouter",
                "available_models": ["anthropic/claude-3-sonnet", "openai/gpt-4"],
                "selected_model": "anthropic/claude-3-sonnet",
                "api_key": openrouter_key,
                "cost_limit": 100.0,
                "max_tokens": 4096,
                "temperature": 0.7,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            },
            "backend": {
                "provider": "ollama",
                "available_models": ["codellama:13b", "deepseek-coder:6.7b"],
                "selected_model": "codellama:13b",
                "api_key": None,  # Ollama doesn't need API key
                "cost_limit": None,
                "max_tokens": 4096,
                "temperature": 0.3,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            },
            "frontend": {
                "provider": "openai",
                "available_models": ["gpt-4o", "gpt-3.5-turbo"],
                "selected_model": "gpt-4o",
                "api_key": openai_key,
                "cost_limit": 75.0,
                "max_tokens": 2048,
                "temperature": 0.5,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            },
            "issue_fix": {
                "provider": "anthropic",
                "available_models": ["claude-3-sonnet", "claude-3-haiku"],
                "selected_model": "claude-3-sonnet",
                "api_key": anthropic_key,
                "cost_limit": 50.0,
                "max_tokens": 4096,
                "temperature": 0.1,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            },
            "shell": {
                "provider": "ollama",
                "available_models": ["llama2:13b", "codellama:7b"],
                "selected_model": "llama2:13b",
                "api_key": None,  # Ollama doesn't need API key
                "cost_limit": None,
                "max_tokens": 2048,
                "temperature": 0.2,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            }
        },
        "version": "1.0",
        "updated_at": now
    }


class RoleConfigurationManager:
    """Manages role configuration persistence with file locking."""

    @staticmethod
    async def ensure_config_directory() -> None:
        """Ensure configuration directory exists."""
        config_dir = Path(CONFIG_FILE_PATH).parent
        try:
            await aiofiles.os.makedirs(config_dir, exist_ok=True)
            logger.info(f"Configuration directory ensured: {config_dir}")
        except OSError as e:
            logger.error(f"Failed to create config directory {config_dir}: {e}")
            raise ConfigPersistenceError(f"Cannot create config directory: {e}")

    @staticmethod
    async def load_configuration() -> RoleConfigurationList:
        """Load role configuration from file with proper error handling.

        Returns:
            RoleConfigurationList: Loaded configuration or default if file doesn't exist

        Raises:
            ConfigPersistenceError: If configuration cannot be loaded or is invalid
        """
        async with CONFIG_LOCK:
            try:
                await RoleConfigurationManager.ensure_config_directory()

                if not await aiofiles.os.path.exists(CONFIG_FILE_PATH):
                    logger.info("Config file not found, creating default configuration")
                    default_config = create_default_config()
                    config_obj = RoleConfigurationList(**default_config)
                    await RoleConfigurationManager.save_configuration(config_obj)
                    return config_obj

                async with aiofiles.open(CONFIG_FILE_PATH, 'r', encoding='utf-8') as file:
                    content = await file.read()

                if not content.strip():
                    logger.warning("Empty config file, using default configuration")
                    return RoleConfigurationList(**create_default_config())

                config_data = json.loads(content)
                config = RoleConfigurationList(**config_data)
                logger.info(f"Configuration loaded successfully with {len(config.roles)} roles")
                return config

            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in config file: {e}")
                raise ConfigPersistenceError(f"Invalid JSON configuration: {e}")
            except Exception as e:
                logger.error(f"Failed to load configuration: {e}")
                raise ConfigPersistenceError(f"Cannot load configuration: {e}")

    @staticmethod
    async def save_configuration(config: RoleConfigurationList) -> None:
        """Save role configuration to file with atomic write.

        Args:
            config: Configuration to save

        Raises:
            ConfigPersistenceError: If configuration cannot be saved
        """
        async with CONFIG_LOCK:
            try:
                await RoleConfigurationManager.ensure_config_directory()

                # Update timestamp
                config.updated_at = datetime.utcnow().isoformat()

                # Atomic write using temporary file
                temp_path = f"{CONFIG_FILE_PATH}.tmp"

                async with aiofiles.open(temp_path, 'w', encoding='utf-8') as file:
                    await file.write(
                        json.dumps(config.model_dump(), indent=2, ensure_ascii=False)
                    )
                    await file.flush()
                    # Use sync os.fsync since aiofiles.os doesn't have fsync
                    sync_os.fsync(file.fileno())

                # Atomic rename
                await aiofiles.os.rename(temp_path, CONFIG_FILE_PATH)
                logger.info("Configuration saved successfully")

            except Exception as e:
                logger.error(f"Failed to save configuration: {e}")
                # Cleanup temp file if it exists
                try:
                    if await aiofiles.os.path.exists(f"{CONFIG_FILE_PATH}.tmp"):
                        await aiofiles.os.remove(f"{CONFIG_FILE_PATH}.tmp")
                except:
                    pass
                raise ConfigPersistenceError(f"Cannot save configuration: {e}")


# Create router
router = APIRouter(
    prefix="/api/roles",
    tags=["Role Management"],
    responses={
        404: {"description": "Role not found"},
        400: {"description": "Invalid role configuration"},
        500: {"description": "Internal server error"}
    }
)


@router.get(
    "",
    response_model=RoleListResponse,
    summary="Get all role configurations",
    description="Retrieve all AI agent role configurations with their LLM settings"
)
@rate_limit("30/minute")
async def get_all_roles(
    request: Request,
    current_user=Depends(get_current_user)
) -> RoleListResponse:
    """Get all role configurations."""
    try:
        config = await RoleConfigurationManager.load_configuration()

        return RoleListResponse(
            success=True,
            message=f"Retrieved {len(config.roles)} role configurations",
            roles=config.roles,
            total_roles=len(config.roles)
        )

    except ConfigPersistenceError as e:
        logger.error(f"Configuration persistence error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error retrieving roles: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve role configurations")


@router.get(
    "/{role_name}",
    response_model=RoleConfigurationResponse,
    summary="Get specific role configuration",
    description="Retrieve configuration for a specific AI agent role"
)
@rate_limit("60/minute")
async def get_role_configuration(
    role_name: str,
    request: Request,
    current_user=Depends(get_current_user)
) -> RoleConfigurationResponse:
    """Get configuration for a specific role."""
    try:
        config = await RoleConfigurationManager.load_configuration()

        if role_name not in config.roles:
            raise HTTPException(
                status_code=404,
                detail=f"Role '{role_name}' not found"
            )

        return RoleConfigurationResponse(
            success=True,
            message=f"Retrieved configuration for role '{role_name}'",
            role_name=role_name,
            configuration=config.roles[role_name]
        )

    except HTTPException:
        raise
    except ConfigPersistenceError as e:
        logger.error(f"Configuration persistence error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error retrieving role {role_name}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve role configuration")


@router.post(
    "/{role_name}",
    response_model=RoleConfigurationResponse,
    summary="Create new role configuration",
    description="Create a new AI agent role with LLM configuration"
)
@rate_limit("10/minute")
async def create_role_configuration(
    role_name: str,
    role_config: RoleConfiguration,
    request: Request,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user)
) -> RoleConfigurationResponse:
    """Create a new role configuration."""
    try:
        # Validate role name
        if not role_name.replace('_', '').isalnum() or len(role_name) > 50:
            raise HTTPException(
                status_code=400,
                detail="Role name must contain only alphanumeric characters and underscores, max 50 chars"
            )

        config = await RoleConfigurationManager.load_configuration()

        if role_name in config.roles:
            raise HTTPException(
                status_code=400,
                detail=f"Role '{role_name}' already exists. Use PUT to update."
            )

        # Set timestamps
        now = datetime.utcnow().isoformat()
        role_config.created_at = now
        role_config.updated_at = now

        # Add role to configuration
        config.roles[role_name] = role_config

        # Save configuration
        await RoleConfigurationManager.save_configuration(config)

        logger.info(f"Created new role configuration: {role_name}")

        return RoleConfigurationResponse(
            success=True,
            message=f"Successfully created role '{role_name}'",
            role_name=role_name,
            configuration=role_config
        )

    except HTTPException:
        raise
    except InvalidRoleConfigError as e:
        logger.error(f"Invalid role configuration for {role_name}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except ConfigPersistenceError as e:
        logger.error(f"Configuration persistence error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating role {role_name}: {e}")
        raise HTTPException(status_code=500, detail="Failed to create role configuration")


@router.put(
    "/{role_name}",
    response_model=RoleConfigurationResponse,
    summary="Update role configuration",
    description="Update an existing AI agent role configuration"
)
@rate_limit("15/minute")
async def update_role_configuration(
    role_name: str,
    role_update: RoleConfigurationUpdate,
    request: Request,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user)
) -> RoleConfigurationResponse:
    """Update an existing role configuration."""
    try:
        config = await RoleConfigurationManager.load_configuration()

        if role_name not in config.roles:
            raise HTTPException(
                status_code=404,
                detail=f"Role '{role_name}' not found"
            )

        existing_role = config.roles[role_name]

        # Apply updates only for provided fields
        update_data = role_update.model_dump(exclude_unset=True)

        if update_data:
            for field, value in update_data.items():
                setattr(existing_role, field, value)

            # Update timestamp
            existing_role.updated_at = datetime.utcnow().isoformat()

            # Validate the updated configuration
            updated_role = RoleConfiguration(**existing_role.model_dump())
            config.roles[role_name] = updated_role

            # Save configuration
            await RoleConfigurationManager.save_configuration(config)

            logger.info(f"Updated role configuration: {role_name}")

            return RoleConfigurationResponse(
                success=True,
                message=f"Successfully updated role '{role_name}'",
                role_name=role_name,
                configuration=updated_role
            )
        else:
            return RoleConfigurationResponse(
                success=True,
                message=f"No changes provided for role '{role_name}'",
                role_name=role_name,
                configuration=existing_role
            )

    except HTTPException:
        raise
    except InvalidRoleConfigError as e:
        logger.error(f"Invalid role configuration update for {role_name}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except ConfigPersistenceError as e:
        logger.error(f"Configuration persistence error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error updating role {role_name}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update role configuration")


@router.delete(
    "/{role_name}",
    response_model=RoleConfigurationResponse,
    summary="Delete role configuration",
    description="Delete an AI agent role configuration"
)
@rate_limit("5/minute")
async def delete_role_configuration(
    role_name: str,
    request: Request,
    current_user=Depends(get_current_user)
) -> RoleConfigurationResponse:
    """Delete a role configuration."""
    try:
        config = await RoleConfigurationManager.load_configuration()

        if role_name not in config.roles:
            raise HTTPException(
                status_code=404,
                detail=f"Role '{role_name}' not found"
            )

        # Remove role
        deleted_config = config.roles.pop(role_name)

        # Save updated configuration
        await RoleConfigurationManager.save_configuration(config)

        logger.info(f"Deleted role configuration: {role_name}")

        return RoleConfigurationResponse(
            success=True,
            message=f"Successfully deleted role '{role_name}'",
            role_name=role_name,
            configuration=deleted_config
        )

    except HTTPException:
        raise
    except ConfigPersistenceError as e:
        logger.error(f"Configuration persistence error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error deleting role {role_name}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete role configuration")


@router.get(
    "/providers/models",
    response_model=List[ProviderModelsResponse],
    summary="Get available models by provider",
    description="Retrieve available models for each LLM provider"
)
@rate_limit("20/minute")
async def get_provider_models(
    request: Request,
    current_user=Depends(get_current_user)
) -> List[ProviderModelsResponse]:
    """Get available models for each provider."""
    try:
        # This would integrate with the enhanced LLM service
        # For now, returning static data matching common models
        provider_models = [
            ProviderModelsResponse(
                provider=LLMProvider.OLLAMA,
                models=[
                    "llama2:7b", "llama2:13b", "llama2:70b",
                    "codellama:7b", "codellama:13b", "codellama:34b",
                    "deepseek-coder:6.7b", "deepseek-coder:33b",
                    "mistral:7b", "mixtral:8x7b"
                ],
                status="available"
            ),
            ProviderModelsResponse(
                provider=LLMProvider.OPENROUTER,
                models=[
                    "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku",
                    "openai/gpt-4", "openai/gpt-4-turbo", "openai/gpt-3.5-turbo",
                    "meta-llama/llama-2-70b-chat", "mistralai/mistral-7b-instruct",
                    "google/gemini-pro"
                ],
                status="available"
            ),
            ProviderModelsResponse(
                provider=LLMProvider.OPENAI,
                models=[
                    "gpt-4", "gpt-4-turbo", "gpt-4o",
                    "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
                ],
                status="available"
            ),
            ProviderModelsResponse(
                provider=LLMProvider.ANTHROPIC,
                models=[
                    "claude-3-sonnet", "claude-3-haiku", "claude-3-opus",
                    "claude-2.1", "claude-instant-1.2"
                ],
                status="available"
            )
        ]

        return provider_models

    except Exception as e:
        logger.error(f"Error retrieving provider models: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve provider models")


@router.get(
    "/health",
    summary="Health check for role management",
    description="Check the health status of role management system"
)
@rate_limit("60/minute")
async def health_check(
    request: Request
) -> Dict[str, Any]:
    """Health check for role management system."""
    try:
        # Test configuration loading
        config = await RoleConfigurationManager.load_configuration()

        return {
            "status": "healthy",
            "roles_configured": len(config.roles),
            "config_file_accessible": True,
            "last_updated": config.updated_at,
            "version": config.version
        }
    except Exception as e:
        logger.error(f"Role management health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "config_file_accessible": False
        }


# Initialize configuration on startup
async def initialize_role_configuration():
    """Initialize role configuration on service startup."""
    try:
        await RoleConfigurationManager.load_configuration()
        logger.info("Role configuration initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize role configuration: {e}")
        # Don't raise here - let the service start with defaults