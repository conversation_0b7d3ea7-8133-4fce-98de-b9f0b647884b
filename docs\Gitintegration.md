Implementing GitHub Authentication via Supabase and Project Import in Code-Server
Your AI Coding Agent already uses Supabase for authentication, which is perfect because Supabase provides built-in support for GitHub OAuth. Here's how to implement the complete solution:

1. Enable GitHub OAuth in Supabase
First, we need to configure GitHub OAuth in your Supabase project:

Create a new project-importer extension for code-server
Update the AI orchestrator to support GitHub OAuth via Supabase
Create a bridge between code-server and the AI orchestrator for authentication
Let's start by creating the project-importer extension:

// containers/code-server/extensions/project-importer/src/extension.ts

import * as vscode from 'vscode';
import * as path from 'path';
import axios from 'axios';

// Configuration for API endpoints
const API_BASE_URL = process.env.NODE_ENV === 'production'
    ? 'http://ai-orchestrator:8000'
    : 'http://localhost:8000';

/**
 * Main extension activation function
 */
export function activate(context: vscode.ExtensionContext) {
    console.log('Project Importer Extension is now active');

    // Register GitHub authentication command
    const loginWithGithubCommand = vscode.commands.registerCommand(
        'projectImporter.loginWithGithub',
        async () => {
            try {
                // Get the auth URL from the AI orchestrator
                const response = await axios.get(`${API_BASE_URL}/auth/github/url`);
                const authUrl = response.data.url;

                // Open the GitHub auth URL in external browser
                vscode.env.openExternal(vscode.Uri.parse(authUrl));

                // Show information message
                vscode.window.showInformationMessage(
                    'GitHub authentication started in your browser. Please complete the process there.'
                );
            } catch (error) {
                vscode.window.showErrorMessage(`GitHub authentication failed: ${error}`);
            }
        }
    );

    // Register clone repository command
    const cloneRepoCommand = vscode.commands.registerCommand(
        'projectImporter.cloneRepository',
        async () => {
            // Get repository URL from user
            const repoUrl = await vscode.window.showInputBox({
                placeHolder: 'https://github.com/username/repository.git',
                prompt: 'Enter GitHub repository URL'
            });

            if (!repoUrl) return;

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: `Cloning ${repoUrl}`,
                cancellable: false
            }, async (progress) => {
                try {
                    // Execute git clone command
                    const terminal = vscode.window.createTerminal('Git Clone');
                    terminal.sendText(`cd /home/<USER>/workspace && git clone ${repoUrl}`);
                    terminal.show();

                    // Wait for clone to complete
                    await new Promise(resolve => setTimeout(resolve, 5000));

                    vscode.window.showInformationMessage(`Successfully cloned ${repoUrl}`);
                } catch (error) {
                    vscode.window.showErrorMessage(`Failed to clone repository: ${error}`);
                }
            });
        }
    );

    // Register upload project command
    const uploadProjectCommand = vscode.commands.registerCommand(
        'projectImporter.uploadProject',
        async () => {
            // Create a webview panel for file upload
            const panel = vscode.window.createWebviewPanel(
                'projectUpload',
                'Upload Project',
                vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    retainContextWhenHidden: true
                }
            );

            // Set webview HTML content with file upload form
            panel.webview.html = getUploadFormHtml(panel.webview, context.extensionUri);

            // Handle messages from the webview
            panel.webview.onDidReceiveMessage(async (message) => {
                if (message.command === 'uploadFiles') {
                    try {
                        // Show progress notification
                        vscode.window.withProgress({
                            location: vscode.ProgressLocation.Notification,
                            title: 'Uploading project files',
                            cancellable: false
                        }, async () => {
                            // Process the file data
                            const response = await axios.post(
                                `${API_BASE_URL}/projects/upload`,
                                message.files,
                                {
                                    headers: {
                                        'Content-Type': 'application/json'
                                    }
                                }
                            );

                            if (response.status === 200) {
                                vscode.window.showInformationMessage('Project uploaded successfully');
                                panel.dispose();
                            } else {
                                throw new Error('Upload failed');
                            }
                        });
                    } catch (error) {
                        vscode.window.showErrorMessage(`Failed to upload project: ${error}`);
                    }
                }
            });
        }
    );

    // Register welcome view
    const welcomeViewProvider = new ProjectImporterViewProvider(context.extensionUri);
    const welcomeView = vscode.window.registerWebviewViewProvider(
        'projectImporterWelcome',
        welcomeViewProvider
    );

    // Add all commands to subscriptions
    context.subscriptions.push(
        loginWithGithubCommand,
        cloneRepoCommand,
        uploadProjectCommand,
        welcomeView
    );
}

/**
 * Generate HTML for the file upload form
 */
function getUploadFormHtml(webview: vscode.Webview, extensionUri: vscode.Uri): string {
    const scriptUri = webview.asWebviewUri(
        vscode.Uri.joinPath(extensionUri, 'media', 'upload.js')
    );

    return `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Upload Project</title>
        <style>
            body {
                font-family: var(--vscode-font-family);
                padding: 20px;
                color: var(--vscode-foreground);
            }
            .upload-container {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
            .upload-area {
                border: 2px dashed var(--vscode-button-background);
                padding: 40px;
                text-align: center;
                cursor: pointer;
                border-radius: 5px;
            }
            .upload-area:hover {
                background-color: var(--vscode-button-hoverBackground);
            }
            button {
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                padding: 8px 16px;
                cursor: pointer;
                border-radius: 2px;
            }
            button:hover {
                background-color: var(--vscode-button-hoverBackground);
            }
            #file-list {
                margin-top: 20px;
            }
            .file-item {
                display: flex;
                justify-content: space-between;
                padding: 5px 0;
                border-bottom: 1px solid var(--vscode-panel-border);
            }
        </style>
    </head>
    <body>
        <div class="upload-container">
            <h2>Upload Project Files</h2>
            <p>Select files or folders to upload to your workspace</p>

            <div class="upload-area" id="drop-area">
                <p>Drag files here or click to select</p>
                <input type="file" id="file-input" multiple webkitdirectory directory hidden />
            </div>

            <div id="file-list"></div>

            <button id="upload-button" disabled>Upload Project</button>
        </div>

        <script src="${scriptUri}"></script>
    </body>
    </html>`;
}

/**
 * Welcome view provider for project importer
 */
class ProjectImporterViewProvider implements vscode.WebviewViewProvider {
    constructor(private readonly extensionUri: vscode.Uri) {}

    resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        token: vscode.CancellationToken
    ) {
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.extensionUri]
        };

        webviewView.webview.html = this.getHtmlContent(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(message => {
            if (message.command === 'loginWithGithub') {
                vscode.commands.executeCommand('projectImporter.loginWithGithub');
            } else if (message.command === 'cloneRepository') {
                vscode.commands.executeCommand('projectImporter.cloneRepository');
            } else if (message.command === 'uploadProject') {
                vscode.commands.executeCommand('projectImporter.uploadProject');
            }
        });
    }

    private getHtmlContent(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Project Importer</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    padding: 10px;
                    color: var(--vscode-foreground);
                }
                .welcome-container {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }
                .action-button {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    text-align: left;
                }
                .action-button:hover {
                    background-color: var(--vscode-button-secondaryHoverBackground);
                }
                .icon {
                    margin-right: 10px;
                    font-size: 18px;
                }
            </style>
        </head>
        <body>
            <div class="welcome-container">
                <h2>Project Importer</h2>
                <p>Get started by importing a project:</p>

                <button class="action-button" id="github-login">
                    <span class="icon">🔑</span>
                    Login with GitHub
                </button>

                <button class="action-button" id="clone-repo">
                    <span class="icon">📋</span>
                    Clone GitHub Repository
                </button>

                <button class="action-button" id="upload-project">
                    <span class="icon">📤</span>
                    Upload Project from Computer
                </button>
            </div>

            <script>
                (function() {
                    const vscode = acquireVsCodeApi();

                    document.getElementById('github-login').addEventListener('click', () => {
                        vscode.postMessage({ command: 'loginWithGithub' });
                    });

                    document.getElementById('clone-repo').addEventListener('click', () => {
                        vscode.postMessage({ command: 'cloneRepository' });
                    });

                    document.getElementById('upload-project').addEventListener('click', () => {
                        vscode.postMessage({ command: 'uploadProject' });
                    });
                })();
            </script>
        </body>
        </html>`;
    }
}

export function deactivate() {}
Now, let's create the package.json for the project-importer extension:

{
  "name": "project-importer",
  "displayName": "Project Importer",
  "description": "Import projects from GitHub or your computer",
  "version": "1.0.0",
  "publisher": "ai-coding-agent",
  "engines": {
    "vscode": "^1.74.0"
  },
  "categories": [
    "Other"
  ],
  "keywords": [
    "github",
    "import",
    "project",
    "upload",
    "clone"
  ],
  "activationEvents": [
    "onStartupFinished"
  ],
  "main": "./dist/extension.js",
  "browser": "./dist/extension.js",
  "contributes": {
    "commands": [
      {
        "command": "projectImporter.loginWithGithub",
        "title": "Login with GitHub",
        "category": "Project Importer"
      },
      {
        "command": "projectImporter.cloneRepository",
        "title": "Clone GitHub Repository",
        "category": "Project Importer"
      },
      {
        "command": "projectImporter.uploadProject",
        "title": "Upload Project from Computer",
        "category": "Project Importer"
      }
    ],
    "views": {
      "explorer": [
        {
          "id": "projectImporterWelcome",
          "name": "Project Importer",
          "when": "true",
          "icon": "$(cloud-download)",
          "contextualTitle": "Project Importer"
        }
      ]
    },
    "viewsWelcome": [
      {
        "view": "projectImporterWelcome",
        "contents": "Import projects from GitHub or your computer.\n\n[Login with GitHub](command:projectImporter.loginWithGithub)\n[Clone Repository](command:projectImporter.cloneRepository)\n[Upload Project](command:projectImporter.uploadProject)",
        "when": "true"
      }
    ]
  },
  "scripts": {
    "vscode:prepublish": "npm run compile",
    "compile": "tsc -p ./",
    "watch": "tsc -watch -p ./",
    "package": "vsce package",
    "test": "echo \"No tests specified\" && exit 0"
  },
  "devDependencies": {
    "@types/vscode": "^1.74.0",
    "@types/node": "^18.15.0",
    "typescript": "^4.9.4",
    "@vscode/vsce": "^2.15.0"
  },
  "dependencies": {
    "axios": "^1.6.0"
  }
}
Now, let's create the tsconfig.json for the project-importer extension:

{
  "compilerOptions": {
    "module": "commonjs",
    "target": "ES2020",
    "outDir": "dist",
    "lib": ["ES2020", "DOM"],
    "sourceMap": true,
    "rootDir": "src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  },
  "exclude": ["node_modules", ".vscode-test"]
}
Next, let's create the upload.js file for the webview:

// containers/code-server/extensions/project-importer/media/upload.js

(function() {
    const vscode = acquireVsCodeApi();
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('file-input');
    const fileList = document.getElementById('file-list');
    const uploadButton = document.getElementById('upload-button');

    let files = [];

    // Handle drop area click
    dropArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Handle file selection
    fileInput.addEventListener('change', handleFiles);

    // Handle drag and drop
    dropArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropArea.classList.add('active');
    });

    dropArea.addEventListener('dragleave', () => {
        dropArea.classList.remove('active');
    });

    dropArea.addEventListener('drop', (e) => {
        e.preventDefault();
        dropArea.classList.remove('active');

        if (e.dataTransfer.items) {
            // Use DataTransferItemList interface
            for (let i = 0; i < e.dataTransfer.items.length; i++) {
                if (e.dataTransfer.items[i].kind === 'file') {
                    const file = e.dataTransfer.items[i].getAsFile();
                    addFileToList(file);
                }
            }
        } else {
            // Use DataTransfer interface
            for (let i = 0; i < e.dataTransfer.files.length; i++) {
                addFileToList(e.dataTransfer.files[i]);
            }
        }

        updateUploadButton();
    });

    // Handle file selection
    function handleFiles() {
        const selectedFiles = fileInput.files;
        if (!selectedFiles.length) return;

        // Clear previous files
        files = [];
        fileList.innerHTML = '';

        for (let i = 0; i < selectedFiles.length; i++) {
            addFileToList(selectedFiles[i]);
        }

        updateUploadButton();
    }

    // Add file to the list
    function addFileToList(file) {
        // Read file content
        const reader = new FileReader();
        reader.onload = function(e) {
            const fileData = {
                name: file.name,
                path: file.webkitRelativePath || file.name,
                content: e.target.result,
                type: file.type
            };

            files.push(fileData);

            // Add file to UI list
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span>${fileData.path}</span>
                <span>${formatFileSize(file.size)}</span>
            `;
            fileList.appendChild(fileItem);

            updateUploadButton();
        };

        reader.readAsText(file);
    }

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Update upload button state
    function updateUploadButton() {
        uploadButton.disabled = files.length === 0;
    }

    // Handle upload button click
    uploadButton.addEventListener('click', () => {
        if (files.length === 0) return;

        vscode.postMessage({
            command: 'uploadFiles',
            files: files
        });
    });
})();
Now, let's update the AI orchestrator to add GitHub OAuth support via Supabase. First, let's create a new GitHub auth router:

# containers/ai-orchestrator/src/router/github_auth_router.py

from fastapi import APIRouter, HTTPException, Depends, status, Request
from fastapi.responses import RedirectResponse
from pydantic import BaseModel
from typing import Optional
import os
import logging
from ..utils.auth import get_supabase, create_access_token
from datetime import timedelta

logger = logging.getLogger(__name__)

# GitHub OAuth configuration
GITHUB_CLIENT_ID = os.getenv("GITHUB_CLIENT_ID")
GITHUB_CLIENT_SECRET = os.getenv("GITHUB_CLIENT_SECRET")
REDIRECT_URI = os.getenv("GITHUB_REDIRECT_URI", "http://localhost:8000/auth/github/callback")
CODE_SERVER_URL = os.getenv("CODE_SERVER_URL", "http://localhost:8080")

router = APIRouter(prefix="/auth/github", tags=["github-authentication"])

class GitHubAuthResponse(BaseModel):
    url: str

@router.get("/url", response_model=GitHubAuthResponse)
async def github_auth_url():
    """Get GitHub OAuth URL"""
    if not GITHUB_CLIENT_ID:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="GitHub OAuth not configured"
        )

    # Get Supabase client
    supabase = get_supabase()
    if not supabase:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service not available"
        )

    try:
        # Generate GitHub OAuth URL via Supabase
        auth_url = supabase.auth.get_url_for_provider(
            "github",
            {
                "redirect_to": REDIRECT_URI
            }
        )

        return {"url": auth_url}
    except Exception as e:
        logger.error(f"Failed to generate GitHub auth URL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate GitHub auth URL: {str(e)}"
        )

@router.get("/callback")
async def github_callback(code: str, request: Request):
    """Handle GitHub OAuth callback"""
    supabase = get_supabase()
    if not supabase:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service not available"
        )

    try:
        # Exchange code for session via Supabase
        session = supabase.auth.exchange_code_for_session({
            "auth_code": code
        })

        if not session or not session.user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to authenticate with GitHub"
            )

        # Create access token
        access_token_expires = timedelta(hours=24)  # Longer expiry for GitHub auth
        access_token = create_access_token(
            data={
                "sub": session.user.id,
                "email": session.user.email,
                "provider": "github"
            },
            expires_delta=access_token_expires
        )

        # Redirect to code-server with token
        redirect_url = f"{CODE_SERVER_URL}/?token={access_token}"
        return RedirectResponse(url=redirect_url)

    except Exception as e:
        logger.error(f"GitHub callback failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"GitHub authentication failed: {str(e)}"
        )
Now, let's create a project upload endpoint:

# containers/ai-orchestrator/src/router/project_router.py

from fastapi import APIRouter, HTTPException, Depends, status, UploadFile, File, Form, Body
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import os
import logging
import json
import base64
from ..utils.auth import get_current_user

logger = logging.getLogger(__name__)

# Configuration
WORKSPACE_PATH = os.getenv("WORKSPACE_PATH", "/home/<USER>/workspace")

router = APIRouter(prefix="/projects", tags=["projects"])

class FileData(BaseModel):
    name: str
    path: str
    content: str
    type: Optional[str] = None

@router.post("/upload")
async def upload_project(
    files: List[FileData] = Body(...),
    current_user = Depends(get_current_user)
):
    """Upload project files"""
    try:
        # Create user directory if it doesn't exist
        user_dir = os.path.join(WORKSPACE_PATH, f"user_{current_user.id}")
        os.makedirs(user_dir, exist_ok=True)

        # Process each file
        for file_data in files:
            # Create directory structure
            file_path = os.path.join(user_dir, file_data.path)
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Write file content
            with open(file_path, "w") as f:
                f.write(file_data.content)

        return {"message": "Project uploaded successfully", "path": user_dir}

    except Exception as e:
        logger.error(f"Project upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload project: {str(e)}"
        )
Now, let's update the main.py file to include our new routers:

# containers/ai-orchestrator/src/main.py (update)

# Add these imports
from .router.github_auth_router import router as github_auth_router
from .router.project_router import router as project_router

# Add these routers to your app
app.include_router(github_auth_router)
app.include_router(project_router)
Finally, let's update the docker-compose.yml file to make the workspace writable and add GitHub OAuth environment variables:

# Update the code-server service in docker-compose.yml
code-server:
  build:
    context: ./containers/code-server
    dockerfile: Dockerfile
  container_name: code-server
  ports:
    - "${CODE_SERVER_PORT:-8080}:8080"
  volumes:
    - code_server_data:/home/<USER>
    - ./workspace:/home/<USER>/workspace  # Remove :ro to make it writable
    - ./containers/code-server/extensions.json:/home/<USER>/extensions.json:ro
    - ./containers/code-server/settings.json:/home/<USER>/settings.json:ro
    - ./containers/code-server/install-extensions.sh:/home/<USER>/install-extensions.sh:ro
  environment:
    PASSWORD: ${CODE_SERVER_PASSWORD}
    DOCKER_USER: coder
    CODE_SERVER_URL: http://localhost:${CODE_SERVER_PORT:-8080}
  networks:
    - ai-coding-agent-network
  restart: unless-stopped
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8080/login"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 60s

# Update the ai-orchestrator service in docker-compose.yml
ai-orchestrator:
  build:
    context: ./containers/ai-orchestrator
    dockerfile: Dockerfile
  container_name: ai-orchestrator
  ports:
    - "8000:8000"
  volumes:
    - ai_orchestrator_data:/app/data
    - ./workspace:/home/<USER>/workspace  # Add this to access the workspace
  environment:
    DATABASE_URL: ${DATABASE_URL:-postgresql://postgres:${POSTGRES_PASSWORD:-postgres_password}@postgresql:5432/ai_coding_agent}
    REDIS_URL: redis://redis:6379/0
    OLLAMA_BASE_URL: ${OLLAMA_BASE_URL:-http://host.docker.internal:11434}
    # AI Service API Keys
    OPENROUTER_API_KEY: ${OPENROUTER_API_KEY}
    OPENAI_API_KEY: ${OPENAI_API_KEY}
    ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
    # LLM Provider Configuration
    DEFAULT_LOCAL_PROVIDER: ${DEFAULT_LOCAL_PROVIDER:-ollama}
    DEFAULT_CLOUD_PROVIDER: ${DEFAULT_CLOUD_PROVIDER:-openrouter}
    ENABLE_CLOUD_FALLBACK: ${ENABLE_CLOUD_FALLBACK:-true}
    # Supabase & Auth
    SUPABASE_URL: ${SUPABASE_URL}
    SUPABASE_KEY: ${SUPABASE_KEY}
    SUPABASE_SERVICE_KEY: ${SUPABASE_SERVICE_KEY}
    JWT_SECRET: ${JWT_SECRET}
    # GitHub OAuth
    GITHUB_CLIENT_ID: ${GITHUB_CLIENT_ID}
    GITHUB_CLIENT_SECRET: ${GITHUB_CLIENT_SECRET}
    GITHUB_REDIRECT_URI: ${GITHUB_REDIRECT_URI:-http://localhost:8000/auth/github/callback}
    CODE_SERVER_URL: http://localhost:${CODE_SERVER_PORT:-8080}
    # Workspace path
    WORKSPACE_PATH: /home/<USER>/workspace
    PYTHONPATH: /app
  depends_on:
    postgresql:
      condition: service_healthy
    redis:
      condition: service_healthy
  networks:
    - ai-coding-agent-network
  restart: unless-stopped
  healthcheck:
    test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5).raise_for_status()"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 60s
Setting Up Supabase for GitHub OAuth
To complete the implementation, you'll need to:

Log in to your Supabase dashboard
Go to Authentication → Providers
Enable GitHub provider
Register a new OAuth application on GitHub:
Go to GitHub → Settings → Developer settings → OAuth Apps → New OAuth App
Set the Authorization callback URL to your Supabase URL + /auth/v1/callback
Copy the Client ID and Client Secret from GitHub to Supabase
Add these environment variables to your .env file:
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_REDIRECT_URI=http://localhost:8000/auth/github/callback
How This Solution Works
User Flow:

User opens code-server in browser
User clicks "Login with GitHub" in the Project Importer panel
User is redirected to GitHub for authentication
After successful authentication, user is redirected back to code-server with a token
User can now clone repositories or upload projects
Authentication Flow:

Supabase handles the OAuth flow with GitHub
AI orchestrator creates a JWT token after successful authentication
Code-server uses this token for subsequent API calls
Project Import Options:

Clone from GitHub: Uses Git CLI to clone repositories
Upload from computer: Uses a webview with file upload functionality