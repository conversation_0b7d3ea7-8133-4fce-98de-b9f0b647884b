"""
RAG (Retrieval-Augmented Generation) Service for AI Coding Agent.

This module provides comprehensive RAG functionality with permission-aware
document retrieval, context building, prompt engineering, and LLM integration
for code generation and assistance.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import json
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging
import re

# Internal imports
from .supabase_service import SupabaseService
from .auth_service import UserProfile
from .vector_service import VectorStorageService, SearchResult

# Configure logging
logger = logging.getLogger(__name__)


class ContextType(str, Enum):
    """Types of context for RAG."""
    CODE = "code"
    DOCUMENTATION = "documentation"
    EXAMPLES = "examples"
    PATTERNS = "patterns"
    ARCHITECTURE = "architecture"


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OLLAMA = "ollama"
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"


@dataclass
class RAGConfig:
    """Configuration for RAG service."""
    max_context_tokens: int = 4000
    max_search_results: int = 5
    similarity_threshold: float = 0.7
    context_overlap_threshold: float = 0.9
    enable_context_reranking: bool = True
    enable_code_context: bool = True
    enable_hybrid_search: bool = True
    prompt_template_version: str = "v2"
    max_response_tokens: int = 2000
    temperature: float = 0.1

    @classmethod
    def from_env(cls) -> 'RAGConfig':
        """Create configuration from environment variables."""
        import os

        return cls(
            max_context_tokens=int(os.getenv('RAG_MAX_CONTEXT_TOKENS', '4000')),
            max_search_results=int(os.getenv('RAG_MAX_SEARCH_RESULTS', '5')),
            similarity_threshold=float(os.getenv('RAG_SIMILARITY_THRESHOLD', '0.7')),
            context_overlap_threshold=float(os.getenv('RAG_CONTEXT_OVERLAP_THRESHOLD', '0.9')),
            enable_context_reranking=os.getenv('RAG_ENABLE_RERANKING', 'true').lower() == 'true',
            enable_code_context=os.getenv('RAG_ENABLE_CODE_CONTEXT', 'true').lower() == 'true',
            enable_hybrid_search=os.getenv('RAG_ENABLE_HYBRID_SEARCH', 'true').lower() == 'true',
            prompt_template_version=os.getenv('RAG_PROMPT_TEMPLATE_VERSION', 'v2'),
            max_response_tokens=int(os.getenv('RAG_MAX_RESPONSE_TOKENS', '2000')),
            temperature=float(os.getenv('RAG_TEMPERATURE', '0.1'))
        )


@dataclass
class ContextDocument:
    """Document used for context building."""
    id: str
    content: str
    similarity: float
    metadata: Dict[str, Any]
    context_type: ContextType
    token_count: int = 0
    relevance_score: float = 0.0

    def __post_init__(self):
        """Post-initialization processing."""
        if self.token_count == 0:
            # Rough token estimation (4 characters per token)
            self.token_count = len(self.content) // 4

        # Determine context type from metadata
        if not hasattr(self, 'context_type') or self.context_type is None:
            self.context_type = self._infer_context_type()

        # Calculate relevance score
        self.relevance_score = self._calculate_relevance_score()

    def _infer_context_type(self) -> ContextType:
        """Infer context type from content and metadata."""
        file_type = self.metadata.get('file_type', '').lower()
        content_lower = self.content.lower()

        # Check for code patterns
        if any(ext in file_type for ext in ['.py', '.js', '.ts', '.go', '.rs', '.java', '.cpp']):
            return ContextType.CODE
        elif any(keyword in file_type for keyword in ['readme', 'doc', '.md']):
            if any(word in content_lower for word in ['example', 'sample', 'demo']):
                return ContextType.EXAMPLES
            elif any(word in content_lower for word in ['architecture', 'design', 'pattern']):
                return ContextType.ARCHITECTURE
            else:
                return ContextType.DOCUMENTATION
        else:
            # Analyze content patterns
            if re.search(r'(def |function |class |import |from )', self.content):
                return ContextType.CODE
            elif re.search(r'(example|sample|demo)', content_lower):
                return ContextType.EXAMPLES
            else:
                return ContextType.DOCUMENTATION

    def _calculate_relevance_score(self) -> float:
        """Calculate relevance score based on similarity and context type."""
        base_score = self.similarity

        # Boost scores based on context type
        type_boost = {
            ContextType.CODE: 1.2,
            ContextType.EXAMPLES: 1.1,
            ContextType.PATTERNS: 1.15,
            ContextType.ARCHITECTURE: 1.05,
            ContextType.DOCUMENTATION: 1.0
        }

        return base_score * type_boost.get(self.context_type, 1.0)


@dataclass
class RAGContext:
    """Built context for RAG."""
    documents: List[ContextDocument]
    total_tokens: int
    context_types: List[ContextType]
    query: str
    project_id: Optional[str]
    created_at: datetime = field(default_factory=datetime.utcnow)

    def get_context_string(self) -> str:
        """Build formatted context string."""
        context_parts = []

        # Group documents by type
        by_type = {}
        for doc in self.documents:
            if doc.context_type not in by_type:
                by_type[doc.context_type] = []
            by_type[doc.context_type].append(doc)

        # Add sections by type
        for context_type in [ContextType.CODE, ContextType.EXAMPLES, ContextType.PATTERNS, ContextType.ARCHITECTURE, ContextType.DOCUMENTATION]:
            if context_type in by_type:
                context_parts.append(f"\n## {context_type.value.title()} Context\n")
                for doc in by_type[context_type]:
                    context_parts.append(f"```\n{doc.content}\n```\n")

        return '\n'.join(context_parts)


@dataclass
class RAGResponse:
    """RAG response with metadata."""
    response: str
    context_used: List[str]  # Document IDs used
    context_tokens: int
    response_tokens: int
    similarity_scores: List[float]
    llm_provider: str
    processing_time_ms: float
    query: str
    metadata: Dict[str, Any] = field(default_factory=dict)


class RAGError(Exception):
    """RAG service errors."""
    pass


class ContextBuildingError(RAGError):
    """Context building errors."""
    pass


class LLMError(RAGError):
    """LLM integration errors."""
    pass


class RAGService:
    """
    Comprehensive RAG service providing:
    - Permission-aware document retrieval
    - Intelligent context building
    - Prompt engineering and template management
    - Multi-provider LLM integration
    - Code-specific RAG optimization
    """

    def __init__(
        self,
        vector_service: VectorStorageService,
        config: Optional[RAGConfig] = None
    ):
        """
        Initialize RAG service.

        Args:
            vector_service: Vector storage service instance.
            config: RAG configuration.
        """
        self.vector_service = vector_service
        self.config = config or RAGConfig.from_env()

        # Prompt templates
        self._prompt_templates = self._load_prompt_templates()

        logger.info("RAG service initialized")

    # ==================================================================================
    # MAIN RAG OPERATIONS
    # ==================================================================================

    async def generate_code_with_context(
        self,
        query: str,
        user_profile: UserProfile,
        project_id: Optional[str] = None,
        context_types: Optional[List[ContextType]] = None,
        llm_provider: LLMProvider = LLMProvider.OLLAMA,
        model_name: Optional[str] = None
    ) -> RAGResponse:
        """
        Generate code using RAG with user-scoped context.

        Args:
            query: User query/request.
            user_profile: User profile for permissions.
            project_id: Optional project filter.
            context_types: Types of context to include.
            llm_provider: LLM provider to use.
            model_name: Specific model name.

        Returns:
            RAG response with generated content.

        Raises:
            RAGError: If RAG operation fails.
        """
        start_time = datetime.utcnow()

        try:
            logger.info(f"Starting RAG generation for user {user_profile.id}")

            # Build context from user's accessible documents
            context = await self._build_context(
                query=query,
                user_id=user_profile.id,
                project_id=project_id,
                context_types=context_types
            )

            # Generate prompt
            prompt = self._build_prompt(query, context)

            # Call LLM
            response_text, response_tokens = await self._call_llm(
                prompt=prompt,
                provider=llm_provider,
                model_name=model_name
            )

            # Calculate processing time
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            # Build response
            rag_response = RAGResponse(
                response=response_text,
                context_used=[doc.id for doc in context.documents],
                context_tokens=context.total_tokens,
                response_tokens=response_tokens,
                similarity_scores=[doc.similarity for doc in context.documents],
                llm_provider=llm_provider.value,
                processing_time_ms=processing_time,
                query=query,
                metadata={
                    'context_types': [ct.value for ct in context.context_types],
                    'project_id': project_id,
                    'user_id': user_profile.id
                }
            )

            logger.info(f"RAG generation completed in {processing_time:.2f}ms")
            return rag_response

        except Exception as e:
            logger.error(f"RAG generation failed: {str(e)}")
            raise RAGError(f"RAG generation failed: {str(e)}") from e

    async def answer_question_with_context(
        self,
        question: str,
        user_profile: UserProfile,
        project_id: Optional[str] = None,
        llm_provider: LLMProvider = LLMProvider.OLLAMA
    ) -> RAGResponse:
        """
        Answer question using document context.

        Args:
            question: User question.
            user_profile: User profile for permissions.
            project_id: Optional project filter.
            llm_provider: LLM provider to use.

        Returns:
            RAG response with answer.
        """
        return await self.generate_code_with_context(
            query=f"Answer this question: {question}",
            user_profile=user_profile,
            project_id=project_id,
            context_types=[ContextType.DOCUMENTATION, ContextType.CODE, ContextType.EXAMPLES],
            llm_provider=llm_provider
        )

    # ==================================================================================
    # CONTEXT BUILDING
    # ==================================================================================

    async def _build_context(
        self,
        query: str,
        user_id: str,
        project_id: Optional[str] = None,
        context_types: Optional[List[ContextType]] = None
    ) -> RAGContext:
        """Build context from relevant documents."""
        try:
            # Search for relevant documents
            if self.config.enable_hybrid_search:
                search_results = await self.vector_service.hybrid_search(
                    query=query,
                    user_id=user_id,
                    project_id=project_id,
                    similarity_threshold=self.config.similarity_threshold,
                    max_results=self.config.max_search_results * 2  # Get more for filtering
                )
            else:
                search_results = await self.vector_service.search_documents(
                    query=query,
                    user_id=user_id,
                    project_id=project_id,
                    similarity_threshold=self.config.similarity_threshold,
                    max_results=self.config.max_search_results * 2
                )

            # Convert to context documents
            context_docs = []
            for result in search_results:
                context_doc = ContextDocument(
                    id=result.id,
                    content=result.content,
                    similarity=result.similarity,
                    metadata=result.metadata,
                    context_type=ContextType.DOCUMENTATION  # Will be inferred
                )
                context_docs.append(context_doc)

            # Filter by context types if specified
            if context_types:
                context_docs = [
                    doc for doc in context_docs
                    if doc.context_type in context_types
                ]

            # Remove duplicate/overlapping content
            context_docs = self._deduplicate_context(context_docs)

            # Rerank if enabled
            if self.config.enable_context_reranking:
                context_docs = self._rerank_context(context_docs, query)

            # Fit within token budget
            context_docs = self._fit_context_budget(context_docs)

            # Build final context
            total_tokens = sum(doc.token_count for doc in context_docs)
            context_types_used = list(set(doc.context_type for doc in context_docs))

            return RAGContext(
                documents=context_docs,
                total_tokens=total_tokens,
                context_types=context_types_used,
                query=query,
                project_id=project_id
            )

        except Exception as e:
            logger.error(f"Context building failed: {str(e)}")
            raise ContextBuildingError(f"Context building failed: {str(e)}") from e

    def _deduplicate_context(self, context_docs: List[ContextDocument]) -> List[ContextDocument]:
        """Remove duplicate and highly overlapping content."""
        if not context_docs:
            return context_docs

        # Sort by relevance score (highest first)
        context_docs.sort(key=lambda x: x.relevance_score, reverse=True)

        # Remove duplicates based on content similarity
        unique_docs = []
        for doc in context_docs:
            is_duplicate = False
            for existing_doc in unique_docs:
                # Simple overlap check (can be enhanced with more sophisticated methods)
                overlap = self._calculate_content_overlap(doc.content, existing_doc.content)
                if overlap > self.config.context_overlap_threshold:
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_docs.append(doc)

        return unique_docs

    def _calculate_content_overlap(self, content1: str, content2: str) -> float:
        """Calculate content overlap percentage."""
        # Simple word-based overlap calculation
        words1 = set(content1.lower().split())
        words2 = set(content2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _rerank_context(self, context_docs: List[ContextDocument], query: str) -> List[ContextDocument]:
        """Rerank context documents based on query relevance."""
        # Simple keyword-based reranking (can be enhanced with ML models)
        query_words = set(query.lower().split())

        for doc in context_docs:
            doc_words = set(doc.content.lower().split())
            keyword_overlap = len(query_words.intersection(doc_words)) / len(query_words) if query_words else 0

            # Adjust relevance score based on keyword overlap
            doc.relevance_score *= (1 + keyword_overlap * 0.5)

        # Sort by updated relevance score
        context_docs.sort(key=lambda x: x.relevance_score, reverse=True)

        return context_docs

    def _fit_context_budget(self, context_docs: List[ContextDocument]) -> List[ContextDocument]:
        """Fit context within token budget."""
        if not context_docs:
            return context_docs

        selected_docs = []
        total_tokens = 0

        for doc in context_docs:
            if total_tokens + doc.token_count <= self.config.max_context_tokens:
                selected_docs.append(doc)
                total_tokens += doc.token_count
            else:
                # Try to include partial content if possible
                remaining_tokens = self.config.max_context_tokens - total_tokens
                if remaining_tokens > 100:  # Minimum useful context
                    # Truncate content to fit
                    chars_per_token = 4
                    max_chars = remaining_tokens * chars_per_token
                    if len(doc.content) > max_chars:
                        truncated_content = doc.content[:max_chars] + "..."
                        truncated_doc = ContextDocument(
                            id=doc.id,
                            content=truncated_content,
                            similarity=doc.similarity,
                            metadata={**doc.metadata, 'truncated': True},
                            context_type=doc.context_type,
                            token_count=remaining_tokens
                        )
                        selected_docs.append(truncated_doc)
                break

        return selected_docs

    # ==================================================================================
    # PROMPT ENGINEERING
    # ==================================================================================

    def _build_prompt(self, query: str, context: RAGContext) -> str:
        """Build prompt from query and context."""
        template = self._prompt_templates.get(
            self.config.prompt_template_version,
            self._prompt_templates['v2']
        )

        context_string = context.get_context_string()

        # Determine if this is a code generation request
        is_code_request = any(word in query.lower() for word in [
            'code', 'function', 'class', 'implement', 'write', 'create',
            'generate', 'build', 'develop', 'script', 'program'
        ])

        # Select appropriate template section
        if is_code_request:
            template_section = template['code_generation']
        else:
            template_section = template['question_answering']

        # Format prompt
        prompt = template_section.format(
            context=context_string,
            query=query,
            context_types=', '.join([ct.value for ct in context.context_types])
        )

        return prompt

    def _load_prompt_templates(self) -> Dict[str, Dict[str, str]]:
        """Load prompt templates."""
        return {
            'v2': {
                'code_generation': """You are an expert AI coding assistant. Use the provided context from the user's codebase to generate high-quality code that follows the established patterns and practices.

Context from codebase (types: {context_types}):
{context}

User Request:
{query}

Instructions:
1. Analyze the provided context to understand the codebase patterns, style, and architecture
2. Generate code that is consistent with the existing codebase
3. Follow the same naming conventions, patterns, and best practices shown in the context
4. Include appropriate error handling and documentation
5. Ensure the code is production-ready and follows the project's coding standards

Generated Code:""",

                'question_answering': """You are a helpful AI assistant with access to the user's codebase and documentation. Use the provided context to answer the user's question accurately and comprehensively.

Context from codebase (types: {context_types}):
{context}

User Question:
{query}

Instructions:
1. Use the provided context to inform your answer
2. Be specific and reference relevant parts of the codebase when applicable
3. If the context doesn't contain enough information, clearly state what's missing
4. Provide practical, actionable advice
5. Include code examples from the context when relevant

Answer:"""
            },

            'v1': {
                'code_generation': """Based on the following codebase context, generate code for: {query}

Context:
{context}

Code:""",

                'question_answering': """Based on the following context, answer: {query}

Context:
{context}

Answer:"""
            }
        }

    # ==================================================================================
    # LLM INTEGRATION
    # ==================================================================================

    async def _call_llm(
        self,
        prompt: str,
        provider: LLMProvider,
        model_name: Optional[str] = None
    ) -> Tuple[str, int]:
        """
        Call LLM provider for text generation.

        Args:
            prompt: Generated prompt.
            provider: LLM provider to use.
            model_name: Specific model name.

        Returns:
            Tuple of (response_text, token_count).

        Raises:
            LLMError: If LLM call fails.
        """
        try:
            if provider == LLMProvider.OLLAMA:
                return await self._call_ollama(prompt, model_name)
            elif provider == LLMProvider.OPENROUTER:
                return await self._call_openrouter(prompt, model_name)
            elif provider == LLMProvider.OPENAI:
                return await self._call_openai(prompt, model_name)
            elif provider == LLMProvider.ANTHROPIC:
                return await self._call_anthropic(prompt, model_name)
            else:
                raise LLMError(f"Unsupported LLM provider: {provider}")

        except Exception as e:
            logger.error(f"LLM call failed: {str(e)}")
            raise LLMError(f"LLM call failed: {str(e)}") from e

    async def _call_ollama(self, prompt: str, model_name: Optional[str] = None) -> Tuple[str, int]:
        """Call Ollama local LLM."""
        import aiohttp
        import os

        ollama_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
        model = model_name or 'codellama:7b'

        payload = {
            'model': model,
            'prompt': prompt,
            'stream': False,
            'options': {
                'temperature': self.config.temperature,
                'num_predict': self.config.max_response_tokens
            }
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(f"{ollama_url}/api/generate", json=payload) as response:
                if response.status != 200:
                    raise LLMError(f"Ollama API error: {response.status}")

                result = await response.json()
                response_text = result.get('response', '')

                # Estimate tokens (rough approximation)
                token_count = len(response_text.split())

                return response_text, token_count

    async def _call_openrouter(self, prompt: str, model_name: Optional[str] = None) -> Tuple[str, int]:
        """Call OpenRouter API."""
        # Implementation would go here
        # This is a placeholder for the actual OpenRouter integration
        raise LLMError("OpenRouter integration not yet implemented")

    async def _call_openai(self, prompt: str, model_name: Optional[str] = None) -> Tuple[str, int]:
        """Call OpenAI API."""
        # Implementation would go here
        # This is a placeholder for the actual OpenAI integration
        raise LLMError("OpenAI integration not yet implemented")

    async def _call_anthropic(self, prompt: str, model_name: Optional[str] = None) -> Tuple[str, int]:
        """Call Anthropic API."""
        # Implementation would go here
        # This is a placeholder for the actual Anthropic integration
        raise LLMError("Anthropic integration not yet implemented")


# Global service instance
_rag_service: Optional[RAGService] = None


async def get_rag_service() -> RAGService:
    """Get global RAG service instance."""
    global _rag_service

    if _rag_service is None:
        from .vector_service import get_vector_service
        vector_service = await get_vector_service()
        _rag_service = RAGService(vector_service)

    return _rag_service