# Project: AI Coding Agent
# Purpose: Approval system data models for user approval workflows

from datetime import datetime, timedelta
from enum import Enum
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
import uuid


class ApprovalType(str, Enum):
    """Types of approval requests"""
    PHASE_COMPLETION = "phase_completion"
    TASK_EXECUTION = "task_execution"
    DESTRUCTIVE_OPERATION = "destructive_operation"
    CONFIGURATION_CHANGE = "configuration_change"
    DEPLOYMENT = "deployment"
    ROLLBACK = "rollback"
    FILE_MODIFICATION = "file_modification"
    DATABASE_MIGRATION = "database_migration"
    SECURITY_CHANGE = "security_change"
    PRODUCTION_DEPLOY = "production_deploy"


class ApprovalStatus(str, Enum):
    """Status of approval requests"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class RiskLevel(str, Enum):
    """Risk assessment levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ApprovalRequest(BaseModel):
    """User approval request with comprehensive context"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str
    approval_type: ApprovalType

    # Request context
    title: str
    description: str
    item_type: str  # 'phase', 'step', 'task', 'operation'
    item_id: str
    roadmap_id: Optional[str] = None

    # Risk assessment
    risk_level: RiskLevel = RiskLevel.MEDIUM
    risk_assessment: Optional[str] = None
    impact_summary: List[str] = Field(default_factory=list)

    # Change details
    changes_summary: List[str] = Field(default_factory=list)
    files_affected: List[str] = Field(default_factory=list)
    services_affected: List[str] = Field(default_factory=list)

    # Approval configuration
    requires_confirmation: bool = True
    auto_approve_low_risk: bool = False
    timeout_minutes: int = 60

    # Status tracking
    status: ApprovalStatus = ApprovalStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    expires_at: datetime = Field(default_factory=lambda: datetime.now() + timedelta(hours=24))
    responded_at: Optional[datetime] = None

    # Response data
    response_user_id: Optional[str] = None
    response_comments: Optional[str] = None
    response_metadata: Dict[str, Any] = Field(default_factory=dict)

    # Context for UI display
    preview_data: Dict[str, Any] = Field(default_factory=dict)
    rollback_plan: Optional[str] = None
    estimated_duration: Optional[int] = None  # seconds

    @property
    def is_expired(self) -> bool:
        """Check if approval request has expired"""
        return datetime.now() > self.expires_at

    @property
    def is_high_risk(self) -> bool:
        """Check if approval is high risk"""
        return self.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]

    def time_remaining_minutes(self) -> int:
        """Get time remaining in minutes"""
        if self.is_expired:
            return 0
        delta = self.expires_at - datetime.now()
        return max(0, int(delta.total_seconds() / 60))


class ApprovalResponse(BaseModel):
    """Response to an approval request"""
    approval_id: str
    user_id: str
    decision: ApprovalStatus  # APPROVED or REJECTED
    comments: Optional[str] = None

    # Additional response data
    response_metadata: Dict[str, Any] = Field(default_factory=dict)
    conditions: List[str] = Field(default_factory=list)  # Conditional approvals

    # Tracking
    responded_at: datetime = Field(default_factory=datetime.now)
    response_time_seconds: Optional[float] = None


class ApprovalAuditEntry(BaseModel):
    """Audit trail entry for approval actions"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    approval_id: str
    user_id: Optional[str] = None

    # Action details
    action: str  # 'created', 'approved', 'rejected', 'timeout', 'cancelled'
    details: str
    metadata: Dict[str, Any] = Field(default_factory=dict)

    # Context
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

    # Tracking
    timestamp: datetime = Field(default_factory=datetime.now)


class ApprovalConfiguration(BaseModel):
    """Configuration for approval system behavior"""

    # Global settings
    approval_enabled: bool = True
    auto_approve_low_risk: bool = False
    require_approval_for_destructive: bool = True

    # Timeout settings
    default_timeout_minutes: int = 60
    critical_timeout_minutes: int = 1440  # 24 hours

    # Risk-based settings
    risk_thresholds: Dict[str, Dict[str, Any]] = Field(default_factory=dict)

    # User-specific settings
    user_preferences: Dict[str, Dict[str, Any]] = Field(default_factory=dict)

    # Notification settings
    notification_channels: List[str] = Field(default_factory=lambda: ["websocket", "email"])
    escalation_enabled: bool = True
    escalation_timeout_minutes: int = 120


class ApprovalMetrics(BaseModel):
    """Metrics for approval system performance"""

    # Volume metrics
    total_requests: int = 0
    approved_count: int = 0
    rejected_count: int = 0
    timeout_count: int = 0
    cancelled_count: int = 0

    # Timing metrics
    average_response_time_minutes: float = 0.0
    median_response_time_minutes: float = 0.0
    max_response_time_minutes: float = 0.0

    # Risk metrics
    high_risk_requests: int = 0
    auto_approved_count: int = 0

    # User metrics
    active_approvers: int = 0
    most_active_approver: Optional[str] = None

    # System metrics
    system_availability: float = 100.0
    notification_success_rate: float = 100.0

    # Time period
    period_start: datetime = Field(default_factory=datetime.now)
    period_end: datetime = Field(default_factory=datetime.now)


class ApprovalWorkflowStep(BaseModel):
    """Individual step in an approval workflow"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    workflow_id: str
    step_number: int

    # Step configuration
    step_name: str
    step_description: str
    approval_type: ApprovalType
    required: bool = True

    # Conditions
    conditions: List[str] = Field(default_factory=list)
    depends_on_steps: List[str] = Field(default_factory=list)

    # Status
    status: ApprovalStatus = ApprovalStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Approval data
    approval_request_id: Optional[str] = None


class ApprovalWorkflow(BaseModel):
    """Multi-step approval workflow"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str

    # Workflow configuration
    steps: List[ApprovalWorkflowStep]
    parallel_execution: bool = False
    fail_fast: bool = True

    # Context
    roadmap_id: Optional[str] = None
    user_id: str

    # Status
    status: ApprovalStatus = ApprovalStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Progress tracking
    current_step: int = 0
    completed_steps: List[str] = Field(default_factory=list)
    failed_steps: List[str] = Field(default_factory=list)


class ApprovalNotification(BaseModel):
    """Notification for approval events"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    approval_id: str
    user_id: str

    # Notification details
    notification_type: str  # 'request', 'reminder', 'timeout_warning', 'decision'
    channel: str  # 'websocket', 'email', 'sms'

    # Content
    title: str
    message: str
    priority: str = "normal"  # 'low', 'normal', 'high', 'urgent'

    # Delivery
    scheduled_at: datetime = Field(default_factory=datetime.now)
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None

    # Status
    status: str = "pending"  # 'pending', 'sent', 'delivered', 'failed'
    retry_count: int = 0
    max_retries: int = 3