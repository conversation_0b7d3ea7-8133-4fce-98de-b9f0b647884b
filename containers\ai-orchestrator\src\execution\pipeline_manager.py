# Project: AI Coding Agent
# Purpose: Pipeline execution manager with validation gates and sequential execution control

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Callable
from enum import Enum

from ..models.validation_models import (
    ExecutionPipeline, PipelineStage, ValidationGate, ExecutionState,
    ExecutionStatus, ValidationGateStatus, ValidationResult, Task, Step, Phase, Roadmap
)
from ..validation.validation_rules import ValidationRuleEngine
from ..services.error_recovery import ErrorRecoverySystem
from ..core.circuit_breaker import circuit_breaker
from ..core.retry_strategies import with_retry, RetryStrategy


class PipelineExecutionError(Exception):
    """Exception raised during pipeline execution"""
    pass


class ValidationGateError(Exception):
    """Exception raised when validation gate fails"""
    pass


class PipelineManager:
    """
    Manages execution of pipelines with validation gates and dependency resolution.

    Provides:
    - Sequential execution with dependency management
    - Validation gates at critical points
    - Error recovery and retry mechanisms
    - State persistence and recovery
    - Progress tracking and monitoring
    """

    def __init__(self):
        self.logger = logging.getLogger("pipeline_manager")

        # Pipeline storage
        self.pipelines: Dict[str, ExecutionPipeline] = {}
        self.execution_states: Dict[str, ExecutionState] = {}

        # Component services
        self.validation_engine = ValidationRuleEngine()
        self.error_recovery = ErrorRecoverySystem()

        # Execution tracking
        self.active_executions: Set[str] = set()
        self.execution_locks: Dict[str, asyncio.Lock] = {}

        # Performance metrics
        self.execution_metrics: Dict[str, Any] = {
            "total_pipelines": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0
        }

        self.logger.info("Pipeline Manager initialized")

    async def create_pipeline(self,
                             name: str,
                             description: str,
                             roadmap: Roadmap) -> ExecutionPipeline:
        """
        Create execution pipeline from roadmap.

        Args:
            name: Pipeline name
            description: Pipeline description
            roadmap: Source roadmap

        Returns:
            ExecutionPipeline: Created pipeline
        """
        self.logger.info(f"Creating pipeline '{name}' from roadmap")

        # Convert roadmap phases to pipeline stages
        stages = []
        validation_gates = []

        stage_counter = 0
        gate_counter = 0

        for phase in roadmap.phases:
            # Create stage for each phase
            stage = PipelineStage(
                name=f"phase_{phase.title.lower().replace(' ', '_')}",
                description=f"Execute phase: {phase.title}",
                requires_validation=True,
                can_run_parallel=False,
                timeout_seconds=3600,  # 1 hour default
                tasks=[task.id for step in phase.steps for task in step.tasks]
            )

            stages.append(stage)
            stage_counter += 1

            # Add validation gate after each phase if required
            if phase.requires_approval or roadmap.strict_validation:
                gate = ValidationGate(
                    name=f"phase_validation_{gate_counter}",
                    description=f"Validation gate for {phase.title}",
                    gate_type="automatic" if not phase.requires_approval else "approval",
                    blocking=True,
                    validation_rules=["phase_completion", "integration_check"],
                    timeout_seconds=1800 if phase.requires_approval else 300
                )

                validation_gates.append(gate)
                gate_counter += 1

        # Create final validation gate
        final_gate = ValidationGate(
            name="final_validation",
            description="Final roadmap completion validation",
            gate_type="automatic",
            blocking=True,
            validation_rules=["roadmap_completion", "production_readiness"],
            timeout_seconds=600
        )
        validation_gates.append(final_gate)

        # Create pipeline
        pipeline = ExecutionPipeline(
            name=name,
            description=description,
            stages=stages,
            validation_gates=validation_gates,
            roadmap_id=roadmap.id,
            user_id=roadmap.user_id,
            strict_mode=roadmap.strict_validation
        )

        # Initialize execution state
        execution_state = ExecutionState(
            pipeline_id=pipeline.id,
            current_phase="initialized",
            variables={"roadmap_id": roadmap.id, "user_id": roadmap.user_id}
        )

        # Store pipeline and state
        self.pipelines[pipeline.id] = pipeline
        self.execution_states[pipeline.id] = execution_state
        self.execution_locks[pipeline.id] = asyncio.Lock()

        self.logger.info(f"Created pipeline '{name}' with {len(stages)} stages and {len(validation_gates)} gates")
        return pipeline

    async def execute_pipeline(self, pipeline_id: str) -> bool:
        """
        Execute pipeline with validation gates.

        Args:
            pipeline_id: ID of pipeline to execute

        Returns:
            bool: True if pipeline executed successfully

        Raises:
            PipelineExecutionError: If pipeline execution fails
        """
        if pipeline_id not in self.pipelines:
            raise PipelineExecutionError(f"Pipeline not found: {pipeline_id}")

        pipeline = self.pipelines[pipeline_id]
        execution_state = self.execution_states[pipeline_id]

        async with self.execution_locks[pipeline_id]:
            if pipeline_id in self.active_executions:
                raise PipelineExecutionError(f"Pipeline already executing: {pipeline_id}")

            self.active_executions.add(pipeline_id)

            try:
                self.logger.info(f"Starting pipeline execution: {pipeline.name}")

                # Update pipeline status
                pipeline.status = ExecutionStatus.IN_PROGRESS
                pipeline.started_at = datetime.now()
                execution_state.status = ExecutionStatus.IN_PROGRESS
                execution_state.current_phase = "executing"

                # Execute stages sequentially
                success = await self._execute_stages_with_gates(pipeline, execution_state)

                # Update final status
                if success:
                    pipeline.status = ExecutionStatus.COMPLETED
                    execution_state.status = ExecutionStatus.COMPLETED
                    execution_state.current_phase = "completed"
                    execution_state.progress_percentage = 100.0

                    self.logger.info(f"Pipeline execution completed successfully: {pipeline.name}")
                else:
                    pipeline.status = ExecutionStatus.FAILED
                    execution_state.status = ExecutionStatus.FAILED
                    execution_state.current_phase = "failed"

                    self.logger.error(f"Pipeline execution failed: {pipeline.name}")

                pipeline.completed_at = datetime.now()
                execution_state.updated_at = datetime.now()

                # Update metrics
                self._update_execution_metrics(pipeline, success)

                return success

            except Exception as e:
                self.logger.error(f"Pipeline execution error: {str(e)}")

                # Update error state
                pipeline.status = ExecutionStatus.FAILED
                execution_state.status = ExecutionStatus.FAILED
                execution_state.current_phase = "error"
                execution_state.error_context = {
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }

                raise PipelineExecutionError(f"Pipeline execution failed: {str(e)}")

            finally:
                self.active_executions.discard(pipeline_id)

    async def _execute_stages_with_gates(self,
                                       pipeline: ExecutionPipeline,
                                       execution_state: ExecutionState) -> bool:
        """Execute pipeline stages with validation gates"""

        total_stages = len(pipeline.stages)

        for i, stage in enumerate(pipeline.stages):
            try:
                # Update progress
                execution_state.current_stage_id = stage.id
                execution_state.progress_percentage = (i / total_stages) * 100
                execution_state.updated_at = datetime.now()

                self.logger.info(f"Executing stage: {stage.name}")

                # Execute stage
                stage_success = await self._execute_stage(stage, execution_state)

                if not stage_success:
                    self.logger.error(f"Stage execution failed: {stage.name}")
                    return False

                # Check for validation gates after this stage
                applicable_gates = self._get_applicable_gates(pipeline, i)

                for gate in applicable_gates:
                    gate_success = await self._execute_validation_gate(gate, pipeline, execution_state)

                    if not gate_success and gate.blocking:
                        self.logger.error(f"Blocking validation gate failed: {gate.name}")
                        return False
                    elif not gate_success:
                        self.logger.warning(f"Non-blocking validation gate failed: {gate.name}")

                self.logger.info(f"Stage completed successfully: {stage.name}")

            except Exception as e:
                self.logger.error(f"Error executing stage {stage.name}: {str(e)}")

                # Attempt error recovery
                recovery_result = await self.error_recovery.handle_task_failure(
                    Task(title=f"stage_{stage.name}", description="Pipeline stage", type="TESTING", agent_type="ARCHITECT"),
                    e
                )

                if not recovery_result.success:
                    return False

                # Retry stage after recovery
                self.logger.info(f"Retrying stage after recovery: {stage.name}")
                stage_success = await self._execute_stage(stage, execution_state)

                if not stage_success:
                    return False

        return True

    @with_retry("standard", RetryStrategy.EXPONENTIAL_BACKOFF)
    async def _execute_stage(self, stage: PipelineStage, execution_state: ExecutionState) -> bool:
        """Execute individual pipeline stage"""

        stage.status = ExecutionStatus.IN_PROGRESS
        stage.started_at = datetime.now()

        try:
            # Stage execution logic would integrate with existing agent system
            # For now, we simulate successful execution

            # Add some realistic delay
            await asyncio.sleep(0.1)

            # Mark stage as completed
            stage.status = ExecutionStatus.COMPLETED
            stage.completed_at = datetime.now()

            self.logger.debug(f"Stage executed successfully: {stage.name}")
            return True

        except Exception as e:
            stage.status = ExecutionStatus.FAILED
            stage.completed_at = datetime.now()

            self.logger.error(f"Stage execution failed: {stage.name} - {str(e)}")
            raise

    async def _execute_validation_gate(self,
                                     gate: ValidationGate,
                                     pipeline: ExecutionPipeline,
                                     execution_state: ExecutionState) -> bool:
        """Execute validation gate"""

        self.logger.info(f"Executing validation gate: {gate.name}")

        gate.status = ValidationGateStatus.VALIDATING
        gate.opened_at = datetime.now()

        try:
            # Execute validation rules
            validation_results = []

            for rule_name in gate.validation_rules:
                result = await self._execute_validation_rule(rule_name, pipeline, execution_state)
                validation_results.append(result)

            gate.validation_results = validation_results

            # Check if all validations passed
            all_passed = all(result.is_valid for result in validation_results)

            if all_passed:
                gate.status = ValidationGateStatus.PASSED
                self.logger.info(f"Validation gate passed: {gate.name}")
            else:
                gate.status = ValidationGateStatus.FAILED
                failed_rules = [result.error for result in validation_results if not result.is_valid]
                self.logger.warning(f"Validation gate failed: {gate.name} - {'; '.join(failed_rules)}")

            gate.closed_at = datetime.now()
            return all_passed

        except Exception as e:
            gate.status = ValidationGateStatus.FAILED
            gate.closed_at = datetime.now()

            self.logger.error(f"Validation gate error: {gate.name} - {str(e)}")
            return False

    async def _execute_validation_rule(self,
                                     rule_name: str,
                                     pipeline: ExecutionPipeline,
                                     execution_state: ExecutionState) -> ValidationResult:
        """Execute individual validation rule"""

        # This would integrate with the ValidationRuleEngine
        # For now, we simulate validation

        if rule_name == "phase_completion":
            return ValidationResult.success("Phase completed successfully")
        elif rule_name == "integration_check":
            return ValidationResult.success("Integration checks passed")
        elif rule_name == "roadmap_completion":
            return ValidationResult.success("Roadmap execution completed")
        elif rule_name == "production_readiness":
            return ValidationResult.success("System is production ready")
        else:
            return ValidationResult.success(f"Validation rule {rule_name} passed")

    def _get_applicable_gates(self, pipeline: ExecutionPipeline, stage_index: int) -> List[ValidationGate]:
        """Get validation gates applicable after a specific stage"""

        # Simple logic: gates are applied sequentially after each stage
        # More sophisticated logic could be implemented based on gate configuration

        if stage_index < len(pipeline.validation_gates):
            return [pipeline.validation_gates[stage_index]]

        return []

    def _update_execution_metrics(self, pipeline: ExecutionPipeline, success: bool):
        """Update execution metrics"""

        self.execution_metrics["total_pipelines"] += 1

        if success:
            self.execution_metrics["successful_executions"] += 1
        else:
            self.execution_metrics["failed_executions"] += 1

        # Calculate execution time
        if pipeline.started_at and pipeline.completed_at:
            execution_time = (pipeline.completed_at - pipeline.started_at).total_seconds()

            # Update average execution time
            total_executions = self.execution_metrics["total_pipelines"]
            current_avg = self.execution_metrics["average_execution_time"]

            new_avg = ((current_avg * (total_executions - 1)) + execution_time) / total_executions
            self.execution_metrics["average_execution_time"] = new_avg

    # Pipeline management methods

    def get_pipeline(self, pipeline_id: str) -> Optional[ExecutionPipeline]:
        """Get pipeline by ID"""
        return self.pipelines.get(pipeline_id)

    def get_execution_state(self, pipeline_id: str) -> Optional[ExecutionState]:
        """Get execution state by pipeline ID"""
        return self.execution_states.get(pipeline_id)

    def list_pipelines(self) -> List[ExecutionPipeline]:
        """List all pipelines"""
        return list(self.pipelines.values())

    def get_active_executions(self) -> List[str]:
        """Get list of currently executing pipeline IDs"""
        return list(self.active_executions)

    async def pause_pipeline(self, pipeline_id: str) -> bool:
        """Pause pipeline execution"""
        if pipeline_id not in self.pipelines:
            return False

        # Implementation would involve coordinating with execution threads
        self.logger.info(f"Pause requested for pipeline: {pipeline_id}")

        # For now, just update status
        pipeline = self.pipelines[pipeline_id]
        execution_state = self.execution_states[pipeline_id]

        if pipeline.status == ExecutionStatus.IN_PROGRESS:
            pipeline.status = ExecutionStatus.PENDING
            execution_state.status = ExecutionStatus.PENDING
            execution_state.current_phase = "paused"
            return True

        return False

    async def resume_pipeline(self, pipeline_id: str) -> bool:
        """Resume paused pipeline execution"""
        if pipeline_id not in self.pipelines:
            return False

        pipeline = self.pipelines[pipeline_id]
        execution_state = self.execution_states[pipeline_id]

        if pipeline.status == ExecutionStatus.PENDING:
            # Resume execution
            return await self.execute_pipeline(pipeline_id)

        return False

    async def cancel_pipeline(self, pipeline_id: str) -> bool:
        """Cancel pipeline execution"""
        if pipeline_id not in self.pipelines:
            return False

        pipeline = self.pipelines[pipeline_id]
        execution_state = self.execution_states[pipeline_id]

        pipeline.status = ExecutionStatus.CANCELLED
        execution_state.status = ExecutionStatus.CANCELLED
        execution_state.current_phase = "cancelled"

        self.active_executions.discard(pipeline_id)

        self.logger.info(f"Pipeline cancelled: {pipeline_id}")
        return True

    def get_pipeline_progress(self, pipeline_id: str) -> Dict[str, Any]:
        """Get detailed progress information for pipeline"""

        if pipeline_id not in self.pipelines:
            return {}

        pipeline = self.pipelines[pipeline_id]
        execution_state = self.execution_states[pipeline_id]

        # Calculate detailed progress
        completed_stages = sum(1 for stage in pipeline.stages if stage.status == ExecutionStatus.COMPLETED)
        total_stages = len(pipeline.stages)

        passed_gates = sum(1 for gate in pipeline.validation_gates if gate.status == ValidationGateStatus.PASSED)
        total_gates = len(pipeline.validation_gates)

        return {
            "pipeline_id": pipeline_id,
            "name": pipeline.name,
            "status": pipeline.status.value,
            "current_phase": execution_state.current_phase,
            "progress_percentage": execution_state.progress_percentage,
            "completed_stages": completed_stages,
            "total_stages": total_stages,
            "passed_gates": passed_gates,
            "total_gates": total_gates,
            "current_stage_id": execution_state.current_stage_id,
            "started_at": pipeline.started_at.isoformat() if pipeline.started_at else None,
            "estimated_completion": self._estimate_completion_time(pipeline),
            "error_context": execution_state.error_context
        }

    def _estimate_completion_time(self, pipeline: ExecutionPipeline) -> Optional[str]:
        """Estimate pipeline completion time"""

        if not pipeline.started_at or pipeline.status not in [ExecutionStatus.IN_PROGRESS]:
            return None

        # Simple estimation based on average execution time
        elapsed = (datetime.now() - pipeline.started_at).total_seconds()
        avg_time = self.execution_metrics["average_execution_time"]

        if avg_time > 0 and elapsed < avg_time:
            remaining = avg_time - elapsed
            estimated_completion = datetime.now() + timedelta(seconds=remaining)
            return estimated_completion.isoformat()

        return None

    def get_metrics(self) -> Dict[str, Any]:
        """Get pipeline manager metrics"""

        return {
            **self.execution_metrics,
            "active_executions": len(self.active_executions),
            "total_pipelines_stored": len(self.pipelines),
            "success_rate": (
                self.execution_metrics["successful_executions"] /
                max(1, self.execution_metrics["total_pipelines"])
            ) * 100
        }