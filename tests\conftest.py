import pytest
import sys
import os
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import Mock, patch

# Add the src directory to Python path
src_path = os.path.join(os.path.dirname(__file__), '..', 'src')
sys.path.insert(0, src_path)

# Import the app correctly
from src.main import app

@pytest.fixture
def client():
    """Create a test client for the FastAPI app"""
    client = TestClient(app)
    yield client

@pytest.fixture
def mock_supabase():
    """Mock Supabase client for testing"""
    with patch('src.utils.auth.supabase') as mock:
        yield mock

@pytest.fixture
def mock_current_user():
    """Mock current user for authentication tests"""
    user_mock = Mock()
    user_mock.id = "test-user-id"
    user_mock.email = "<EMAIL>"
    return user_mock
