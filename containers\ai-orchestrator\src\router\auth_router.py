from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer
from pydantic import BaseModel
from typing import Optional
from ..utils.auth import (
    get_supabase, create_access_token, TokenData,
    get_current_user, hash_password, verify_password
)
from ..core.config import settings
import logging
from datetime import timedelta

logger = logging.getLogger(__name__)

# Validate Supabase configuration on module load
def _validate_auth_config():
    """Validate authentication configuration."""
    if not settings.supabase_available:
        logger.warning(
            "Supabase configuration incomplete. "
            "Authentication endpoints may not work properly."
        )
        return False
    return True

# Check configuration on import
AUTH_CONFIGURED = _validate_auth_config()

router = APIRouter(prefix="/auth", tags=["authentication"])

class UserCreate(BaseModel):
    email: str
    password: str
    username: Optional[str] = None

class UserLogin(BaseModel):
    email: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

@router.post("/register", response_model=Token)
async def register_user(user: UserCreate):
    """Register a new user"""
    supabase = get_supabase()

    try:
        # Create user in Supabase Auth
        response = supabase.auth.sign_up({
            "email": user.email,
            "password": user.password,
            "options": {
                "data": {
                    "username": user.username
                }
            }
        })

        if not response.user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create user"
            )

        # Create access token
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": response.user.id, "email": response.user.email},
            expires_delta=access_token_expires
        )

        return {"access_token": access_token, "token_type": "bearer"}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Registration failed: {str(e)}"
        )

@router.post("/login", response_model=Token)
async def login_user(user: UserLogin):
    """Login user and return JWT token"""
    supabase = get_supabase()

    try:
        # Sign in with Supabase Auth
        response = supabase.auth.sign_in_with_password({
            "email": user.email,
            "password": user.password
        })

        if not response.user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        # Create access token
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": response.user.id, "email": response.user.email},
            expires_delta=access_token_expires
        )

        return {"access_token": access_token, "token_type": "bearer"}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Login failed: {str(e)}"
        )

@router.post("/logout")
async def logout_user(current_user = Depends(get_current_user)):
    """Logout current user"""
    supabase = get_supabase()

    try:
        supabase.auth.sign_out()
        return {"message": "Successfully logged out"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Logout failed: {str(e)}"
        )

@router.get("/me")
async def get_current_user_info(current_user = Depends(get_current_user)):
    """Get current user information"""
    return {
        "user_id": current_user.id,
        "email": current_user.email,
        "username": getattr(current_user, 'username', None)
    }

@router.get("/protected")
async def protected_route(current_user = Depends(get_current_user)):
    """Example protected route"""
    return {"message": f"Hello {current_user.email}! This is a protected route."}

@router.get("/config")
async def get_auth_config():
    """Get authentication configuration status."""
    return {
        "supabase_configured": AUTH_CONFIGURED,
        "auth_available": AUTH_CONFIGURED,
        "message": "Authentication ready" if AUTH_CONFIGURED else "Authentication not configured"
    }
