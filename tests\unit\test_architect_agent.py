import asyncio
import os
import sys
from typing import Any, Dict, List
from unittest.mock import AsyncMock, patch

import pytest

# Ensure the ai-orchestrator src is importable
REPO_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
AI_SRC = os.path.join(REPO_ROOT, "containers", "ai-orchestrator", "src")
if AI_SRC not in sys.path:
    sys.path.insert(0, AI_SRC)

from sequential_agents.architect import ArchitectAgent  # type: ignore  # noqa: E402
from models.llm_models import LLMResponse, LLMProvider  # type: ignore  # noqa: E402


class _StubLLM:
    def __init__(self, content: str, model: str = "test-model", provider: LLMProvider = LLMProvider.OLLAMA):
        self._resp = LLMResponse(content=content, model=model, provider=provider)

    async def generate(self, *_args, **_kwargs) -> LLMResponse:
        return self._resp


@pytest.mark.asyncio
async def test_architect_agent_creates_tasks_from_plan(monkeypatch):
    # Arrange: stub LLM returning a fenced JSON plan
    plan_json = {
        "plan": [
            {"agent_role": "backend", "task_description": "Create SQLAlchemy models for Post and Comment"},
            {"agent_role": "shell", "task_description": "Run alembic migration for new tables"},
            {"agent_role": "frontend", "task_description": "Build React component to list posts"},
        ]
    }
    fenced = f"""```json\n{__import__('json').dumps(plan_json)}\n```"""
    stub_llm = _StubLLM(content=fenced)

    # Patch LLM service factory
    with patch("router.llm_router.get_llm_service", new=AsyncMock(return_value=stub_llm)):
        # Patch TaskRepository.create_task to return objects with incremental IDs
        created: List[Dict[str, Any]] = []

        async def _fake_create_task(db, *, project_id: int, agent_role: str, input_data: Dict[str, Any]):
            _id = len(created) + 1
            created.append({"id": _id, "project_id": project_id, "agent_role": agent_role, "input_data": input_data})
            class _Task:
                def __init__(self, id_: int):
                    self.id = id_
            return _Task(_id)

        with patch("repository.task_repository.TaskRepository.create_task", new=_fake_create_task):
            agent = ArchitectAgent()
            # Act
            result = await agent.execute({"project_id": 123, "goal": "Create a FastAPI blog backend and UI"})

    # Assert
    assert result["status"] == "success"
    assert result["goal"]
    assert isinstance(result["plan"], list)
    assert len(result["plan"]) == 3
    assert result["created_task_ids"] == [1, 2, 3]


@pytest.mark.asyncio
async def test_architect_agent_requires_project_id():
    agent = ArchitectAgent()
    result = await agent.execute({"goal": "Do something"})
    assert result["status"] == "invalid_input"
    assert "project_id" in result["error"]