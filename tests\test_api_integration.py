import pytest
from unittest.mock import Mock, patch

class TestAPIEndpoints:

    def test_health_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json() == {"status": "ok"}

    def test_root_endpoint(self, client):
        """Test root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "status" in data
        assert data["status"] == "running"

    def test_agents_endpoint_unauthenticated(self, client):
        """Test that agents endpoint requires authentication"""
        response = client.get("/api/agents")
        assert response.status_code == 403 or response.status_code == 401

    def test_models_endpoint_unauthenticated(self, client):
        """Test that models endpoint requires authentication"""
        response = client.get("/api/models")
        assert response.status_code == 403 or response.status_code == 401

    def test_auth_register_endpoint_exists(self, client):
        """Test that auth register endpoint exists"""
        response = client.post("/auth/register", json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "username": "testuser"
        })
        # Should return error from Supabase, not 404
        assert response.status_code != 404

    def test_auth_login_endpoint_exists(self, client):
        """Test that auth login endpoint exists"""
        response = client.post("/auth/login", json={
            "email": "<EMAIL>",
            "password": "testpassword123"
        })
        # Should return error from Supabase, not 404
        assert response.status_code != 404

    def test_auth_protected_endpoint_exists(self, client):
        """Test that auth protected endpoint exists"""
        response = client.get("/auth/protected")
        # Should return 403/401 for unauthenticated, not 404
        assert response.status_code != 404

class TestAuthIntegration:

    @patch('src.utils.auth.get_supabase')
    def test_auth_register_success(self, mock_get_supabase, client):
        """Test successful user registration"""
        # Mock Supabase response
        mock_supabase = Mock()
        mock_response = Mock()
        mock_user = Mock()
        mock_user.id = "test-user-id"
        mock_user.email = "<EMAIL>"
        mock_response.user = mock_user
        mock_supabase.auth.sign_up.return_value = mock_response
        mock_get_supabase.return_value = mock_supabase

        response = client.post("/auth/register", json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "username": "testuser"
        })

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"

    @patch('src.utils.auth.get_supabase')
    def test_auth_login_success(self, mock_get_supabase, client):
        """Test successful user login"""
        # Mock Supabase response
        mock_supabase = Mock()
        mock_response = Mock()
        mock_user = Mock()
        mock_user.id = "test-user-id"
        mock_user.email = "<EMAIL>"
        mock_response.user = mock_user
        mock_supabase.auth.sign_in_with_password.return_value = mock_response
        mock_get_supabase.return_value = mock_supabase

        response = client.post("/auth/login", json={
            "email": "<EMAIL>",
            "password": "testpassword123"
        })

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"

    @patch('src.utils.auth.get_supabase')
    def test_agents_endpoint_authenticated(self, mock_get_supabase, client):
        """Test agents endpoint with valid authentication"""
        # Mock the get_current_user dependency
        with patch('src.utils.auth.get_current_user') as mock_current_user:
            mock_user = Mock()
            mock_user.id = "test-user-id"
            mock_user.email = "<EMAIL>"
            mock_current_user.return_value = mock_user

            response = client.get("/api/agents")
            assert response.status_code == 200
            data = response.json()
            assert "agents" in data

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
