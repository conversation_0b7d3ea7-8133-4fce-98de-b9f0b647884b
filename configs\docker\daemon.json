{"description": "Docker daemon configuration for AI Coding Agent - Production Security & Performance", "version": "1.0.0", "log-driver": "json-file", "log-opts": {"max-size": "100m", "max-file": "5", "compress": "true"}, "storage-driver": "overlay2", "storage-opts": ["overlay2.override_kernel_check=true"], "security-opts": ["seccomp:unconfined", "apparmor:unconfined"], "default-ulimits": {"nofile": {"Name": "nofile", "Hard": 64000, "Soft": 64000}, "memlock": {"Name": "memlock", "Hard": -1, "Soft": -1}}, "max-concurrent-downloads": 10, "max-concurrent-uploads": 5, "userland-proxy": false, "iptables": true, "ip-forward": true, "ip-masq": true, "icc": true, "default-address-pools": [{"base": "**********/16", "size": 24}], "dns": ["*******", "*******"], "registry-mirrors": [], "insecure-registries": [], "experimental": false, "features": {"buildkit": true}, "builder": {"gc": {"enabled": true, "defaultKeepStorage": "20GB", "policy": [{"keepStorage": "10GB", "filter": ["unused-for=2160h"]}]}}, "containerd-snapshotter": "overlayfs"}