/**
 * RoleConfigPanel Component
 * Main container for managing a single role's LLM configuration
 */

import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import {
  CogIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';
import ProviderSelector from './ProviderSelector';
import ModelSelector from './ModelSelector';
import ApiKeyInput from './ApiKeyInput';
import { LLMProvider } from '@/types/role';
import type { RoleConfigPanelProps, FormState } from '@/types/role';

const RoleConfigPanel: React.FC<RoleConfigPanelProps> = ({
  roleName,
  configuration,
  onConfigurationChange,
  onSave,
  isLoading = false,
  error,
}) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const {
    control,
    watch,
    setValue,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<FormState>({
    defaultValues: {
      provider: configuration.provider,
      selected_model: configuration.selected_model,
      available_models: configuration.available_models,
      api_key: configuration.api_key || '',
      cost_limit: configuration.cost_limit,
      max_tokens: configuration.max_tokens || 4096,
      temperature: configuration.temperature || 0.7,
      enabled: configuration.enabled !== false,
    },
    mode: 'onChange',
  });

  // Watch all form values for changes
  const watchedValues = watch();

  // Update hasUnsavedChanges when form becomes dirty
  useEffect(() => {
    setHasUnsavedChanges(isDirty);
  }, [isDirty]);

  // Reset form when configuration prop changes
  useEffect(() => {
    reset({
      provider: configuration.provider,
      selected_model: configuration.selected_model,
      available_models: configuration.available_models,
      api_key: configuration.api_key || '',
      cost_limit: configuration.cost_limit,
      max_tokens: configuration.max_tokens || 4096,
      temperature: configuration.temperature || 0.7,
      enabled: configuration.enabled !== false,
    });
  }, [configuration, reset]);

  // Handle provider change and update available models
  const handleProviderChange = (provider: LLMProvider) => {
    setValue('provider', provider, { shouldDirty: true });

    // Reset selected model when provider changes
    setValue('selected_model', '', { shouldDirty: true });

    // Clear API key for Ollama
    if (provider === LLMProvider.OLLAMA) {
      setValue('api_key', '', { shouldDirty: true });
    }

    // Update parent component
    onConfigurationChange({
      provider,
      selected_model: '',
      api_key: provider === LLMProvider.OLLAMA ? null : watchedValues.api_key,
    });
  };

  // Handle form submission
  const onSubmit = (data: FormState) => {
    const updates = {
      provider: data.provider,
      selected_model: data.selected_model,
      available_models: data.available_models,
      api_key: data.provider === LLMProvider.OLLAMA ? null : data.api_key || null,
      cost_limit: data.cost_limit,
      max_tokens: data.max_tokens,
      temperature: data.temperature,
      enabled: data.enabled,
    };

    onConfigurationChange(updates);
    onSave();
  };

  // Handle reset
  const handleReset = () => {
    reset();
    setHasUnsavedChanges(false);
  };

  // Get role display name
  const roleDisplayName = roleName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return (
    <div className="bg-white rounded-lg shadow-soft border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <CogIcon className="h-6 w-6 text-gray-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {roleDisplayName} Configuration
              </h3>
              <p className="text-sm text-gray-500">
                Configure LLM provider and model for the {roleDisplayName.toLowerCase()} role
              </p>
            </div>
          </div>

          {/* Status indicator */}
          <div className="flex items-center space-x-2">
            {hasUnsavedChanges && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Unsaved Changes
              </span>
            )}

            <span
              className={clsx(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                {
                  'bg-green-100 text-green-800': configuration.enabled && !error,
                  'bg-red-100 text-red-800': !configuration.enabled || error,
                }
              )}
            >
              {configuration.enabled && !error ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {/* Global error */}
        {error && (
          <div className="rounded-md bg-red-50 border border-red-200 p-4">
            <div className="flex items-start">
              <ExclamationCircleIcon className="h-5 w-5 text-red-400 mt-0.5" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Configuration Error
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error.message}</p>
                  {error.details && <p className="mt-1">Details: {error.details}</p>}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Provider Selection */}
        <Controller
          name="provider"
          control={control}
          rules={{ required: 'Provider is required' }}
          render={({ field }) => (
            <ProviderSelector
              value={field.value}
              onChange={handleProviderChange}
              error={errors.provider?.message}
              disabled={isLoading}
            />
          )}
        />

        {/* Model Selection */}
        <Controller
          name="selected_model"
          control={control}
          rules={{ required: 'Model selection is required' }}
          render={({ field }) => (
            <ModelSelector
              value={field.value}
              onChange={(model) => {
                field.onChange(model);
                onConfigurationChange({ selected_model: model });
              }}
              models={[]} // Will use mock data from component
              provider={watchedValues.provider}
              error={errors.selected_model?.message}
              disabled={isLoading || !watchedValues.provider}
            />
          )}
        />

        {/* API Key Input */}
        <Controller
          name="api_key"
          control={control}
          rules={{
            required: watchedValues.provider !== LLMProvider.OLLAMA ? 'API key is required for cloud providers' : false,
          }}
          render={({ field }) => (
            <ApiKeyInput
              value={field.value}
              onChange={field.onChange}
              provider={watchedValues.provider}
              required={watchedValues.provider !== LLMProvider.OLLAMA}
              error={errors.api_key?.message}
              disabled={isLoading}
            />
          )}
        />

        {/* Advanced Settings */}
        <div className="border-t border-gray-200 pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Advanced Settings</h4>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Cost Limit */}
            <div>
              <label htmlFor="cost_limit" className="block text-sm font-medium text-gray-700">
                Monthly Cost Limit (USD)
              </label>
              <Controller
                name="cost_limit"
                control={control}
                rules={{
                  min: { value: 0, message: 'Cost limit must be non-negative' },
                  max: { value: 10000, message: 'Cost limit cannot exceed $10,000' },
                }}
                render={({ field }) => (
                  <input
                    id="cost_limit"
                    type="number"
                    step="0.01"
                    {...field}
                    value={field.value || ''}
                    className={clsx(
                      'mt-1 block w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500',
                      { 'border-red-300': errors.cost_limit }
                    )}
                    placeholder="100.00"
                    disabled={isLoading}
                  />
                )}
              />
              {errors.cost_limit && (
                <p className="mt-1 text-sm text-red-600">{errors.cost_limit.message}</p>
              )}
            </div>

            {/* Max Tokens */}
            <div>
              <label htmlFor="max_tokens" className="block text-sm font-medium text-gray-700">
                Max Tokens
              </label>
              <Controller
                name="max_tokens"
                control={control}
                rules={{
                  required: 'Max tokens is required',
                  min: { value: 1, message: 'Max tokens must be at least 1' },
                  max: { value: 32768, message: 'Max tokens cannot exceed 32,768' },
                }}
                render={({ field }) => (
                  <input
                    id="max_tokens"
                    type="number"
                    {...field}
                    className={clsx(
                      'mt-1 block w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500',
                      { 'border-red-300': errors.max_tokens }
                    )}
                    disabled={isLoading}
                  />
                )}
              />
              {errors.max_tokens && (
                <p className="mt-1 text-sm text-red-600">{errors.max_tokens.message}</p>
              )}
            </div>

            {/* Temperature */}
            <div>
              <label htmlFor="temperature" className="block text-sm font-medium text-gray-700">
                Temperature
              </label>
              <Controller
                name="temperature"
                control={control}
                rules={{
                  required: 'Temperature is required',
                  min: { value: 0, message: 'Temperature must be between 0 and 2' },
                  max: { value: 2, message: 'Temperature must be between 0 and 2' },
                }}
                render={({ field }) => (
                  <input
                    id="temperature"
                    type="number"
                    step="0.1"
                    {...field}
                    className={clsx(
                      'mt-1 block w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500',
                      { 'border-red-300': errors.temperature }
                    )}
                    disabled={isLoading}
                  />
                )}
              />
              {errors.temperature && (
                <p className="mt-1 text-sm text-red-600">{errors.temperature.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Enable/Disable Toggle */}
        <div className="flex items-center">
          <Controller
            name="enabled"
            control={control}
            render={({ field }) => (
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={field.value}
                  onChange={field.onChange}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-offset-0 focus:ring-blue-200 focus:ring-opacity-50"
                  disabled={isLoading}
                />
                <span className="ml-2 text-sm text-gray-900">
                  Enable this role configuration
                </span>
              </label>
            )}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={handleReset}
            disabled={!hasUnsavedChanges || isLoading}
            className={clsx(
              'px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium',
              {
                'text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500':
                  hasUnsavedChanges && !isLoading,
                'text-gray-400 bg-gray-100 cursor-not-allowed':
                  !hasUnsavedChanges || isLoading,
              }
            )}
          >
            Reset
          </button>

          <button
            type="submit"
            disabled={!hasUnsavedChanges || isLoading}
            className={clsx(
              'px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white',
              {
                'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500':
                  hasUnsavedChanges && !isLoading,
                'bg-gray-400 cursor-not-allowed': !hasUnsavedChanges || isLoading,
              }
            )}
          >
            {isLoading ? (
              <>
                <ArrowPathIcon className="h-4 w-4 animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                Save Configuration
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RoleConfigPanel;