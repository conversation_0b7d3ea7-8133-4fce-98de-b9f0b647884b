{"name": "ai-chat-extension", "displayName": "AI Coding Agent <PERSON>", "description": "Real-time chat interface with AI Coding Agents", "version": "1.0.0", "publisher": "ai-coding-agent", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["ai", "chat", "assistant", "coding", "websocket"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "browser": "./dist/extension.js", "contributes": {"commands": [{"command": "aiChatExtension.openChat", "title": "Open AI Chat", "category": "AI Chat"}, {"command": "aiChatExtension.clearHistory", "title": "Clear Chat History", "category": "AI Chat"}, {"command": "aiChatExtension.reconnect", "title": "Reconnect to AI Service", "category": "AI Chat"}], "views": {"explorer": [{"id": "aiChatView", "name": "AI Chat", "when": "true", "icon": "$(comment-discussion)", "contextualTitle": "AI Coding Agent <PERSON>"}]}, "viewsWelcome": [{"view": "aiChatView", "contents": "Welcome to AI Coding Agent Cha<PERSON>!\n\n[Open Chat](command:aiChatExtension.openChat)\n\nChat with AI agents to get help with your coding tasks.", "when": "true"}], "configuration": {"title": "AI Chat", "properties": {"aiChat.serverUrl": {"type": "string", "default": "ws://localhost:8000", "description": "AI Orchestrator WebSocket server URL"}, "aiChat.autoConnect": {"type": "boolean", "default": true, "description": "Automatically connect to AI service on startup"}, "aiChat.maxMessages": {"type": "number", "default": 100, "description": "Maximum number of messages to keep in history"}, "aiChat.maxReconnectAttempts": {"type": "number", "default": 5, "description": "Maximum number of WebSocket reconnection attempts"}, "aiChat.reconnectDelay": {"type": "number", "default": 1000, "description": "Base delay in milliseconds between reconnection attempts"}, "aiChat.heartbeatInterval": {"type": "number", "default": 30000, "description": "Heartbeat interval in milliseconds to keep connection alive"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "test": "echo \"No tests specified\" && exit 0"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "^18.15.0", "typescript": "^4.9.4", "@vscode/vsce": "^2.15.0"}, "dependencies": {}}