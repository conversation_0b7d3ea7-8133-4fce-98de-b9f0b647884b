# Prometheus configuration for AI Coding Agent
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "ai-orchestrator"
    static_configs:
      - targets: ["ai-orchestrator:8000"]
    metrics_path: "/metrics"
    scrape_interval: 30s

  - job_name: "admin-dashboard"
    static_configs:
      - targets: ["admin-dashboard:3000"]
    scrape_interval: 30s

  - job_name: "postgresql"
    static_configs:
      - targets: ["postgresql:9187"]
    scrape_interval: 30s

  - job_name: "redis"
    static_configs:
      - targets: ["redis:9121"]
    scrape_interval: 30s

  - job_name: "ollama"
    static_configs:
      - targets: ["host.docker.internal:11434"]
    scrape_interval: 30s

  - job_name: "code-server"
    static_configs:
      - targets: ["code-server:8080"]
    scrape_interval: 30s

# Alerting rules
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
# Remote write configuration (if needed)
# remote_write:
#   - url: http://remote-storage:9090/api/v1/write
