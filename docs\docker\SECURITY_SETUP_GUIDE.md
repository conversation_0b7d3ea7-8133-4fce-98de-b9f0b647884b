# Docker Compose Security Setup Guide

## Overview

This project now uses a multi-file Docker Compose strategy for enhanced security and flexible deployment:

- **`docker-compose.yml`**: Production-ready base configuration
- **`docker-compose.dev.yml`**: Development overrides with secure port bindings
- **`docker-compose.secrets.yml`**: Enhanced security with Docker secrets

## Security Improvements

### 1. Secure Port Bindings

All development ports are now bound to `127.0.0.1` (localhost only) instead of `0.0.0.0`:

```yaml
ports:
  - "127.0.0.1:8000:8000"  # Only accessible from local machine
```

### 2. Docker Secrets Management

Sensitive data is handled via Docker secrets instead of environment variables:

```bash
# Setup secrets (one-time)
chmod +x scripts/setup-secrets.sh
./scripts/setup-secrets.sh
```

### 3. Network Simplification

Removed complex network driver options for better compatibility across systems.

## Usage Patterns

### Development (Recommended)

```bash
# Secure development with localhost-only access
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# With watch mode for hot-reloading
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch
```

### Production with Secrets

```bash
# Setup secrets first
./scripts/setup-secrets.sh

# Deploy with enhanced security
docker-compose -f docker-compose.yml -f docker-compose.secrets.yml up -d
```

### Development with Secrets

```bash
# Most secure development setup
docker-compose -f docker-compose.yml -f docker-compose.dev.yml -f docker-compose.secrets.yml up -d
```

## Service Access Points

When using development configuration:

- **AI Orchestrator**: http://localhost:8000
- **Admin Dashboard**: http://localhost:3000
- **Code Server**: http://localhost:8080
- **PostgreSQL**: localhost:5432 (development only)
- **Redis**: localhost:6379 (development only)

## Security Best Practices

### Secrets Management

1. **Never commit secrets to version control**
2. **Use strong, unique passwords**
3. **Rotate secrets regularly**
4. **Review secret file permissions (600)**

### Network Security

1. **Development ports bound to localhost only**
2. **Production has no external ports exposed**
3. **Database and cache not accessible externally in production**

### Container Security

1. **Non-root users in all containers**
2. **Health checks for reliability**
3. **Minimal attack surface**

## Migration from Old Setup

If you're upgrading from the previous configuration:

1. **Backup your current .env file**
2. **Run the secrets setup script**
3. **Update your startup commands**
4. **Verify localhost-only access**

```bash
# Backup
cp .env .env.backup

# Setup new security
./scripts/setup-secrets.sh

# Test new configuration
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

## Troubleshooting

### Port Access Issues

If you can't access services, ensure you're using `localhost` or `127.0.0.1`:
- ✅ `http://localhost:8000`
- ❌ `http://0.0.0.0:8000`

### Secrets Not Found

If containers fail to start with secrets:
```bash
# Verify secrets directory
ls -la secrets/
chmod 700 secrets/
chmod 600 secrets/*
```

### Network Connectivity

If services can't communicate:
```bash
# Check network
docker network ls
docker network inspect ai-coding-agent-dev_ai-coding-agent-network
```

## Additional Security Considerations

### Production Deployment

For production environments, consider:
1. **Using external load balancer**
2. **TLS/SSL termination**
3. **Network policies**
4. **Container image scanning**
5. **Regular security updates**

### Monitoring

Enable logging and monitoring:
```bash
# Full stack with monitoring
docker-compose -f docker-compose.yml -f docker-compose.dev.yml -f docker-compose.full.yml up -d
```

## Questions or Issues?

If you encounter problems with the new security setup:
1. Check the troubleshooting section above
2. Verify file permissions on secrets
3. Ensure you're using the correct compose file combination
4. Check container logs for specific error messages