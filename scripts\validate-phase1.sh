#!/bin/bash
# Phase 1 Validation Script - Test Traefik Configuration
# This script validates that Traefik is properly configured and routing works

echo "🚀 Phase 1 Multi-Tenant Architecture Validation"
echo "==============================================="

# Function to check if a service is responding
check_service() {
    local url=$1
    local name=$2
    echo -n "Testing $name at $url... "

    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200\|302\|404"; then
        echo "✅ Service responding"
        return 0
    else
        echo "❌ Service not responding"
        return 1
    fi
}

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

echo ""
echo "🔍 Testing Traefik Configuration:"
echo "--------------------------------"

# Test Traefik dashboard
check_service "http://localhost:8090" "Traefik Dashboard"

# Test API routing through Traefik
check_service "http://api.localhost/api/v1/health" "AI Orchestrator (via Traefik)"

# Test User Portal routing through Traefik
check_service "http://portal.localhost" "User Portal (via Traefik)"

echo ""
echo "📊 Service Access URLs:"
echo "----------------------"
echo "• Traefik Dashboard: http://localhost:8090"
echo "• AI Orchestrator API: http://api.localhost"
echo "• User Portal: http://portal.localhost"
echo ""

# Check Docker containers
echo "🐳 Active Containers:"
echo "--------------------"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(traefik|ai-orchestrator|user-portal)"

echo ""
echo "🎯 Phase 1 Validation Complete!"
echo "Next: Execute Phase 2 to add workspace management capabilities"