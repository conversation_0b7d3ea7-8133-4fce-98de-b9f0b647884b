# System Patterns: AI Coding Agent

## System Architecture

### Container-First Architecture
The AI Coding Agent follows a container-first approach with minimal overhead, utilizing 5 essential containers:
- **ai-orchestrator**: Core AI agent framework and validation engine
- **code-server**: Browser-based VS Code development environment
- **postgresql**: Primary database with pgvector for vector operations
- **redis**: In-memory caching and messaging system
- **ollama**: Local LLM model serving and management
- **monitoring**: Prometheus, Grafana, and ELK stack for observability
- **admin-dashboard**: Next.js-based management interface
- **nginx**: Reverse proxy and load balancing

### Network Architecture
- **Bridge Network**: Custom Docker network for secure service communication
- **Service Isolation**: Each service runs in its own container with defined boundaries
- **Port Management**: Minimal port exposure with nginx reverse proxy
- **Health Checks**: Comprehensive health endpoints for all services

## Key Technical Decisions

### 1. Sequential Agent Architecture
- **Resource Locking**: Mutex mechanisms ensure only one agent executes at a time
- **Agent Hierarchy**: Architect → Frontend → Backend → Shell → Issue Fix workflow
- **State Management**: Redis-based context sharing for agent handoffs
- **Graceful Degradation**: Agents can safely pause and resume operations

### 2. Validation-First Philosophy
- **Multi-Layered Validation**: Pre-execution, execution monitoring, and post-execution validation
- **Risk Assessment**: Automated risk scoring for user approval workflows
- **Rollback Capability**: Checkpoint system for safe state restoration
- **Error Recovery**: LLM-assisted automatic fixing of common issues

### 3. Multi-Provider LLM Strategy
- **Local First**: Ollama for offline development and privacy
- **Cloud Fallback**: OpenRouter, OpenAI, Anthropic for advanced capabilities
- **Model Switching**: Dynamic provider selection based on task requirements
- **Rate Limiting**: Cost control and performance optimization

### 4. Database Design Patterns
- **Vector Operations**: PostgreSQL with pgvector for embedding storage and similarity search
- **Async ORM**: SQLAlchemy 2.0 with asyncpg for non-blocking database operations
- **Migration Strategy**: Alembic for schema versioning and deployment
- **Connection Pooling**: Optimized database connections for container environments

### 5. Official Documentation Integration
- **Reference Documentation**: Comprehensive official documentation references for all key technologies
- **Best Practices**: Implementation patterns and guidelines from official sources
- **Integration Points**: Service communication and data flow patterns documentation

## Design Patterns in Use

### Core Patterns

#### 1. Factory Pattern
- **Agent Factory**: Dynamic agent creation based on task requirements
- **LLM Provider Factory**: Provider selection and configuration
- **Validator Factory**: Validation strategy selection based on context

#### 2. Observer Pattern
- **WebSocket Notifications**: Real-time status updates to clients
- **Event Broadcasting**: System-wide event propagation
- **Progress Tracking**: Continuous feedback during long operations

#### 3. Strategy Pattern
- **Validation Strategies**: Different validation approaches for different risk levels
- **LLM Selection**: Provider choice based on cost, performance, and capability
- **Error Recovery**: Multiple recovery approaches for different error types

#### 4. Decorator Pattern
- **Validation Decorators**: Layered validation without code modification
- **Logging Decorators**: Comprehensive operation tracking
- **Security Decorators**: Authentication and authorization enforcement

### AI-Specific Patterns

#### 1. Chain of Responsibility
- **Agent Chain**: Sequential processing through specialized agents
- **Validation Chain**: Multi-stage validation workflow
- **Error Handling Chain**: Progressive error recovery attempts

#### 2. State Pattern
- **Agent States**: Idle, Processing, Waiting, Error, Completed states
- **Task States**: Pending, In Progress, Validating, Completed, Failed
- **System States**: Initializing, Running, Maintenance, Degraded

#### 3. Template Method Pattern
- **Agent Base Class**: Common agent behavior with customizable steps
- **Validation Framework**: Standard validation流程 with custom validators
- **LLM Interaction**: Consistent prompt/response handling with provider-specific implementations

## Component Relationships

### Core Service Interactions

#### AI Orchestrator ↔ Code Server
- **WebSocket Communication**: Real-time status updates and command execution
- **File System Integration**: Project file access and modification
- **Extension API**: Custom VS Code extension for AI chat and commands

#### AI Orchestrator ↔ Database
- **Async Operations**: Non-blocking database queries and updates
- **Vector Search**: Embedding-based similarity searches for context retrieval
- **State Persistence**: Agent state and conversation context storage

#### AI Orchestrator ↔ Redis
- **Session Management**: User sessions and authentication tokens
- **Message Queuing**: Task queue and inter-agent communication
- **Cache Layer**: Frequently accessed data and computed results

#### AI Orchestrator ↔ LLM Providers
- **Provider Abstraction**: Unified interface for multiple LLM services
- **Fallback Mechanisms**: Automatic switching between providers
- **Rate Limiting**: Cost and performance optimization

### Data Flow Patterns

#### 1. User Request Flow
```
User → Code Server → Architect Agent → Validation → Specialized Agent → Execution → Validation → User
```

#### 2. Agent Handoff Flow
```
Agent A → Save State (Redis) → Release Lock → Agent B → Load State (Redis) → Continue
```

#### 3. Validation Flow
```
Pre-Validation → Execution → Monitoring → Post-Validation → Error Recovery → User Approval
```

## Critical Implementation Paths

### 1. Agent Communication Protocol
- **Lock Acquisition**: Mutex locking before agent execution
- **Context Serialization**: State preservation for handoffs
- **Progress Reporting**: Real-time WebSocket updates
- **Result Validation**: Automated verification of agent output

### 2. LLM Integration Pipeline
- **Provider Selection**: Dynamic choice based on task requirements
- **Prompt Engineering**: Template-based prompt generation
- **Response Processing**: Structured output parsing and validation
- **Cost Management**: Rate limiting and optimization

### 3. Validation Framework
- **Risk Assessment**: Automated scoring of operation risk levels
- **Multi-Stage Validation**: Pre, during, and post-execution checks
- **User Approval Workflow**: Risk-based approval requirements
- **Rollback Mechanism**: Safe state restoration capabilities

### 4. Error Recovery System
- **Error Detection**: Automated identification of issues
- **Recovery Strategy**: LLM-assisted problem solving
- **State Restoration**: Checkpoint-based rollback
- **User Notification**: Clear error reporting and resolution status

## Security Patterns

### 1. Zero Trust Architecture
- **Service-to-Service Authentication**: Mutual TLS and JWT tokens
- **Input Sanitization**: Strict validation of all user inputs
- **Role-Based Access Control**: Fine-grained permission management
- **Audit Logging**: Comprehensive operation tracking

### 2. Container Security
- **Non-Root Execution**: All containers run as non-privileged users
- **Resource Limits**: CPU and memory constraints for isolation
- **Network Segmentation**: Custom networks for service isolation
- **Secrets Management**: Secure handling of API keys and credentials

## Performance Optimization Patterns

### 1. Caching Strategy
- **Redis Caching**: Session data, frequently accessed content
- **Database Connection Pooling**: Optimized database connections
- **LLM Response Caching**: Common query responses
- **File System Caching**: Static assets and templates

### 2. Asynchronous Processing
- **Async/Await**: Non-blocking operations throughout
- **Background Tasks**: Long-running operations without blocking UI
- **WebSocket Communication**: Real-time updates without polling
- **Event-Driven Architecture**: Reactive system design

## Monitoring and Observability Patterns

### 1. Health Check System
- **Service Health**: Individual container health endpoints
- **System Health**: Overall system status and dependencies
- **Performance Metrics**: Response times and resource usage
- **Error Tracking**: Exception monitoring and alerting

### 2. Logging Strategy
- **Structured Logging**: JSON-formatted logs for easy parsing
- **Log Levels**: Appropriate severity levels for different events
- **Centralized Logging**: ELK stack for log aggregation
- **Audit Trails**: Comprehensive operation history

## Scalability Patterns

### 1. Horizontal Scaling
- **Container Replication**: Multiple instances of stateless services
- **Load Balancing**: nginx for traffic distribution
- **Database Sharding**: Future-ready database scaling
- **Caching Layers**: Redis for distributed caching

### 2. Resource Management
- **CPU/Memory Limits**: Container resource constraints
- **Auto-Scaling**: Dynamic resource allocation based on demand
- **Queue Management**: Redis-based task queuing
- **Connection Pooling**: Efficient resource utilization

## Integration Patterns

### 1. API Design
- **RESTful Endpoints**: Standard HTTP API for service communication
- **WebSocket Support**: Real-time bidirectional communication
- **GraphQL Integration**: Flexible data querying (future)
- **Rate Limiting**: API protection and fair usage

### 2. Third-Party Integration
- **Provider Abstraction**: Unified interfaces for external services
- **Fallback Mechanisms**: Graceful degradation when services fail
- **Configuration Management**: Environment-based service configuration
- **Security Integration**: OAuth, JWT, and other authentication protocols
