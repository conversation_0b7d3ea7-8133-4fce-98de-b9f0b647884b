"""
WebSocket Router for AI Coding Agent Chat.

This module provides the WebSocket endpoint for real-time chat communication
with the Architect Agent, following FastAPI WebSocket patterns and the
established architectural guidelines.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from fastapi import <PERSON><PERSON>outer, WebSocket, WebSocketDisconnect, Depends, Query, HTTPException
from fastapi.responses import JSONResponse
from datetime import datetime

# Internal imports
from ..services.websocket_manager import WebSocketChatManager, get_chat_manager, MessageType, ChatMessage
from ..agents.architect import ArchitectAgent
from ..services.enhanced_llm_service import EnhancedLLMService
from ..router.llm_router import get_llm_service
from ..utils.auth import get_current_user

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/ws", tags=["websocket-chat"])

# Global agent instance (will be initialized on startup)
architect_agent: Optional[ArchitectAgent] = None


async def initialize_websocket_chat():
    """Initialize WebSocket chat services and agents."""
    global architect_agent

    try:
        # Initialize Architect Agent
        architect_agent = ArchitectAgent()
        await architect_agent.initialize()

        # Get chat manager and register message handler
        chat_manager = await get_chat_manager()
        chat_manager.register_message_handler(
            MessageType.USER_MESSAGE,
            handle_user_message_for_agent
        )

        logger.info("WebSocket chat services initialized successfully")

    except Exception as e:
        logger.error(f"Failed to initialize WebSocket chat services: {e}")
        raise


async def handle_user_message_for_agent(connection_id: str, chat_message: ChatMessage):
    """
    Handle user messages by routing them to the Architect Agent.

    Args:
        connection_id: WebSocket connection ID
        chat_message: User's chat message
    """
    if not architect_agent:
        logger.error("Architect Agent not initialized")
        return

    try:
        chat_manager = await get_chat_manager()

        # Send typing indicator
        typing_message = {
            "type": MessageType.TYPING.value,
            "content": "Architect Agent is processing your request...",
            "timestamp": datetime.utcnow().isoformat()
        }
        await chat_manager.send_to_connection(connection_id, typing_message)

        # Get response from Architect Agent
        agent_response = await architect_agent.handle_chat_message(
            user_id=chat_message.user_id or "anonymous",
            message=chat_message.content
        )

        # Send agent response back to user
        response_message = {
            "type": MessageType.AGENT_RESPONSE.value,
            "content": agent_response,
            "timestamp": datetime.utcnow().isoformat(),
            "agent": "architect",
            "message_id": chat_message.message_id
        }

        await chat_manager.send_to_connection(connection_id, response_message)

        logger.info(f"Architect Agent responded to user {chat_message.user_id}")

    except Exception as e:
        logger.error(f"Error processing user message: {e}")

        # Send error message to user
        error_message = {
            "type": MessageType.ERROR.value,
            "content": "Sorry, I encountered an error processing your message. Please try again.",
            "timestamp": datetime.utcnow().isoformat(),
            "error_code": "AGENT_ERROR"
        }

        chat_manager = await get_chat_manager()
        await chat_manager.send_to_connection(connection_id, error_message)


@router.websocket("")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    user_id: Optional[str] = Query(default=None, description="User identifier"),
    token: Optional[str] = Query(default=None, description="JWT authentication token"),
    chat_manager: WebSocketChatManager = Depends(get_chat_manager)
):
    """
    Main WebSocket endpoint for chat communication.

    This endpoint handles real-time chat communication between users and the
    Architect Agent. It accepts WebSocket connections, manages user sessions,
    and routes messages to the appropriate handlers.

    Query Parameters:
        user_id: Optional user identifier for session tracking
        token: Optional JWT token for authentication (future use)

    WebSocket Message Format:
        {
            "type": "user_message" | "ping" | "typing",
            "content": "message content",
            "metadata": {} // optional additional data
        }

    Response Message Format:
        {
            "type": "agent_response" | "system_message" | "error" | "pong",
            "content": "response content",
            "timestamp": "ISO format timestamp",
            "agent": "architect", // for agent responses
            "message_id": "uuid" // for agent responses
        }
    """
    connection_id = None

    try:
        # TODO: Implement JWT authentication when needed
        # if token:
        #     user_id = await get_current_user_websocket(token)

        # Connect to chat manager
        connection_id = await chat_manager.connect(websocket, user_id or "anonymous")

        logger.info(f"WebSocket chat connection established: {connection_id}")

        # Message handling loop
        while True:
            try:
                # Receive message from client
                message = await websocket.receive_text()

                # Handle message through chat manager
                await chat_manager.handle_message(connection_id, message)

            except WebSocketDisconnect:
                logger.info(f"WebSocket chat disconnected: {connection_id}")
                break
            except Exception as e:
                logger.error(f"Error in WebSocket message loop: {e}")
                # Send error to client and continue
                error_message = {
                    "type": MessageType.ERROR.value,
                    "content": "An error occurred processing your message",
                    "timestamp": datetime.utcnow().isoformat()
                }
                try:
                    await websocket.send_text(str(error_message))
                except:
                    break

    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        if connection_id:
            await chat_manager.disconnect(connection_id)
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass
    finally:
        # Ensure cleanup
        if connection_id:
            await chat_manager.disconnect(connection_id)


@router.get("/stats")
async def get_websocket_stats(
    chat_manager: WebSocketChatManager = Depends(get_chat_manager)
) -> Dict[str, Any]:
    """
    Get WebSocket connection statistics.

    Returns:
        Dict containing connection statistics and system status
    """
    try:
        stats = chat_manager.get_connection_stats()

        # Add agent status
        agent_status = "not_initialized"
        if architect_agent:
            agent_status = "ready"

        return {
            "websocket_stats": stats,
            "architect_agent_status": agent_status,
            "chat_endpoint": "/ws",
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting WebSocket stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get WebSocket statistics")


@router.post("/broadcast")
async def broadcast_message(
    message: Dict[str, Any],
    chat_manager: WebSocketChatManager = Depends(get_chat_manager)
) -> JSONResponse:
    """
    Broadcast a message to all connected clients.

    This endpoint allows system administrators to send messages to all
    connected chat clients. Useful for system announcements.

    Request Body:
        {
            "content": "message content",
            "type": "system_message" // optional, defaults to system_message
        }
    """
    try:
        broadcast_message = {
            "type": message.get("type", MessageType.SYSTEM_MESSAGE.value),
            "content": message.get("content", ""),
            "timestamp": datetime.utcnow().isoformat(),
            "source": "system_admin"
        }

        sent_count = await chat_manager.broadcast_to_all(broadcast_message)

        return JSONResponse({
            "success": True,
            "message": "Broadcast sent successfully",
            "recipients": sent_count,
            "timestamp": datetime.utcnow().isoformat()
        })

    except Exception as e:
        logger.error(f"Error broadcasting message: {e}")
        raise HTTPException(status_code=500, detail="Failed to broadcast message")


@router.delete("/connections/{connection_id}")
async def disconnect_connection(
    connection_id: str,
    chat_manager: WebSocketChatManager = Depends(get_chat_manager)
) -> JSONResponse:
    """
    Forcefully disconnect a specific WebSocket connection.

    This endpoint allows administrators to disconnect specific connections
    if needed for maintenance or moderation purposes.

    Args:
        connection_id: The WebSocket connection ID to disconnect
    """
    try:
        await chat_manager.disconnect(connection_id)

        return JSONResponse({
            "success": True,
            "message": f"Connection {connection_id} disconnected",
            "timestamp": datetime.utcnow().isoformat()
        })

    except Exception as e:
        logger.error(f"Error disconnecting connection {connection_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to disconnect connection")


# Health check endpoint
@router.get("/health")
async def websocket_health_check(
    chat_manager: WebSocketChatManager = Depends(get_chat_manager)
) -> Dict[str, Any]:
    """
    Health check endpoint for WebSocket chat services.

    Returns:
        Dict containing health status and service information
    """
    try:
        stats = chat_manager.get_connection_stats()

        health_status = {
            "status": "healthy",
            "service": "websocket-chat",
            "architect_agent_ready": architect_agent is not None,
            "active_connections": stats["active_connections"],
            "total_connections": stats["total_connections"],
            "timestamp": datetime.utcnow().isoformat()
        }

        return health_status

    except Exception as e:
        logger.error(f"WebSocket health check failed: {e}")
        return {
            "status": "unhealthy",
            "service": "websocket-chat",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


# Export router and initialization function
__all__ = ["router", "initialize_websocket_chat"]