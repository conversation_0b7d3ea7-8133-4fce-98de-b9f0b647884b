from fastapi import APIRouter
from .auth_router import router as auth_router
from .llm_router import router as llm_router
from .role_management import router as role_router
from .approval_router import router as approval_router

# Create main router that aggregates all sub-routers
# Note: This is an alternative to including routers directly in main.py
router = APIRouter()

# Include all routers
router.include_router(auth_router)
router.include_router(llm_router)
router.include_router(role_router)
router.include_router(approval_router)
