from fastapi import APIRouter, HTTPException, Depends, status, Body
from fastapi.responses import JSONResponse
from pydantic import BaseModel, validator
from typing import List, Dict, Any, Optional
import logging
from ..utils.auth import get_current_user
from ..repository.project_repository import ProjectRepository, get_project_repository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/projects", tags=["projects"])

class FileData(BaseModel):
    name: str
    path: str
    content: str
    type: Optional[str] = None

    @validator('name', 'path')
    def validate_file_paths(cls, v: str) -> str:
        """Validate file paths for security"""
        if '..' in v or v.startswith('/'):
            raise ValueError('Invalid file path: path traversal not allowed')
        return v

class ProjectUploadResponse(BaseModel):
    message: str
    path: str
    files_processed: int

class GitCloneRequest(BaseModel):
    repository_url: str
    target_directory: Optional[str] = None
    branch: Optional[str] = None

    @validator('repository_url')
    def validate_git_url(cls, v: str) -> str:
        """Validate Git repository URL"""
        if not (v.startswith('https://github.com/') or
                v.startswith('https://gitlab.com/') or
                v.startswith('**************:') or
                v.endswith('.git')):
            raise ValueError('Invalid Git repository URL')
        return v

class GitCloneResponse(BaseModel):
    message: str
    path: str
    repository_url: str

@router.get("/workspace/info")
async def get_workspace_info(
    current_user = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository)
) -> Dict[str, Any]:
    """Get workspace information"""
    try:
        user_id = getattr(current_user, 'id', 'unknown')
        workspace_info = await project_repo.get_workspace_info(user_id)
        return workspace_info
    except Exception as e:
        logger.error(f"Failed to get workspace info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get workspace info: {str(e)}"
        )

@router.post("/upload", response_model=ProjectUploadResponse)
async def upload_project(
    files: List[FileData] = Body(...),
    current_user = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository)
) -> ProjectUploadResponse:
    """Upload project files to user workspace"""
    try:
        user_id = getattr(current_user, 'id', 'unknown')

        # Convert FileData objects to dictionaries for the repository
        file_dicts = [
            {
                "name": file_data.name,
                "path": file_data.path,
                "content": file_data.content,
                "type": file_data.type
            }
            for file_data in files
        ]

        result = await project_repo.upload_project_files(user_id, file_dicts)
        return ProjectUploadResponse(**result)

    except Exception as e:
        logger.error(f"Project upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload project: {str(e)}"
        )

@router.post("/clone", response_model=GitCloneResponse)
async def clone_repository(
    request: GitCloneRequest,
    current_user = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository)
) -> GitCloneResponse:
    """Clone a Git repository to user workspace"""
    try:
        user_id = getattr(current_user, 'id', 'unknown')

        result = await project_repo.clone_git_repository(
            user_id=user_id,
            repository_url=request.repository_url,
            target_directory=request.target_directory,
            branch=request.branch
        )

        return GitCloneResponse(**result)

    except Exception as e:
        logger.error(f"Repository cloning failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clone repository: {str(e)}"
        )

@router.get("/list")
async def list_user_projects(
    current_user = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository)
) -> Dict[str, Any]:
    """List all projects in user workspace"""
    try:
        user_id = getattr(current_user, 'id', 'unknown')
        projects = await project_repo.list_user_projects(user_id)
        return {"projects": projects}
    except Exception as e:
        logger.error(f"Failed to list projects: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list projects: {str(e)}"
        )

@router.delete("/delete/{project_name}")
async def delete_project(
    project_name: str,
    current_user = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository)
) -> Dict[str, str]:
    """Delete a project from user workspace"""
    try:
        user_id = getattr(current_user, 'id', 'unknown')
        result = await project_repo.delete_project(user_id, project_name)
        return result
    except Exception as e:
        logger.error(f"Failed to delete project: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete project: {str(e)}"
        )