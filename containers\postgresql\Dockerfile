# syntax=docker/dockerfile:1
# PostgreSQL with pgvector extension
# Custom Dockerfile for AI Coding Agent database requirements

FROM pgvector/pgvector:pg15

# Install additional tools for debugging and monitoring
USER root
RUN apt-get update && apt-get install -y \
  postgresql-contrib \
  curl \
  && rm -rf /var/lib/apt/lists/*

# Create custom configuration directory
RUN mkdir -p /etc/postgresql/conf.d && \
  chown postgres:postgres /etc/postgresql/conf.d

# Copy custom configuration
COPY postgresql.conf /etc/postgresql/conf.d/postgresql.conf

# Copy initialization scripts
COPY init-scripts/ /docker-entrypoint-initdb.d/

# Set proper permissions
RUN chown -R postgres:postgres /docker-entrypoint-initdb.d/

# Switch back to postgres user
USER postgres

# Configure default database settings for AI workloads
ENV POSTGRES_DB=ai_coding_agent
ENV POSTGRES_USER=postgres
ENV POSTGRES_INITDB_ARGS="--auth-host=scram-sha-256 --auth-local=scram-sha-256 --data-checksums"

# Expose PostgreSQL port
EXPOSE 5432

# Health check for PostgreSQL service
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD pg_isready -U $POSTGRES_USER -d $POSTGRES_DB || exit 1

# Set security and project labels
LABEL org.opencontainers.image.title="AI Coding Agent - PostgreSQL" \
  org.opencontainers.image.description="PostgreSQL database with pgvector extension for AI operations" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="postgres"

# Use the default entrypoint from pgvector image
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["postgres"]