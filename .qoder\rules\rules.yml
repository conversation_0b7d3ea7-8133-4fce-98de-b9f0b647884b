rules:
  # ------------------------
  # Headers
  # ------------------------
  - id: compose-dev-header
    description: Add header to dev Compose files
    applies_to: ["**/docker-compose.dev.yml"]
    auto_fix: true
    enforce: |
      # Development override file for Docker Compose
      # Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch

  - id: compose-prod-header
    description: Add header to prod Compose file
    applies_to: ["**/docker-compose.yml"]
    auto_fix: true
    enforce: |
      # Main production Docker Compose file
      # Usage: docker-compose -f docker-compose.yml up -d

  - id: python-header
    description: Add project header to Python files
    applies_to: ["**/*.py"]
    auto_fix: true
    enforce: |
      # Project: AI Coding Agent
      # Author: [Your Name]
      # Purpose: <Describe file purpose here>

  - id: js-header
    description: Add project header to JS/TS files
    applies_to: ["**/*.js", "**/*.ts"]
    auto_fix: true
    enforce: |
      // Project: AI Coding Agent
      // Author: [Your Name]
      // Purpose: <Describe file purpose here>

  # ------------------------
  # Docker Compose Best Practices
  # ------------------------
  - id: compose-volumes
    description: Ensure volumes are declared properly
    applies_to: ["**/docker-compose*.yml"]
    auto_fix: false
    enforce: |
      All volumes should be explicitly declared and avoid overwriting node_modules/.next

  - id: compose-watch
    description: Ensure develop.watch is configured for dev Compose
    applies_to: ["**/docker-compose.dev.yml"]
    auto_fix: true
    enforce: |
      Each service should have develop.watch configured for sync/rebuild/restart

  # ------------------------
  # Formatting / Linting
  # ------------------------
  - id: python-format
    description: Ensure Python files are formatted with black
    applies_to: ["**/*.py"]
    auto_fix: true
    enforce: |
      Run black on all Python files

  - id: js-format
    description: Ensure JS/TS files are formatted with Prettier
    applies_to: ["**/*.js", "**/*.ts"]
    auto_fix: true
    enforce: |
      Run prettier on all JS/TS files

  # ------------------------
  # General File Hygiene
  # ------------------------
  - id: trailing-whitespace
    description: Remove trailing whitespace from all files
    applies_to: ["**/*.*"]
    auto_fix: true
    enforce: |
      No trailing whitespace allowed

  - id: end-of-file-newline
    description: Ensure all files end with a newline
    applies_to: ["**/*.*"]
    auto_fix: true
    enforce: |
      Every file must end with a single newline character

  # ------------------------
  # Optional Advanced Rules
  # ------------------------
  - id: python-import-order
    description: Ensure Python imports are sorted with isort
    applies_to: ["**/*.py"]
    auto_fix: true
    enforce: |
      Run isort on all Python files

  - id: markdown-header
    description: Ensure Markdown files have project info header
    applies_to: ["**/*.md"]
    auto_fix: true
    enforce: |
      <!-- Project: AI Coding Agent -->
      <!-- Author: [Your Name] -->
      <!-- Purpose: <Describe file purpose here> -->
