import * as vscode from 'vscode';
import * as path from 'path';
import axios from 'axios';

// Configuration for API endpoints
const API_BASE_URL = process.env.NODE_ENV === 'production'
    ? 'http://ai-orchestrator:8000'
    : 'http://localhost:8000';

/**
 * Main extension activation function
 */
export function activate(context: vscode.ExtensionContext) {
    console.log('Project Importer Extension is now active');

    // Register GitHub authentication command
    const loginWithGithubCommand = vscode.commands.registerCommand(
        'projectImporter.loginWithGithub',
        async () => {
            try {
                // Get the auth URL from the AI orchestrator
                const response = await axios.get(`${API_BASE_URL}/auth/github/url`);
                const authUrl = response.data.url;

                // Open the GitHub auth URL in external browser
                vscode.env.openExternal(vscode.Uri.parse(authUrl));

                // Show information message
                vscode.window.showInformationMessage(
                    'GitHub authentication started in your browser. Please complete the process there.'
                );
            } catch (error) {
                vscode.window.showErrorMessage(`GitHub authentication failed: ${error}`);
            }
        }
    );

    // Register clone repository command
    const cloneRepoCommand = vscode.commands.registerCommand(
        'projectImporter.cloneRepository',
        async () => {
            // Get repository URL from user
            const repoUrl = await vscode.window.showInputBox({
                placeHolder: 'https://github.com/username/repository.git',
                prompt: 'Enter GitHub repository URL'
            });

            if (!repoUrl) return;

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: `Cloning ${repoUrl}`,
                cancellable: false
            }, async (progress) => {
                try {
                    // Execute git clone command
                    const terminal = vscode.window.createTerminal('Git Clone');
                    terminal.sendText(`cd /home/<USER>/workspace && git clone ${repoUrl}`);
                    terminal.show();

                    // Wait for clone to complete
                    await new Promise(resolve => setTimeout(resolve, 5000));

                    vscode.window.showInformationMessage(`Successfully cloned ${repoUrl}`);
                } catch (error) {
                    vscode.window.showErrorMessage(`Failed to clone repository: ${error}`);
                }
            });
        }
    );

    // Register upload project command
    const uploadProjectCommand = vscode.commands.registerCommand(
        'projectImporter.uploadProject',
        async () => {
            // Create a webview panel for file upload
            const panel = vscode.window.createWebviewPanel(
                'projectUpload',
                'Upload Project',
                vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    retainContextWhenHidden: true
                }
            );

            // Set webview HTML content with file upload form
            panel.webview.html = getUploadFormHtml(panel.webview, context.extensionUri);

            // Handle messages from the webview
            panel.webview.onDidReceiveMessage(async (message) => {
                if (message.command === 'uploadFiles') {
                    try {
                        // Show progress notification
                        vscode.window.withProgress({
                            location: vscode.ProgressLocation.Notification,
                            title: 'Uploading project files',
                            cancellable: false
                        }, async () => {
                            // Process the file data
                            const response = await axios.post(
                                `${API_BASE_URL}/projects/upload`,
                                message.files,
                                {
                                    headers: {
                                        'Content-Type': 'application/json'
                                    }
                                }
                            );

                            if (response.status === 200) {
                                vscode.window.showInformationMessage('Project uploaded successfully');
                                panel.dispose();
                            } else {
                                throw new Error('Upload failed');
                            }
                        });
                    } catch (error) {
                        vscode.window.showErrorMessage(`Failed to upload project: ${error}`);
                    }
                }
            });
        }
    );

    // Register welcome view
    const welcomeViewProvider = new ProjectImporterViewProvider(context.extensionUri);
    const welcomeView = vscode.window.registerWebviewViewProvider(
        'projectImporterWelcome',
        welcomeViewProvider
    );

    // Add all commands to subscriptions
    context.subscriptions.push(
        loginWithGithubCommand,
        cloneRepoCommand,
        uploadProjectCommand,
        welcomeView
    );
}

/**
 * Generate HTML for the file upload form
 */
function getUploadFormHtml(webview: vscode.Webview, extensionUri: vscode.Uri): string {
    const scriptUri = webview.asWebviewUri(
        vscode.Uri.joinPath(extensionUri, 'media', 'upload.js')
    );

    return `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Upload Project</title>
        <style>
            body {
                font-family: var(--vscode-font-family);
                padding: 20px;
                color: var(--vscode-foreground);
            }
            .upload-container {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }
            .upload-area {
                border: 2px dashed var(--vscode-button-background);
                padding: 40px;
                text-align: center;
                cursor: pointer;
                border-radius: 5px;
            }
            .upload-area:hover {
                background-color: var(--vscode-button-hoverBackground);
            }
            button {
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                padding: 8px 16px;
                cursor: pointer;
                border-radius: 2px;
            }
            button:hover {
                background-color: var(--vscode-button-hoverBackground);
            }
            #file-list {
                margin-top: 20px;
            }
            .file-item {
                display: flex;
                justify-content: space-between;
                padding: 5px 0;
                border-bottom: 1px solid var(--vscode-panel-border);
            }
        </style>
    </head>
    <body>
        <div class="upload-container">
            <h2>Upload Project Files</h2>
            <p>Select files or folders to upload to your workspace</p>

            <div class="upload-area" id="drop-area">
                <p>Drag files here or click to select</p>
                <input type="file" id="file-input" multiple webkitdirectory directory hidden />
            </div>

            <div id="file-list"></div>

            <button id="upload-button" disabled>Upload Project</button>
        </div>

        <script src="${scriptUri}"></script>
    </body>
    </html>`;
}

/**
 * Welcome view provider for project importer
 */
class ProjectImporterViewProvider implements vscode.WebviewViewProvider {
    constructor(private readonly extensionUri: vscode.Uri) {}

    resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        token: vscode.CancellationToken
    ) {
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.extensionUri]
        };

        webviewView.webview.html = this.getHtmlContent(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(message => {
            if (message.command === 'loginWithGithub') {
                vscode.commands.executeCommand('projectImporter.loginWithGithub');
            } else if (message.command === 'cloneRepository') {
                vscode.commands.executeCommand('projectImporter.cloneRepository');
            } else if (message.command === 'uploadProject') {
                vscode.commands.executeCommand('projectImporter.uploadProject');
            }
        });
    }

    private getHtmlContent(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Project Importer</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    padding: 10px;
                    color: var(--vscode-foreground);
                }
                .welcome-container {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }
                .action-button {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    text-align: left;
                }
                .action-button:hover {
                    background-color: var(--vscode-button-secondaryHoverBackground);
                }
                .icon {
                    margin-right: 10px;
                    font-size: 18px;
                }
            </style>
        </head>
        <body>
            <div class="welcome-container">
                <h2>Project Importer</h2>
                <p>Get started by importing a project:</p>

                <button class="action-button" id="github-login">
                    <span class="icon">🔑</span>
                    Login with GitHub
                </button>

                <button class="action-button" id="clone-repo">
                    <span class="icon">📋</span>
                    Clone GitHub Repository
                </button>

                <button class="action-button" id="upload-project">
                    <span class="icon">📤</span>
                    Upload Project from Computer
                </button>
            </div>

            <script>
                (function() {
                    const vscode = acquireVsCodeApi();

                    document.getElementById('github-login').addEventListener('click', () => {
                        vscode.postMessage({ command: 'loginWithGithub' });
                    });

                    document.getElementById('clone-repo').addEventListener('click', () => {
                        vscode.postMessage({ command: 'cloneRepository' });
                    });

                    document.getElementById('upload-project').addEventListener('click', () => {
                        vscode.postMessage({ command: 'uploadProject' });
                    });
                })();
            </script>
        </body>
        </html>`;
    }
}

export function deactivate() {}