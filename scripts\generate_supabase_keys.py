#!/usr/bin/env python3
"""
Generate Supabase Authentication Keys.

This script generates the necessary JWT secrets and API keys for Supabase
self-hosted deployment, compatible with Windows environment.

Author: AI Coding Agent
Version: 1.0.0
"""

import jwt
import secrets
import json
import os
from datetime import datetime, timedelta


def generate_jwt_secret() -> str:
    """Generate a secure JWT secret."""
    return secrets.token_urlsafe(32)


def generate_anon_key(jwt_secret: str) -> str:
    """Generate anonymous user API key."""
    payload = {
        "iss": "supabase-demo",
        "role": "anon",
        "exp": int((datetime.utcnow() + timedelta(days=3650)).timestamp())  # 10 years
    }
    return jwt.encode(payload, jwt_secret, algorithm="HS256")


def generate_service_role_key(jwt_secret: str) -> str:
    """Generate service role API key."""
    payload = {
        "iss": "supabase-demo",
        "role": "service_role",
        "exp": int((datetime.utcnow() + timedelta(days=3650)).timestamp())  # 10 years
    }
    return jwt.encode(payload, jwt_secret, algorithm="HS256")


def generate_postgres_password() -> str:
    """Generate secure PostgreSQL password."""
    return secrets.token_urlsafe(32)


def main():
    """Generate all Supabase keys and display them."""
    print("🔐 Generating Supabase Authentication Keys...")
    print("=" * 60)

    # Generate secrets
    jwt_secret = generate_jwt_secret()
    postgres_password = generate_postgres_password()
    anon_key = generate_anon_key(jwt_secret)
    service_role_key = generate_service_role_key(jwt_secret)

    # Display keys
    print("\n📝 Generated Keys (copy these to your .env.supabase file):")
    print("-" * 60)
    print(f"JWT_SECRET={jwt_secret}")
    print(f"POSTGRES_PASSWORD={postgres_password}")
    print(f"ANON_KEY={anon_key}")
    print(f"SERVICE_ROLE_KEY={service_role_key}")

    # Save to file
    keys_config = {
        "jwt_secret": jwt_secret,
        "postgres_password": postgres_password,
        "anon_key": anon_key,
        "service_role_key": service_role_key,
        "generated_at": datetime.utcnow().isoformat()
    }

    output_file = "supabase_keys.json"
    with open(output_file, 'w') as f:
        json.dump(keys_config, f, indent=2)

    print(f"\n💾 Keys saved to: {output_file}")
    print("\n⚠️  IMPORTANT SECURITY NOTES:")
    print("   - Keep these keys secure and never commit them to version control")
    print("   - Add supabase_keys.json to your .gitignore file")
    print("   - Use these keys to update your .env.supabase file")

    print("\n🚀 Next steps:")
    print("   1. Copy the keys above to your .env.supabase file")
    print("   2. Run: docker-compose -f docker-compose.supabase.yml up -d")
    print("   3. Test the setup with the provided test scripts")


if __name__ == "__main__":
    main()