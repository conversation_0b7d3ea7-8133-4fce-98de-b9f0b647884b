# Ollama Host Setup Guide

This guide helps you set up Ollama on your host computer to work with the AI Coding Agent containers.

## 🎯 Overview

Instead of running Ollama in a Docker container, we'll run it directly on your host machine and configure the containerized AI services to connect to it via `host.docker.internal`.

**Benefits:**
- Better performance (no container overhead)
- Easier model management
- Persistent models across container restarts
- Direct access to GPU if available

## 📋 Setup Steps

### 1. Install Ollama on Host

**Windows:**
1. Download Ollama from https://ollama.ai
2. Run the installer
3. Ollama will start automatically and run on `http://localhost:11434`

**macOS:**
1. Download Ollama from https://ollama.ai
2. Install the app
3. Launch Ollama from Applications

**Linux:**
```bash
curl -fsSL https://ollama.ai/install.sh | sh
sudo systemctl enable ollama
sudo systemctl start ollama
```

### 2. Pull Recommended Models

Pull at least one model for testing:

```bash
# Small, fast model (good for testing)
ollama pull llama3.2:1b

# Balanced model (recommended for development)
ollama pull llama3.2

# Larger model (better quality, slower)
ollama pull llama3.1:8b
```

### 3. Verify Installation

Test that Ollama is working:

```bash
# Check running models
curl http://localhost:11434/api/tags

# Test generation
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{"model": "llama3.2", "prompt": "Hello!", "stream": false}'
```

Or run our test script:
```bash
python scripts/test-ollama-connection.py
```

### 4. Configure Environment Variables

Update your `.env` file:

```bash
# Copy the example if you haven't already
cp .env.example .env

# The .env file should contain:
OLLAMA_BASE_URL=http://host.docker.internal:11434
```

### 5. Start the AI Coding Agent

Now start the containerized services (without Ollama):

```bash
docker-compose up -d
```

The `ai-orchestrator` container will connect to your host Ollama via `host.docker.internal:11434`.

## 🔍 Testing the Connection

### From the Container

Test the connection from within the AI Orchestrator:

```bash
# Check Ollama status endpoint
curl http://localhost:8000/api/ollama/status

# List available models
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/api/models
```

### Available API Endpoints

The following endpoints are available for Ollama integration:

- `GET /api/ollama/status` - Check connection to host Ollama
- `GET /api/models` - List all available models (requires auth)
- `POST /api/ollama/pull` - Pull a new model (requires auth)
- `POST /api/generate` - Generate text using LLM (requires auth)

## 🛠 Troubleshooting

### Connection Issues

**Problem**: Container cannot connect to host Ollama

**Solutions:**
1. Verify Ollama is running: `curl http://localhost:11434/api/tags`
2. Check Docker Desktop is running
3. On Windows: Ensure `host.docker.internal` resolves
4. Check firewall settings aren't blocking port 11434

**Test connectivity from container:**
```bash
docker exec -it ai-orchestrator curl http://host.docker.internal:11434/api/tags
```

### Docker Desktop on Windows

If `host.docker.internal` doesn't work:

1. Open Docker Desktop
2. Go to Settings → Resources → Network
3. Enable "Use the WSL 2 based engine" if using WSL2
4. Restart Docker Desktop

### Alternative Host Access

If `host.docker.internal` doesn't work, find your host IP:

**Windows:**
```powershell
ipconfig | findstr "IPv4"
```

**macOS/Linux:**
```bash
ifconfig | grep inet
```

Then update `.env`:
```
OLLAMA_BASE_URL=http://YOUR_HOST_IP:11434
```

## 📊 Model Management

### Recommended Models by Use Case

**Development & Testing:**
- `llama3.2:1b` - Fastest, smallest (1.3GB)
- `llama3.2` - Balanced (2.0GB)

**Production Quality:**
- `llama3.1:8b` - High quality (4.7GB)
- `codellama:7b` - Specialized for code (3.8GB)

**Specialized:**
- `codellama:code` - Code completion
- `llama3.1:70b` - Highest quality (requires 40GB+ RAM)

### Model Commands

```bash
# List available models online
ollama list

# Pull a specific model
ollama pull model_name

# Remove a model
ollama rm model_name

# Show model information
ollama show model_name
```

## 🔒 Security Considerations

- Ollama runs on localhost by default (secure)
- Only containers can access via `host.docker.internal`
- API endpoints require JWT authentication
- Consider using HTTPS in production

## 🚀 Next Steps

Once Ollama is working with your containers:

1. Test the `/api/generate` endpoint
2. Configure model assignments for different AI agents
3. Set up cloud provider fallback (OpenRouter, OpenAI)
4. Explore Phase 3: Core AI Agent Framework

## 💡 Tips

- Use smaller models during development for faster responses
- Keep at least one model pulled for testing
- Monitor system resources when running large models
- Consider GPU acceleration for better performance

---

✅ **Success**: When you can call `http://localhost:8000/api/ollama/status` and get a connected response, you're ready for the next phase!