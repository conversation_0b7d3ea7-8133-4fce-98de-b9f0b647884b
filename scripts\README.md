# Scripts Folder

This folder contains utility scripts for the project.

## Guidelines
- Store automation, backup, and migration scripts here.
- Document usage and parameters for each script.
- Ensure scripts follow security best practices.

## Development Workflow

### Streamlined Development
For daily development with essential services only:
```bash
# Start streamlined development environment
docker-compose -f docker-compose.dev.yml up -d

# Execute commands in the development container
docker-compose -f docker-compose.dev.yml exec dev-and-backend bash
```

### Full-Featured Debugging
For debugging with monitoring and logging stack:
```bash
# Start full environment (streamlined + monitoring)
docker-compose -f docker-compose.dev.yml -f docker-compose.full.yml up -d
```

## Validation Tools

This project includes several validation tools to ensure code quality and consistency:

### File Naming Validation
- **Script**: `validate_naming.py`
- **Purpose**: Ensures files and directories follow naming conventions (hyphens for directories/files, underscores for Python modules)
- **Usage**: `python scripts/validate_naming.py`

### Directory Structure Validation
- **Script**: `validate_structure.py`
- **Purpose**: Validates that the project structure matches the expected layout defined in the roadmap
- **Usage**: `python scripts/validate_structure.py`

### Dockerfile Linting
- **Script**: `lint_dockerfiles.sh`
- **Purpose**: Lints all Dockerfiles using hadolint to ensure best practices
- **Usage**: `./scripts/lint_dockerfiles.sh`

## Pre-commit Hooks

The project uses pre-commit hooks to automatically run validation tools before commits:

1. Install pre-commit: `pip install pre-commit`
2. Install hooks: `pre-commit install`
3. Hooks will run automatically on `git commit`

The hooks include:
- Code style checking (black, isort, flake8)
- File naming validation
- Directory structure validation
