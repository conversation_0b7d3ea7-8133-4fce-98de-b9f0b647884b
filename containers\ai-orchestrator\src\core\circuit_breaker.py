# Project: AI Coding Agent
# Purpose: Circuit breaker pattern implementation for service resilience

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Callable, Optional, Union, List
from enum import Enum

from ..models.validation_models import CircuitBreakerState, CircuitBreakerConfig


class CircuitBreakerError(Exception):
    """Exception raised when circuit breaker is open"""
    pass


class CircuitBreaker:
    """
    Circuit breaker implementation for preventing cascading failures.

    The circuit breaker monitors failures and prevents calls to failing services
    to allow them time to recover.
    """

    def __init__(self,
                 name: str,
                 config: Optional[CircuitBreakerConfig] = None):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self.logger = logging.getLogger(f"circuit_breaker.{name}")

        # State tracking
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.state_changed_at = datetime.now()

        # Call tracking for monitoring window
        self.call_history: list = []  # Recent calls for monitoring

        # Metrics
        self.total_calls = 0
        self.total_failures = 0
        self.total_successes = 0

        self.logger.info(f"Circuit breaker '{name}' initialized in {self.state.value} state")

    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a function through the circuit breaker.

        Args:
            func: The function to execute
            *args, **kwargs: Arguments to pass to the function

        Returns:
            The result of the function call

        Raises:
            CircuitBreakerError: If the circuit breaker is open
        """
        await self._update_state()

        if self.state == CircuitBreakerState.OPEN:
            self.logger.warning(f"Circuit breaker '{self.name}' is OPEN, rejecting call")
            raise CircuitBreakerError(f"Circuit breaker '{self.name}' is open")

        self.total_calls += 1
        call_start = time.time()

        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)

            # Success case
            await self._record_success()
            call_duration = time.time() - call_start

            self._record_call(success=True, duration=call_duration)
            self.logger.debug(f"Circuit breaker '{self.name}' - successful call ({call_duration:.3f}s)")

            return result

        except Exception as e:
            # Failure case
            await self._record_failure(e)
            call_duration = time.time() - call_start

            self._record_call(success=False, duration=call_duration, error=str(e))
            self.logger.warning(f"Circuit breaker '{self.name}' - failed call: {str(e)}")

            raise

    async def _update_state(self):
        """Update circuit breaker state based on current conditions"""
        now = datetime.now()

        if self.state == CircuitBreakerState.OPEN:
            # Check if we should transition to HALF_OPEN
            if (self.last_failure_time and
                (now - self.last_failure_time).total_seconds() >= self.config.recovery_timeout_seconds):

                await self._transition_to_half_open()

        elif self.state == CircuitBreakerState.HALF_OPEN:
            # In HALF_OPEN, we allow limited calls to test recovery
            # If we get enough successes, we close the breaker
            if self.success_count >= self.config.success_threshold:
                await self._transition_to_closed()

    async def _record_success(self):
        """Record a successful call"""
        self.total_successes += 1

        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            self.logger.debug(f"Circuit breaker '{self.name}' - success count: {self.success_count}")

            if self.success_count >= self.config.success_threshold:
                await self._transition_to_closed()
        elif self.state == CircuitBreakerState.CLOSED:
            # Reset failure count on success
            self.failure_count = 0

    async def _record_failure(self, error: Exception):
        """Record a failed call"""
        self.total_failures += 1
        self.failure_count += 1
        self.last_failure_time = datetime.now()

        if self.state == CircuitBreakerState.CLOSED:
            if self.failure_count >= self.config.failure_threshold:
                await self._transition_to_open()
        elif self.state == CircuitBreakerState.HALF_OPEN:
            # Single failure in HALF_OPEN takes us back to OPEN
            await self._transition_to_open()

    async def _transition_to_open(self):
        """Transition to OPEN state"""
        old_state = self.state
        self.state = CircuitBreakerState.OPEN
        self.state_changed_at = datetime.now()

        self.logger.warning(f"Circuit breaker '{self.name}' transitioned from {old_state.value} to OPEN "
                           f"(failures: {self.failure_count})")

    async def _transition_to_half_open(self):
        """Transition to HALF_OPEN state"""
        old_state = self.state
        self.state = CircuitBreakerState.HALF_OPEN
        self.state_changed_at = datetime.now()
        self.success_count = 0  # Reset success counter

        self.logger.info(f"Circuit breaker '{self.name}' transitioned from {old_state.value} to HALF_OPEN")

    async def _transition_to_closed(self):
        """Transition to CLOSED state"""
        old_state = self.state
        self.state = CircuitBreakerState.CLOSED
        self.state_changed_at = datetime.now()
        self.failure_count = 0  # Reset failure counter
        self.success_count = 0  # Reset success counter

        self.logger.info(f"Circuit breaker '{self.name}' transitioned from {old_state.value} to CLOSED")

    def _record_call(self, success: bool, duration: float, error: Optional[str] = None):
        """Record call in history for monitoring"""
        call_record = {
            'timestamp': time.time(),
            'success': success,
            'duration': duration,
            'error': error
        }

        self.call_history.append(call_record)

        # Keep only recent calls within monitoring window
        cutoff_time = time.time() - self.config.monitoring_window_seconds
        self.call_history = [call for call in self.call_history if call['timestamp'] > cutoff_time]

    def get_metrics(self) -> Dict[str, Any]:
        """Get circuit breaker metrics"""
        now = time.time()
        cutoff_time = now - self.config.monitoring_window_seconds

        recent_calls = [call for call in self.call_history if call['timestamp'] > cutoff_time]
        recent_failures = [call for call in recent_calls if not call['success']]
        recent_successes = [call for call in recent_calls if call['success']]

        success_rate = (len(recent_successes) / len(recent_calls)) * 100 if recent_calls else 0

        avg_duration = sum(call['duration'] for call in recent_calls) / len(recent_calls) if recent_calls else 0

        return {
            'name': self.name,
            'state': self.state.value,
            'failure_count': self.failure_count,
            'success_count': self.success_count,
            'total_calls': self.total_calls,
            'total_failures': self.total_failures,
            'total_successes': self.total_successes,
            'success_rate_percent': round(success_rate, 2),
            'recent_calls_count': len(recent_calls),
            'recent_failures_count': len(recent_failures),
            'avg_call_duration_seconds': round(avg_duration, 3),
            'state_changed_at': self.state_changed_at.isoformat(),
            'last_failure_time': self.last_failure_time.isoformat() if self.last_failure_time else None
        }

    def is_healthy(self) -> bool:
        """Check if circuit breaker indicates the service is healthy"""
        return self.state == CircuitBreakerState.CLOSED

    async def reset(self):
        """Manually reset the circuit breaker to CLOSED state"""
        self.logger.info(f"Manually resetting circuit breaker '{self.name}'")
        await self._transition_to_closed()

    async def force_open(self):
        """Manually force the circuit breaker to OPEN state"""
        self.logger.warning(f"Manually forcing circuit breaker '{self.name}' to OPEN")
        await self._transition_to_open()


class CircuitBreakerRegistry:
    """Registry for managing multiple circuit breakers"""

    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.logger = logging.getLogger("circuit_breaker_registry")

    def create_circuit_breaker(self,
                             name: str,
                             config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        """Create and register a new circuit breaker"""
        if name in self.circuit_breakers:
            self.logger.warning(f"Circuit breaker '{name}' already exists, returning existing instance")
            return self.circuit_breakers[name]

        circuit_breaker = CircuitBreaker(name, config)
        self.circuit_breakers[name] = circuit_breaker

        self.logger.info(f"Created circuit breaker '{name}'")
        return circuit_breaker

    def get_circuit_breaker(self, name: str) -> Optional[CircuitBreaker]:
        """Get circuit breaker by name"""
        return self.circuit_breakers.get(name)

    def remove_circuit_breaker(self, name: str) -> bool:
        """Remove circuit breaker from registry"""
        if name in self.circuit_breakers:
            del self.circuit_breakers[name]
            self.logger.info(f"Removed circuit breaker '{name}'")
            return True
        return False

    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get metrics for all circuit breakers"""
        return {name: cb.get_metrics() for name, cb in self.circuit_breakers.items()}

    def get_unhealthy_services(self) -> List[str]:
        """Get list of services with unhealthy circuit breakers"""
        return [name for name, cb in self.circuit_breakers.items() if not cb.is_healthy()]

    async def reset_all(self):
        """Reset all circuit breakers"""
        for name, cb in self.circuit_breakers.items():
            await cb.reset()
            self.logger.info(f"Reset circuit breaker '{name}'")

    def get_summary(self) -> Dict[str, Any]:
        """Get summary of all circuit breakers"""
        total = len(self.circuit_breakers)
        healthy = sum(1 for cb in self.circuit_breakers.values() if cb.is_healthy())

        return {
            'total_circuit_breakers': total,
            'healthy_count': healthy,
            'unhealthy_count': total - healthy,
            'circuit_breakers': list(self.circuit_breakers.keys())
        }


# Global registry instance
circuit_breaker_registry = CircuitBreakerRegistry()


# Decorator for automatic circuit breaker integration
def circuit_breaker(name: str, config: Optional[CircuitBreakerConfig] = None):
    """
    Decorator to automatically wrap functions with circuit breaker protection.

    Usage:
        @circuit_breaker("external_api", config=CircuitBreakerConfig(failure_threshold=3))
        async def call_external_api():
            # API call code
            pass
    """
    def decorator(func):
        cb = circuit_breaker_registry.create_circuit_breaker(name, config)

        async def wrapper(*args, **kwargs):
            return await cb.call(func, *args, **kwargs)

        # Preserve function metadata
        wrapper.__name__ = func.__name__
        wrapper.__doc__ = func.__doc__
        wrapper.circuit_breaker = cb

        return wrapper

    return decorator