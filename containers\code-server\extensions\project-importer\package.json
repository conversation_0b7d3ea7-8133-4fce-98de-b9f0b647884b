{"name": "project-importer", "displayName": "Project Importer", "description": "Import projects from GitHub or your computer", "version": "1.0.0", "publisher": "ai-coding-agent", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["github", "import", "project", "upload", "clone"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "browser": "./dist/extension.js", "contributes": {"commands": [{"command": "projectImporter.loginWithGithub", "title": "Login with GitHub", "category": "Project Importer"}, {"command": "projectImporter.cloneRepository", "title": "Clone Repository", "category": "Project Importer"}, {"command": "projectImporter.uploadProject", "title": "Upload Project", "category": "Project Importer"}], "views": {"explorer": [{"type": "webview", "id": "projectImporterWelcome", "name": "Project Importer"}]}, "viewsWelcome": [{"view": "projectImporterWelcome", "contents": "Import projects from GitHub or your computer.\n\n[Login with GitHub](command:projectImporter.loginWithGithub)\n[Clone Repository](command:projectImporter.cloneRepository)\n[Upload Project](command:projectImporter.uploadProject)", "when": "true"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "test": "echo \"No tests specified\" && exit 0"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "^18.15.0", "typescript": "^4.9.4", "@vscode/vsce": "^2.15.0"}, "dependencies": {"axios": "^1.6.0"}}