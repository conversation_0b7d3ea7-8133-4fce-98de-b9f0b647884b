# PostgreSQL configuration for Supabase
# Optimized for AI workloads with vector extensions and proper security

# =============================================================================
# CONNECTION SETTINGS
# =============================================================================

# Connection and Authentication
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Authentication
password_encryption = scram-sha-256
ssl = off  # Disabled for development, enable for production
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'

# =============================================================================
# MEMORY SETTINGS
# =============================================================================

# Memory Configuration (adjust based on container limits)
shared_buffers = 256MB                    # 25% of RAM
effective_cache_size = 1GB               # 75% of RAM
work_mem = 16MB                          # For complex queries
maintenance_work_mem = 128MB             # For maintenance operations
max_stack_depth = 2MB

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Query Planner
default_statistics_target = 100
random_page_cost = 1.1                  # SSD-optimized
effective_io_concurrency = 200          # For SSDs
seq_page_cost = 1.0

# Checkpoints and WAL
wal_buffers = 16MB
checkpoint_completion_target = 0.9
checkpoint_timeout = 5min
checkpoint_warning = 30s

# Background Writer
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0
bgwriter_flush_after = 512kB

# =============================================================================
# LOGGING SETTINGS
# =============================================================================

# Logging Configuration
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_truncate_on_rotation = on

# What to Log
log_min_messages = info
log_min_error_statement = error
log_min_duration_statement = 1000       # Log slow queries (1 second)
log_statement = 'none'                  # Set to 'all' for debugging
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_timezone = 'UTC'

# Statement Statistics
shared_preload_libraries = 'pg_stat_statements,vector'
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all
stats_temp_directory = 'pg_stat_tmp'

# =============================================================================
# VECTOR EXTENSION SETTINGS
# =============================================================================

# Vector Extension Configuration
# These settings optimize for AI/ML workloads with vector operations

# Enable vector extension
# shared_preload_libraries already includes 'vector' above

# Vector-specific settings
max_locks_per_transaction = 128          # Increased for vector operations
max_pred_locks_per_transaction = 128     # For complex vector queries

# =============================================================================
# SUPABASE-SPECIFIC SETTINGS
# =============================================================================

# Row Level Security
row_security = on

# Extensions
shared_preload_libraries = 'pg_stat_statements,vector,pg_cron'

# JSON and JSONB optimization
gin_pending_list_limit = 4MB

# Replication (for Supabase realtime)
wal_level = logical
max_wal_senders = 10
max_replication_slots = 10

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Database Security
ssl_ciphers = 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384'
ssl_prefer_server_ciphers = on

# Connection Security
tcp_keepalives_idle = 600
tcp_keepalives_interval = 30
tcp_keepalives_count = 3

# Statement Timeout (prevent runaway queries)
statement_timeout = 30min
lock_timeout = 10s
idle_in_transaction_session_timeout = 5min

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# These settings are optimized for development
# For production, consider increasing memory settings based on available RAM

# Enable auto-explain for development
session_preload_libraries = 'auto_explain'
auto_explain.log_min_duration = 5s
auto_explain.log_analyze = on
auto_explain.log_verbose = on
auto_explain.log_buffers = on
auto_explain.log_format = json

# =============================================================================
# VACUUM AND AUTOVACUUM
# =============================================================================

# Autovacuum Configuration
autovacuum = on
autovacuum_max_workers = 4
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_threshold = 50
autovacuum_analyze_scale_factor = 0.05

# Vacuum Cost
vacuum_cost_delay = 10ms
vacuum_cost_page_hit = 1
vacuum_cost_page_miss = 10
vacuum_cost_page_dirty = 20
vacuum_cost_limit = 200

# =============================================================================
# PARALLEL PROCESSING
# =============================================================================

# Parallel Query Settings
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4
parallel_tuple_cost = 0.1
parallel_setup_cost = 1000.0

# =============================================================================
# ARCHIVE AND BACKUP
# =============================================================================

# Archive Mode (for point-in-time recovery)
archive_mode = off                       # Enable for production
archive_command = ''                     # Set for production
archive_timeout = 0

# =============================================================================
# TIMEZONE
# =============================================================================

# Timezone Configuration
timezone = 'UTC'
log_timezone = 'UTC'
datestyle = 'iso, mdy'
default_text_search_config = 'pg_catalog.english'

# =============================================================================
# EXTENSIONS AUTO-LOADING
# =============================================================================

# Automatically create extensions in new databases
# This is handled by migration scripts, but listed here for reference:
# - vector (for AI embeddings)
# - pg_stat_statements (for query statistics)
# - pg_cron (for scheduled tasks)
# - uuid-ossp (for UUID generation)
# - pgcrypto (for encryption functions)