# Supabase Integration Guide for AI Coding Agent

## Overview

This guide provides Supabase integration for authentication and vector storage in the AI Coding Agent, replacing PostgreSQL with a comprehensive auth + vector solution using Row Level Security (RLS) for fine-grained permissions.

## Architecture Changes

### Updated System Stack
```
AI Orchestrator (FastAPI) ←→ Supabase (Auth + Vector DB) ←→ Redis (Cache)
                          ↓
                   Code-Server + Admin Dashboard
```

### Key Benefits
1. **Unified Auth**: JWT-based authentication with RLS
2. **Vector Storage**: Built-in pgvector for RAG with permissions
3. **Real-time**: WebSocket subscriptions for live updates
4. **Self-Hosted**: Complete control over data and infrastructure

## Docker Compose Configuration

### Core Supabase Services

```yaml
# docker-compose.supabase.yml
version: '3.8'

services:
  # Supabase Database
  supabase-db:
    image: supabase/postgres:*********
    container_name: ai-coding-supabase-db
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ai_coding_agent}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_super_secret_password}
    volumes:
      - supabase_db_data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - ai-coding-network

  # Supabase Auth
  supabase-auth:
    image: supabase/gotrue:v2.132.3
    container_name: ai-coding-supabase-auth
    depends_on:
      - supabase-db
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      GOTRUE_DB_DATABASE_URL: postgres://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-your_super_secret_password}@supabase-db:5432/${POSTGRES_DB:-ai_coding_agent}?search_path=auth
      GOTRUE_SITE_URL: ${SITE_URL:-http://localhost:3000}
      GOTRUE_JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      GOTRUE_JWT_EXP: ${JWT_EXPIRY:-3600}
      API_EXTERNAL_URL: ${API_EXTERNAL_URL:-http://localhost:8000}
    networks:
      - ai-coding-network

  # Supabase REST API
  supabase-rest:
    image: postgrest/postgrest:v12.0.1
    container_name: ai-coding-supabase-rest
    depends_on:
      - supabase-db
    environment:
      PGRST_DB_URI: postgres://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-your_super_secret_password}@supabase-db:5432/${POSTGRES_DB:-ai_coding_agent}
      PGRST_DB_SCHEMAS: ${PGRST_DB_SCHEMAS:-public}
      PGRST_DB_ANON_ROLE: ${PGRST_DB_ANON_ROLE:-anon}
      PGRST_JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
    networks:
      - ai-coding-network

  # Supabase Kong Gateway
  supabase-kong:
    image: kong:2.8.1
    container_name: ai-coding-supabase-kong
    ports:
      - "8000:8000"
      - "8443:8443"
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
    volumes:
      - ./supabase/config/kong.yml:/var/lib/kong/kong.yml:ro
    networks:
      - ai-coding-network

  # Updated AI Orchestrator
  ai-orchestrator:
    build: ./containers/ai-orchestrator
    container_name: ai-coding-orchestrator
    depends_on:
      - supabase-kong
      - redis
    environment:
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_ANON_KEY: ${ANON_KEY}
      SUPABASE_SERVICE_KEY: ${SERVICE_ROLE_KEY}
      DATABASE_URL: postgres://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-your_super_secret_password}@supabase-db:5432/${POSTGRES_DB:-ai_coding_agent}
      REDIS_URL: redis://redis:6379/0
      OLLAMA_URL: http://ollama:11434
    ports:
      - "8001:8000"
    networks:
      - ai-coding-network

volumes:
  supabase_db_data:

networks:
  ai-coding-network:
    driver: bridge
```

### Environment Configuration

```bash
# .env for Supabase
POSTGRES_DB=ai_coding_agent
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_super_secret_and_long_postgres_password

# Generate these JWT keys
JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long
ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# URLs
SITE_URL=http://localhost:3000
API_EXTERNAL_URL=http://localhost:8000

# Auth Settings
JWT_EXPIRY=3600
PGRST_DB_SCHEMAS=public,auth,storage
PGRST_DB_ANON_ROLE=anon
```

## Database Schema with RLS

### Core Tables with Permissions

```sql
-- supabase/migrations/001_initial_schema.sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;

-- User profiles
CREATE TABLE public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE,
    full_name TEXT,
    role TEXT DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

-- Projects
CREATE TABLE public.projects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own projects" ON public.projects
    FOR SELECT USING (owner_id = auth.uid());

-- Documents (for RAG)
CREATE TABLE public.documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    content TEXT,
    owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;

-- Document sections with vector embeddings
CREATE TABLE public.document_sections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_id UUID REFERENCES public.documents(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    embedding vector(384),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE public.document_sections ENABLE ROW LEVEL SECURITY;

-- RLS Policy for document sections (RAG with permissions)
CREATE POLICY "Users can query their document sections" ON public.document_sections
    FOR SELECT USING (
        document_id IN (
            SELECT id FROM public.documents
            WHERE project_id IN (
                SELECT id FROM public.projects
                WHERE owner_id = auth.uid()
            )
        )
    );

-- Vector similarity search function with RLS
CREATE OR REPLACE FUNCTION match_document_sections(
    query_embedding vector(384),
    match_threshold float,
    match_count int
)
RETURNS TABLE(
    id uuid,
    content text,
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        ds.id,
        ds.content,
        1 - (ds.embedding <=> query_embedding) AS similarity
    FROM public.document_sections ds
    WHERE 1 - (ds.embedding <=> query_embedding) > match_threshold
    ORDER BY ds.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Create vector index
CREATE INDEX document_sections_embedding_idx ON public.document_sections
    USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
```

## Python Integration

### Supabase Service

```python
# src/services/supabase_service.py
import os
import asyncio
from typing import Optional, Dict, Any, List
from supabase import create_client, Client
import asyncpg
from pgvector.asyncpg import register_vector
import logging

logger = logging.getLogger(__name__)

class SupabaseService:
    def __init__(self):
        self.url = os.getenv("SUPABASE_URL")
        self.anon_key = os.getenv("SUPABASE_ANON_KEY")
        self.service_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.database_url = os.getenv("DATABASE_URL")

        self.client: Optional[Client] = None
        self.service_client: Optional[Client] = None
        self.db_pool: Optional[asyncpg.Pool] = None

    async def initialize(self):
        """Initialize Supabase clients and database connections."""
        self.client = create_client(self.url, self.anon_key)
        self.service_client = create_client(self.url, self.service_key)

        # Direct DB connection for vector operations
        self.db_pool = await asyncpg.create_pool(
            self.database_url,
            min_size=5,
            max_size=20
        )

        # Register vector type
        async with self.db_pool.acquire() as conn:
            await register_vector(conn)

        logger.info("Supabase service initialized")

    def get_client(self, user_jwt: Optional[str] = None) -> Client:
        """Get client with optional user JWT for RLS."""
        if user_jwt:
            return create_client(
                self.url,
                self.anon_key,
                options={"headers": {"Authorization": f"Bearer {user_jwt}"}}
            )
        return self.client

# Global instance
supabase_service = SupabaseService()
```

### Authentication Service

```python
# src/services/auth_service.py
from typing import Optional, Dict, Any
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer
from supabase import Client

security = HTTPBearer()

class AuthService:
    def __init__(self, supabase_client: Client):
        self.client = supabase_client

    async def sign_up(self, email: str, password: str, metadata: Optional[Dict] = None):
        """Sign up new user."""
        try:
            response = self.client.auth.sign_up({
                "email": email,
                "password": password,
                "options": {"data": metadata or {}}
            })
            return {"user": response.user, "session": response.session}
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))

    async def sign_in(self, email: str, password: str):
        """Sign in user."""
        try:
            response = self.client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            return {"user": response.user, "session": response.session}
        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid credentials")

    async def get_user_from_token(self, token: str):
        """Get user from JWT token."""
        try:
            response = self.client.auth.get_user(token)
            return response.user
        except Exception:
            return None

async def get_current_user(credentials = Depends(security)):
    """Get current authenticated user."""
    token = credentials.credentials
    auth_service = AuthService(supabase_service.client)
    user = await auth_service.get_user_from_token(token)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid token")
    return user
```

### Vector Storage Service

```python
# src/services/vector_service.py
import asyncpg
from typing import List, Dict, Any
import json

class VectorService:
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool

    async def store_document_embedding(
        self,
        document_id: str,
        content: str,
        embedding: List[float]
    ):
        """Store document section with embedding."""
        async with self.db_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO document_sections (document_id, content, embedding)
                VALUES ($1, $2, $3)
            """, document_id, content, embedding)

    async def semantic_search(
        self,
        query_embedding: List[float],
        threshold: float = 0.7,
        limit: int = 10,
        user_jwt: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Perform semantic search with RLS."""
        async with self.db_pool.acquire() as conn:
            if user_jwt:
                # Set JWT for RLS
                await conn.execute(f"SELECT set_config('request.jwt.claims', '{user_jwt}', true)")

            results = await conn.fetch("""
                SELECT * FROM match_document_sections($1, $2, $3)
            """, query_embedding, threshold, limit)

            return [dict(row) for row in results]

    async def get_document_sections(self, document_id: str):
        """Get all sections for a document."""
        async with self.db_pool.acquire() as conn:
            results = await conn.fetch("""
                SELECT id, content, embedding FROM document_sections
                WHERE document_id = $1
                ORDER BY created_at
            """, document_id)

            return [dict(row) for row in results]
```

## FastAPI Integration

### Updated Main Application

```python
# src/main.py
from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from .services.supabase_service import supabase_service
from .services.auth_service import get_current_user, AuthService
from .services.vector_service import VectorService

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await supabase_service.initialize()
    yield
    # Shutdown
    if supabase_service.db_pool:
        await supabase_service.db_pool.close()

app = FastAPI(
    title="AI Coding Agent Orchestrator",
    version="2.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8443"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Auth endpoints
@app.post("/api/v1/auth/signup")
async def signup(email: str, password: str):
    auth_service = AuthService(supabase_service.client)
    return await auth_service.sign_up(email, password)

@app.post("/api/v1/auth/signin")
async def signin(email: str, password: str):
    auth_service = AuthService(supabase_service.client)
    return await auth_service.sign_in(email, password)

# Protected endpoints
@app.get("/api/v1/projects")
async def get_projects(current_user = Depends(get_current_user)):
    """Get user's projects with RLS."""
    user_client = supabase_service.get_client(current_user.get('access_token'))
    response = user_client.table('projects').select('*').execute()
    return response.data

@app.post("/api/v1/documents/{document_id}/search")
async def search_documents(
    document_id: str,
    query_embedding: List[float],
    current_user = Depends(get_current_user)
):
    """Semantic search with user permissions."""
    vector_service = VectorService(supabase_service.db_pool)
    results = await vector_service.semantic_search(
        query_embedding,
        user_jwt=current_user.get('access_token')
    )
    return {"results": results}

@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy", "version": "2.0.0"}
```

## RAG Implementation with Permissions

### Secure RAG Service

```python
# src/services/rag_service.py
from typing import List, Dict, Any, Optional
import openai
from .vector_service import VectorService
from .supabase_service import supabase_service

class RAGService:
    def __init__(self):
        self.vector_service = VectorService(supabase_service.db_pool)

    async def generate_code_with_context(
        self,
        prompt: str,
        user_jwt: str,
        project_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate code using RAG with user permissions."""

        # 1. Get embedding for user prompt
        prompt_embedding = await self._get_embedding(prompt)

        # 2. Search relevant documents (RLS applied automatically)
        relevant_docs = await self.vector_service.semantic_search(
            prompt_embedding,
            user_jwt=user_jwt,
            threshold=0.7,
            limit=5
        )

        # 3. Build context from retrieved documents
        context = self._build_context(relevant_docs)

        # 4. Generate code with LLM
        enhanced_prompt = f"""
        Context from your codebase:
        {context}

        User Request:
        {prompt}

        Generate code that follows the patterns and style from the context.
        """

        generated_code = await self._call_llm(enhanced_prompt)

        return {
            "generated_code": generated_code,
            "context_used": len(relevant_docs),
            "sources": [doc["id"] for doc in relevant_docs]
        }

    async def _get_embedding(self, text: str) -> List[float]:
        """Get embedding for text (replace with your embedding model)."""
        # Use your preferred embedding model here
        # e.g., OpenAI, Sentence Transformers, etc.
        pass

    def _build_context(self, documents: List[Dict]) -> str:
        """Build context string from retrieved documents."""
        context_parts = []
        for doc in documents:
            context_parts.append(f"```\n{doc['content']}\n```")
        return "\n\n".join(context_parts)

    async def _call_llm(self, prompt: str) -> str:
        """Call LLM service (Ollama in this case)."""
        # Implementation for calling Ollama
        pass
```

## Security Best Practices

### Kong Gateway Configuration

```yaml
# supabase/config/kong.yml
_format_version: "1.1"

services:
  - name: auth-v1-open
    url: http://supabase-auth:9999/
    routes:
      - name: auth-v1-open
        strip_path: true
        paths:
          - /auth/v1/

  - name: rest-v1
    url: http://supabase-rest:3000/
    routes:
      - name: rest-v1
        strip_path: true
        paths:
          - /rest/v1/
    plugins:
      - name: cors
      - name: key-auth
        config:
          hide_credentials: false

consumers:
  - username: anon
    keyauth_credentials:
      - key: SUPABASE_ANON_KEY
  - username: service_role
    keyauth_credentials:
      - key: SUPABASE_SERVICE_KEY
```

## Key Takeaways

1. **Self-Hosted Supabase**: Complete control with Docker deployment
2. **Row Level Security**: Fine-grained permissions on vector data
3. **JWT Authentication**: Seamless auth with FastAPI integration
4. **Vector Storage**: pgvector with permission-aware semantic search
5. **RAG with Permissions**: Context retrieval respects user access
6. **Production Ready**: Kong gateway, connection pooling, monitoring
7. **Migration Path**: Gradual replacement of existing PostgreSQL
8. **Scalable Architecture**: Async operations with connection pooling

This integration provides enterprise-grade authentication and vector capabilities while maintaining the security and performance requirements of your AI coding agent.