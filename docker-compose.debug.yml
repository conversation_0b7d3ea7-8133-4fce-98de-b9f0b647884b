version: '3.8'

# Docker Compose override for debugging ai-orchestrator startup issues
# Usage: docker-compose -f docker-compose.yml -f docker-compose.debug.yml up

services:
  ai-orchestrator:
    # Disable restart policy for debugging
    restart: "no"

    # Override environment variables for debugging
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - PYTHONPATH=/app/src
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1

    # Enable development mode with verbose output
    command: >
      sh -c "
        echo 'Starting AI Orchestrator in debug mode...' &&
        echo 'Python path: /app/src' &&
        echo 'Current directory:' && pwd &&
        echo 'Directory contents:' && ls -la &&
        echo 'Source directory contents:' && ls -la src/ &&
        echo 'Running startup diagnostics...' &&
        python debug_startup.py &&
        echo 'Starting application...' &&
        python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
      "

    # Override healthcheck for debugging (less aggressive)
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 60s
      timeout: 30s
      start_period: 120s  # Give more time for startup
      retries: 5

    # Add volume for debug logs
    volumes:
      - debug_logs:/tmp

    # Additional ports for debugging if needed
    ports:
      - "8001:8000"  # Alternative port mapping

volumes:
  debug_logs: