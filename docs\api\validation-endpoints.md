# Validation Framework API Documentation

## Overview

The Validation Framework API provides comprehensive endpoints for managing task validation, error recovery, pipeline execution, state management, and approval workflows. All endpoints follow REST principles and return JSON responses.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

All API endpoints require authentication. Include the authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "data": {...},
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

Error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Task validation failed: Missing required files",
    "details": {...}
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Validation Endpoints

### Validate Task

Validates a task for completion and correctness.

**Endpoint:** `POST /validate/task/{task_id}`

**Parameters:**
- `task_id` (path): UUID of the task to validate

**Request Body:**
```json
{
  "task_result": {
    "success": true,
    "output": "Task completed successfully",
    "files_created": ["src/components/Button.tsx"],
    "files_modified": ["src/App.tsx"],
    "duration_seconds": 45.2,
    "metadata": {
      "component_type": "functional",
      "props": ["onClick", "disabled", "children"]
    }
  },
  "validation_options": {
    "strict_mode": false,
    "skip_tests": false,
    "custom_rules": []
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "validation_result": {
      "is_valid": true,
      "details": "Task validation passed successfully",
      "error": null,
      "warnings": [],
      "metrics": {
        "validation_time_seconds": 2.34,
        "checks_performed": 5,
        "task_type": "create_component",
        "agent_type": "frontend"
      }
    }
  }
}
```

**Error Codes:**
- `400` - Invalid request format
- `404` - Task not found
- `422` - Validation failed
- `500` - Internal validation error

### Get Validation Results

Retrieves validation results for a specific task.

**Endpoint:** `GET /validate/results/{task_id}`

**Parameters:**
- `task_id` (path): UUID of the task
- `include_metrics` (query, optional): Include performance metrics (default: true)
- `include_warnings` (query, optional): Include validation warnings (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "task_id": "123e4567-e89b-12d3-a456-************",
    "validation_results": [
      {
        "validation_type": "file_existence",
        "status": "passed",
        "details": "All expected files exist",
        "timestamp": "2024-01-15T10:30:00Z"
      },
      {
        "validation_type": "code_syntax",
        "status": "passed",
        "details": "TypeScript syntax validation passed",
        "warnings": ["Unused import detected"],
        "timestamp": "2024-01-15T10:30:05Z"
      }
    ],
    "overall_status": "passed",
    "summary": {
      "total_checks": 5,
      "passed": 5,
      "failed": 0,
      "warnings": 1
    }
  }
}
```

## Execution Control Endpoints

### Start Execution

Starts execution of a pipeline or individual task.

**Endpoint:** `POST /execution/start`

**Request Body:**
```json
{
  "execution_type": "pipeline", // or "task"
  "target_id": "pipeline-123",
  "user_id": "user-456",
  "execution_options": {
    "create_checkpoints": true,
    "require_approvals": true,
    "parallel_execution": false,
    "timeout_minutes": 60
  },
  "context": {
    "roadmap_id": "roadmap-789",
    "environment": "development"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "execution_id": "exec-123e4567-e89b-12d3-a456-************",
    "status": "started",
    "estimated_duration_minutes": 45,
    "checkpoints_enabled": true,
    "next_milestone": "component_creation_phase"
  }
}
```

### Pause Execution

Pauses an ongoing execution.

**Endpoint:** `POST /execution/pause/{execution_id}`

**Parameters:**
- `execution_id` (path): UUID of the execution to pause

**Request Body:**
```json
{
  "reason": "User requested pause for review",
  "create_checkpoint": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "execution_id": "exec-123e4567-e89b-12d3-a456-************",
    "status": "paused",
    "checkpoint_id": "checkpoint-987fcdeb-51a2-43d1-b789-123456789abc",
    "paused_at": "2024-01-15T10:35:00Z"
  }
}
```

### Resume Execution

Resumes a paused execution.

**Endpoint:** `POST /execution/resume/{execution_id}`

**Parameters:**
- `execution_id` (path): UUID of the execution to resume

**Request Body:**
```json
{
  "resume_from_checkpoint": true,
  "checkpoint_id": "checkpoint-987fcdeb-51a2-43d1-b789-123456789abc"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "execution_id": "exec-123e4567-e89b-12d3-a456-************",
    "status": "running",
    "resumed_at": "2024-01-15T10:40:00Z",
    "current_stage": "frontend_component_creation"
  }
}
```

### Get Execution Status

Retrieves the current status of an execution.

**Endpoint:** `GET /execution/status/{execution_id}`

**Parameters:**
- `execution_id` (path): UUID of the execution
- `include_details` (query, optional): Include detailed stage information (default: false)

**Response:**
```json
{
  "success": true,
  "data": {
    "execution_id": "exec-123e4567-e89b-12d3-a456-************",
    "status": "running",
    "progress": {
      "current_stage": 2,
      "total_stages": 5,
      "percentage": 40.0
    },
    "stages": [
      {
        "stage_id": "backend_setup",
        "name": "Backend Setup",
        "status": "completed",
        "started_at": "2024-01-15T10:00:00Z",
        "completed_at": "2024-01-15T10:15:00Z"
      },
      {
        "stage_id": "frontend_components",
        "name": "Frontend Components",
        "status": "running",
        "started_at": "2024-01-15T10:15:00Z",
        "progress": {
          "completed_tasks": 3,
          "total_tasks": 7
        }
      }
    ],
    "metrics": {
      "elapsed_time_minutes": 35,
      "estimated_remaining_minutes": 25
    }
  }
}
```

## Checkpoint Management Endpoints

### Create Checkpoint

Creates a checkpoint of the current execution state.

**Endpoint:** `POST /checkpoints/create/{execution_id}`

**Parameters:**
- `execution_id` (path): UUID of the execution

**Request Body:**
```json
{
  "checkpoint_name": "component_creation_complete",
  "description": "All React components have been created successfully",
  "checkpoint_type": "milestone", // or "automatic", "manual"
  "include_files": true,
  "include_database": false,
  "metadata": {
    "phase": "frontend_development",
    "milestone": "components_complete"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "checkpoint_id": "checkpoint-987fcdeb-51a2-43d1-b789-123456789abc",
    "execution_id": "exec-123e4567-e89b-12d3-a456-************",
    "created_at": "2024-01-15T10:45:00Z",
    "checkpoint_size_mb": 15.7,
    "files_included": 23,
    "creation_time_seconds": 3.2
  }
}
```

### List Checkpoints

Lists all checkpoints for an execution.

**Endpoint:** `GET /checkpoints/list/{execution_id}`

**Parameters:**
- `execution_id` (path): UUID of the execution
- `limit` (query, optional): Maximum number of checkpoints to return (default: 50)
- `offset` (query, optional): Number of checkpoints to skip (default: 0)
- `type` (query, optional): Filter by checkpoint type

**Response:**
```json
{
  "success": true,
  "data": {
    "execution_id": "exec-123e4567-e89b-12d3-a456-************",
    "total_checkpoints": 5,
    "checkpoints": [
      {
        "checkpoint_id": "checkpoint-newest",
        "name": "component_creation_complete",
        "type": "milestone",
        "created_at": "2024-01-15T10:45:00Z",
        "size_mb": 15.7,
        "description": "All React components created"
      },
      {
        "checkpoint_id": "checkpoint-older",
        "name": "backend_setup_complete",
        "type": "automatic",
        "created_at": "2024-01-15T10:15:00Z",
        "size_mb": 8.3,
        "description": "Backend services initialized"
      }
    ]
  }
}
```

### Rollback to Checkpoint

Rolls back execution to a previous checkpoint.

**Endpoint:** `POST /rollback/{checkpoint_id}`

**Parameters:**
- `checkpoint_id` (path): UUID of the checkpoint to rollback to

**Request Body:**
```json
{
  "user_id": "user-456",
  "reason": "Need to fix critical bug in component logic",
  "create_pre_rollback_checkpoint": true,
  "rollback_options": {
    "restore_files": true,
    "restore_database": false,
    "verify_integrity": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "rollback_id": "rollback-abc123def456",
    "checkpoint_id": "checkpoint-987fcdeb-51a2-43d1-b789-123456789abc",
    "status": "completed",
    "rollback_time_seconds": 8.5,
    "pre_rollback_checkpoint": "checkpoint-backup-789xyz",
    "restored_files": 23,
    "verification_result": "passed"
  }
}
```

## Approval System Endpoints

### Get Pending Approvals

Retrieves all pending approval requests.

**Endpoint:** `GET /approvals/pending`

**Parameters:**
- `user_id` (query, optional): Filter by user ID
- `approval_type` (query, optional): Filter by approval type
- `risk_level` (query, optional): Filter by risk level
- `limit` (query, optional): Maximum results (default: 50)

**Response:**
```json
{
  "success": true,
  "data": {
    "total_pending": 3,
    "approvals": [
      {
        "id": "approval-123abc",
        "approval_type": "phase_completion",
        "title": "Frontend Phase Complete",
        "description": "All frontend components have been created",
        "item_type": "phase",
        "item_id": "phase-frontend",
        "risk_level": "medium",
        "created_at": "2024-01-15T10:50:00Z",
        "expires_at": "2024-01-16T10:50:00Z",
        "time_remaining_minutes": 1380,
        "changes_summary": [
          "Created 5 React components",
          "Added component tests",
          "Updated routing configuration"
        ],
        "files_affected": [
          "src/components/Header.tsx",
          "src/components/Footer.tsx",
          "src/App.tsx"
        ]
      }
    ]
  }
}
```

### Approve Request

Approves a pending approval request.

**Endpoint:** `POST /approvals/{approval_id}/approve`

**Parameters:**
- `approval_id` (path): UUID of the approval request

**Request Body:**
```json
{
  "user_id": "user-456",
  "comments": "Phase implementation looks good. Components follow design guidelines and tests are comprehensive.",
  "conditions": [
    "Add integration tests before production deployment",
    "Review performance impact on load times"
  ],
  "metadata": {
    "review_duration_minutes": 15,
    "reviewer_role": "senior_developer"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "approved",
    "approval_id": "approval-123abc",
    "approved_at": "2024-01-15T11:05:00Z",
    "response_time_minutes": 15,
    "next_actions": [
      "Continue to next phase",
      "Create deployment checkpoint"
    ]
  }
}
```

### Reject Request

Rejects a pending approval request.

**Endpoint:** `POST /approvals/{approval_id}/reject`

**Parameters:**
- `approval_id` (path): UUID of the approval request

**Request Body:**
```json
{
  "user_id": "user-456",
  "comments": "Components need significant refactoring. Issues found: 1) Missing accessibility attributes, 2) Performance concerns with large lists, 3) Inconsistent error handling",
  "required_changes": [
    "Add ARIA labels to all interactive components",
    "Implement virtual scrolling for large lists",
    "Standardize error boundary implementation"
  ],
  "metadata": {
    "severity": "major",
    "estimated_fix_time_hours": 8
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "rejected",
    "approval_id": "approval-123abc",
    "rejected_at": "2024-01-15T11:05:00Z",
    "required_actions": [
      "Address listed issues",
      "Resubmit for approval after fixes"
    ],
    "rollback_recommended": true
  }
}
```

### Create Approval Request

Creates a new approval request.

**Endpoint:** `POST /approvals/create`

**Request Body:**
```json
{
  "user_id": "user-456",
  "approval_type": "destructive_operation",
  "title": "Database Schema Migration",
  "description": "Migration will drop unused columns and add new indexes",
  "item_type": "migration",
  "item_id": "migration-20240115",
  "roadmap_id": "roadmap-789",
  "timeout_minutes": 120,
  "files_affected": [
    "migrations/20240115_drop_columns.sql",
    "models/user.py"
  ],
  "services_affected": [
    "user-service",
    "auth-service"
  ],
  "changes_summary": [
    "Drop unused 'legacy_id' column from users table",
    "Add composite index on (email, status)",
    "Update User model class"
  ],
  "impact_summary": [
    "Potential data loss if migration fails",
    "Application downtime during migration",
    "Breaking changes to legacy API endpoints"
  ],
  "rollback_plan": "Migration includes rollback script to restore original schema",
  "estimated_duration": 900
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "approval_request": {
      "id": "approval-456def",
      "status": "pending",
      "risk_level": "high",
      "risk_assessment": "High risk: potential data loss and breaking changes",
      "created_at": "2024-01-15T11:10:00Z",
      "expires_at": "2024-01-15T13:10:00Z",
      "notification_sent": true
    }
  }
}
```

### Get Approval Metrics

Retrieves approval system performance metrics.

**Endpoint:** `GET /approvals/metrics`

**Parameters:**
- `time_range` (query, optional): Time range for metrics (1h, 24h, 7d, 30d)
- `user_id` (query, optional): Filter by specific user

**Response:**
```json
{
  "success": true,
  "data": {
    "time_range": "24h",
    "metrics": {
      "total_requests": 15,
      "approved_count": 12,
      "rejected_count": 2,
      "timeout_count": 0,
      "cancelled_count": 1,
      "auto_approved_count": 8,
      "average_response_time_minutes": 25.4,
      "median_response_time_minutes": 18.0,
      "max_response_time_minutes": 95.0,
      "high_risk_requests": 3,
      "approval_rate": 0.8,
      "active_approvers": 4
    },
    "trends": {
      "requests_per_hour": [2, 1, 0, 1, 3, 2, 4, 2],
      "average_response_time_trend": "decreasing"
    }
  }
}
```

### Get Audit Trail

Retrieves audit trail for approval activities.

**Endpoint:** `GET /approvals/audit`

**Parameters:**
- `approval_id` (query, optional): Filter by specific approval
- `user_id` (query, optional): Filter by user
- `action` (query, optional): Filter by action type
- `limit` (query, optional): Maximum entries (default: 100)
- `start_date` (query, optional): Start date for range (ISO format)
- `end_date` (query, optional): End date for range (ISO format)

**Response:**
```json
{
  "success": true,
  "data": {
    "total_entries": 50,
    "audit_entries": [
      {
        "id": "audit-123",
        "approval_id": "approval-456def",
        "user_id": "user-456",
        "action": "approved",
        "details": "User approved database migration request",
        "timestamp": "2024-01-15T11:15:00Z",
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0...",
        "metadata": {
          "response_time_seconds": 300,
          "comments_length": 125
        }
      },
      {
        "id": "audit-124",
        "approval_id": "approval-456def",
        "user_id": null,
        "action": "created",
        "details": "Approval request created automatically",
        "timestamp": "2024-01-15T11:10:00Z",
        "metadata": {
          "risk_level": "high",
          "auto_approval_eligible": false
        }
      }
    ]
  }
}
```

## Health and Monitoring Endpoints

### System Health Check

Checks the overall health of the validation framework.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-15T11:20:00Z",
    "services": {
      "validation_engine": {
        "status": "healthy",
        "response_time_ms": 45,
        "last_check": "2024-01-15T11:19:55Z"
      },
      "error_recovery": {
        "status": "healthy",
        "active_recoveries": 0,
        "success_rate": 0.85
      },
      "approval_system": {
        "status": "healthy",
        "pending_requests": 3,
        "average_response_time_minutes": 25.4
      },
      "state_management": {
        "status": "healthy",
        "active_checkpoints": 12,
        "storage_used_gb": 2.3
      }
    },
    "metrics": {
      "uptime_seconds": 86400,
      "memory_usage_mb": 512,
      "cpu_usage_percent": 15.2
    }
  }
}
```

### Approval System Health

Specific health check for the approval system.

**Endpoint:** `GET /approvals/health`

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "issues": [],
    "metrics": {
      "total_requests": 150,
      "pending_requests": 3,
      "average_response_time_minutes": 25.4,
      "approval_rate": 0.8
    },
    "timestamp": "2024-01-15T11:20:00Z"
  }
}
```

## WebSocket Events

The validation framework supports real-time updates via WebSocket connections.

### Connection

Connect to WebSocket for real-time notifications:

```
ws://localhost:8000/ws/validation/{user_id}
```

### Event Types

#### Validation Events

```json
{
  "type": "validation_completed",
  "data": {
    "task_id": "task-123",
    "validation_result": {
      "is_valid": true,
      "details": "Validation passed"
    },
    "timestamp": "2024-01-15T11:25:00Z"
  }
}
```

#### Execution Events

```json
{
  "type": "execution_status_changed",
  "data": {
    "execution_id": "exec-456",
    "previous_status": "running",
    "current_status": "paused",
    "reason": "User requested pause",
    "timestamp": "2024-01-15T11:25:00Z"
  }
}
```

#### Approval Events

```json
{
  "type": "approval_notification",
  "data": {
    "approval_id": "approval-789",
    "notification_type": "request",
    "title": "Approval Required: Database Migration",
    "message": "Please review and approve database migration",
    "priority": "high",
    "expires_at": "2024-01-15T13:25:00Z"
  }
}
```

### WebSocket Registration

Register WebSocket connection for notifications:

**Endpoint:** `POST /approvals/websocket/register`

**Request Body:**
```json
{
  "user_id": "user-456",
  "connection_id": "conn-abc123"
}
```

## Error Codes

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (resource already exists or conflicting state)
- `422` - Unprocessable Entity (validation failed)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error
- `503` - Service Unavailable (temporary)

### Application Error Codes

- `VALIDATION_FAILED` - Task validation failed
- `EXECUTION_ERROR` - Pipeline execution error
- `CHECKPOINT_FAILED` - Checkpoint creation/restoration failed
- `APPROVAL_TIMEOUT` - Approval request timed out
- `ROLLBACK_FAILED` - Rollback operation failed
- `PERMISSION_DENIED` - User lacks required permissions
- `RESOURCE_NOT_FOUND` - Requested resource not found
- `INVALID_STATE` - Operation not allowed in current state

## Rate Limits

API endpoints are rate limited to ensure system stability:

- **Validation endpoints**: 100 requests per minute per user
- **Execution control**: 10 requests per minute per user
- **Approval actions**: 50 requests per minute per user
- **Status queries**: 200 requests per minute per user

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

## SDKs and Client Libraries

### Python SDK

```python
from validation_framework_sdk import ValidationClient

# Initialize client
client = ValidationClient(
    base_url="http://localhost:8000/api/v1",
    auth_token="your-jwt-token"
)

# Validate task
result = await client.validate_task(
    task_id="task-123",
    task_result=task_result
)

# Create approval request
approval = await client.create_approval_request(
    approval_type="phase_completion",
    title="Phase Complete",
    description="Ready for next phase"
)
```

### JavaScript SDK

```javascript
import { ValidationFrameworkClient } from 'validation-framework-sdk';

// Initialize client
const client = new ValidationFrameworkClient({
  baseUrl: 'http://localhost:8000/api/v1',
  authToken: 'your-jwt-token'
});

// Get pending approvals
const approvals = await client.getPendingApprovals({
  userId: 'user-456'
});

// Approve request
await client.approveRequest(approvalId, {
  comments: 'Looks good!',
  userId: 'user-456'
});
```

## Examples

### Complete Validation Workflow

```bash
# 1. Start execution
curl -X POST http://localhost:8000/api/v1/execution/start \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "execution_type": "pipeline",
    "target_id": "pipeline-123",
    "user_id": "user-456"
  }'

# 2. Monitor execution status
curl -X GET http://localhost:8000/api/v1/execution/status/exec-789 \
  -H "Authorization: Bearer $TOKEN"

# 3. Validate completed task
curl -X POST http://localhost:8000/api/v1/validate/task/task-123 \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_result": {
      "success": true,
      "files_created": ["src/component.tsx"]
    }
  }'

# 4. Handle approval if required
curl -X GET http://localhost:8000/api/v1/approvals/pending?user_id=user-456 \
  -H "Authorization: Bearer $TOKEN"

# 5. Approve or reject
curl -X POST http://localhost:8000/api/v1/approvals/approval-456/approve \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user-456",
    "comments": "Approved for production"
  }'
```

This API documentation provides a comprehensive guide for integrating with the validation framework. For additional support and examples, refer to the main validation framework documentation.