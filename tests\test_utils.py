import pytest
from unittest.mock import Mock
import jwt
from datetime import datetime, timedelta

class TestUtils:

    @staticmethod
    def create_test_token(user_id="test-user", email="<EMAIL>", expires_in_minutes=30):
        """Create a test JWT token for testing"""
        from src.utils.auth import SECRET_KEY, ALGORITHM

        data = {
            "sub": user_id,
            "email": email,
            "exp": datetime.utcnow() + timedelta(minutes=expires_in_minutes)
        }
        return jwt.encode(data, SECRET_KEY, algorithm=ALGORITHM)

    @staticmethod
    def create_mock_user(user_id="test-user", email="<EMAIL>", username="testuser"):
        """Create a mock user object for testing"""
        user = Mock()
        user.id = user_id
        user.email = email
        user.username = username
        return user

    @staticmethod
    def create_mock_supabase_response(user=None, success=True):
        """Create a mock Supabase response for testing"""
        response = Mock()
        if success and user:
            response.user = user
        else:
            response.user = None
        return response

def test_create_test_token():
    """Test the test token creation utility"""
    token = TestUtils.create_test_token()
    assert token is not None
    assert isinstance(token, str)
    assert len(token) > 0

def test_create_mock_user():
    """Test the mock user creation utility"""
    user = TestUtils.create_mock_user()
    assert user.id == "test-user"
    assert user.email == "<EMAIL>"
    assert user.username == "testuser"

def test_create_mock_supabase_response():
    """Test the mock Supabase response utility"""
    user = TestUtils.create_mock_user()
    response = TestUtils.create_mock_supabase_response(user)
    assert response.user == user

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
