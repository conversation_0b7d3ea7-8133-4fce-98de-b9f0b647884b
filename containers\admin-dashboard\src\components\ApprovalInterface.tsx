// Project: AI Coding Agent
// Purpose: Frontend approval interface component for user interaction with approval requests

import React, { useState, useEffect, useCallback } from 'react';
import {
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  DocumentTextIcon,
  ServerIcon,
  CircleStackIcon,
  ShieldCheckIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';

// Types for approval system
interface ApprovalRequest {
  id: string;
  user_id: string;
  approval_type: string;
  title: string;
  description: string;
  item_type: string;
  item_id: string;
  roadmap_id?: string;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  risk_assessment?: string;
  impact_summary: string[];
  changes_summary: string[];
  files_affected: string[];
  services_affected: string[];
  status: 'pending' | 'approved' | 'rejected' | 'timeout' | 'cancelled';
  created_at: string;
  expires_at: string;
  timeout_minutes: number;
  estimated_duration?: number;
  preview_data?: Record<string, any>;
  rollback_plan?: string;
}

interface ApprovalResponse {
  decision: 'approved' | 'rejected';
  comments?: string;
  conditions?: string[];
}

interface ApprovalInterfaceProps {
  // WebSocket connection for real-time updates
  websocket?: WebSocket;
  // API base URL
  apiBaseUrl?: string;
  // Current user ID
  userId: string;
  // Callback when approval is completed
  onApprovalComplete?: (requestId: string, response: ApprovalResponse) => void;
}

const ApprovalInterface: React.FC<ApprovalInterfaceProps> = ({
  websocket,
  apiBaseUrl = '/api/v1',
  userId,
  onApprovalComplete
}) => {
  // State management
  const [approvals, setApprovals] = useState<ApprovalRequest[]>([]);
  const [selectedApproval, setSelectedApproval] = useState<ApprovalRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedApprovals, setExpandedApprovals] = useState<Set<string>>(new Set());

  // Form state for approval response
  const [responseComments, setResponseComments] = useState('');
  const [responseConditions, setResponseConditions] = useState<string[]>([]);

  // Fetch pending approvals
  const fetchApprovals = useCallback(async () => {
    try {
      const response = await fetch(`${apiBaseUrl}/approvals/pending?user_id=${userId}`);
      if (response.ok) {
        const data = await response.json();
        setApprovals(data);
      } else {
        setError('Failed to fetch pending approvals');
      }
    } catch (err) {
      setError('Network error fetching approvals');
      console.error('Error fetching approvals:', err);
    } finally {
      setLoading(false);
    }
  }, [apiBaseUrl, userId]);

  // Handle approval decision
  const handleApprovalDecision = async (requestId: string, decision: 'approved' | 'rejected') => {
    setSubmitting(true);
    setError(null);

    try {
      const response = await fetch(`${apiBaseUrl}/approvals/${requestId}/${decision}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          comments: responseComments || undefined,
          conditions: responseConditions.length > 0 ? responseConditions : undefined,
        }),
      });

      if (response.ok) {
        // Remove from pending list
        setApprovals(prev => prev.filter(a => a.id !== requestId));

        // Clear form
        setResponseComments('');
        setResponseConditions([]);
        setSelectedApproval(null);

        // Call callback
        if (onApprovalComplete) {
          onApprovalComplete(requestId, {
            decision,
            comments: responseComments || undefined,
            conditions: responseConditions.length > 0 ? responseConditions : undefined,
          });
        }
      } else {
        const errorData = await response.json();
        setError(errorData.detail || `Failed to ${decision} approval`);
      }
    } catch (err) {
      setError(`Network error ${decision === 'approved' ? 'approving' : 'rejecting'} request`);
      console.error(`Error ${decision} approval:`, err);
    } finally {
      setSubmitting(false);
    }
  };

  // WebSocket message handling
  useEffect(() => {
    if (!websocket) return;

    const handleMessage = (event: MessageEvent) => {
      try {
        const message = JSON.parse(event.data);

        if (message.type === 'approval_notification') {
          // Refresh approvals list
          fetchApprovals();
        }
      } catch (err) {
        console.error('Error parsing WebSocket message:', err);
      }
    };

    websocket.addEventListener('message', handleMessage);

    return () => {
      websocket.removeEventListener('message', handleMessage);
    };
  }, [websocket, fetchApprovals]);

  // Initial fetch and polling
  useEffect(() => {
    fetchApprovals();

    // Poll for updates every 30 seconds
    const interval = setInterval(fetchApprovals, 30000);

    return () => clearInterval(interval);
  }, [fetchApprovals]);

  // Helper functions
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'critical': return <ExclamationTriangleIcon className="w-5 h-5" />;
      case 'high': return <ExclamationTriangleIcon className="w-4 h-4" />;
      default: return <ShieldCheckIcon className="w-4 h-4" />;
    }
  };

  const getTimeRemaining = (expiresAt: string) => {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diff = expires.getTime() - now.getTime();

    if (diff <= 0) return 'Expired';

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    } else {
      return `${minutes}m remaining`;
    }
  };

  const toggleExpanded = (approvalId: string) => {
    setExpandedApprovals(prev => {
      const newSet = new Set(prev);
      if (newSet.has(approvalId)) {
        newSet.delete(approvalId);
      } else {
        newSet.add(approvalId);
      }
      return newSet;
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Loading approvals...</span>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Pending Approvals</h1>
        <p className="text-gray-600 mt-1">
          {approvals.length} approval request{approvals.length !== 1 ? 's' : ''} pending your review
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <XCircleIcon className="h-5 w-5 text-red-400 mt-0.5" />
            <div className="ml-3">
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {approvals.length === 0 ? (
        <div className="text-center py-12">
          <CheckCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No pending approvals</h3>
          <p className="mt-1 text-sm text-gray-500">All approval requests have been processed.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {approvals.map((approval) => {
            const isExpanded = expandedApprovals.has(approval.id);

            return (
              <div key={approval.id} className="bg-white border border-gray-200 rounded-lg shadow-sm">
                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium border ${getRiskColor(approval.risk_level)}`}>
                          {getRiskIcon(approval.risk_level)}
                          {approval.risk_level.toUpperCase()} RISK
                        </span>
                        <span className="text-sm text-gray-500 capitalize">
                          {approval.approval_type.replace('_', ' ')}
                        </span>
                      </div>

                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        {approval.title}
                      </h3>

                      <p className="text-gray-600 mb-3">
                        {approval.description}
                      </p>

                      {/* Quick info */}
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <ClockIcon className="w-4 h-4" />
                          {getTimeRemaining(approval.expires_at)}
                        </span>
                        {approval.files_affected.length > 0 && (
                          <span className="flex items-center gap-1">
                            <DocumentTextIcon className="w-4 h-4" />
                            {approval.files_affected.length} file{approval.files_affected.length !== 1 ? 's' : ''}
                          </span>
                        )}
                        {approval.services_affected.length > 0 && (
                          <span className="flex items-center gap-1">
                            <ServerIcon className="w-4 h-4" />
                            {approval.services_affected.length} service{approval.services_affected.length !== 1 ? 's' : ''}
                          </span>
                        )}
                      </div>
                    </div>

                    <button
                      onClick={() => toggleExpanded(approval.id)}
                      className="ml-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {isExpanded ? <ChevronUpIcon className="w-5 h-5" /> : <ChevronDownIcon className="w-5 h-5" />}
                    </button>
                  </div>

                  {/* Expanded content */}
                  {isExpanded && (
                    <div className="mt-6 pt-6 border-t border-gray-100">
                      <div className="grid md:grid-cols-2 gap-6">
                        {/* Left column - Details */}
                        <div className="space-y-4">
                          {approval.risk_assessment && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 mb-2">Risk Assessment</h4>
                              <p className="text-sm text-gray-600">{approval.risk_assessment}</p>
                            </div>
                          )}

                          {approval.impact_summary.length > 0 && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 mb-2">Impact Summary</h4>
                              <ul className="text-sm text-gray-600 space-y-1">
                                {approval.impact_summary.map((impact, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                                    {impact}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {approval.changes_summary.length > 0 && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 mb-2">Changes</h4>
                              <ul className="text-sm text-gray-600 space-y-1">
                                {approval.changes_summary.map((change, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                                    {change}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>

                        {/* Right column - Affected resources */}
                        <div className="space-y-4">
                          {approval.files_affected.length > 0 && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 mb-2">Files Affected</h4>
                              <div className="max-h-32 overflow-y-auto">
                                <ul className="text-sm text-gray-600 space-y-1">
                                  {approval.files_affected.map((file, index) => (
                                    <li key={index} className="flex items-center gap-2">
                                      <DocumentTextIcon className="w-3 h-3" />
                                      <code className="text-xs bg-gray-100 px-1 rounded">{file}</code>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          )}

                          {approval.services_affected.length > 0 && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 mb-2">Services Affected</h4>
                              <ul className="text-sm text-gray-600 space-y-1">
                                {approval.services_affected.map((service, index) => (
                                  <li key={index} className="flex items-center gap-2">
                                    <ServerIcon className="w-3 h-3" />
                                    {service}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {approval.rollback_plan && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 mb-2">Rollback Plan</h4>
                              <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                                {approval.rollback_plan}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Response form */}
                      <div className="mt-6 pt-6 border-t border-gray-100">
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Comments (optional)
                            </label>
                            <textarea
                              value={responseComments}
                              onChange={(e) => setResponseComments(e.target.value)}
                              rows={3}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Add any comments or conditions for this approval..."
                            />
                          </div>

                          <div className="flex items-center gap-3">
                            <button
                              onClick={() => handleApprovalDecision(approval.id, 'approved')}
                              disabled={submitting}
                              className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <CheckCircleIcon className="w-4 h-4" />
                              {submitting ? 'Processing...' : 'Approve'}
                            </button>

                            <button
                              onClick={() => handleApprovalDecision(approval.id, 'rejected')}
                              disabled={submitting}
                              className="inline-flex items-center gap-2 px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <XCircleIcon className="w-4 h-4" />
                              {submitting ? 'Processing...' : 'Reject'}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default ApprovalInterface;