#!/usr/bin/env python3
# Project: AI Coding Agent
# Purpose: Test complete development environment setup

import asyncio
import aiohttp
import time
import sys

async def test_service(url, name, timeout=30):
    """Test if a service is responding"""
    print(f"🔍 Testing {name}...")
    start_time = time.time()

    while time.time() - start_time < timeout:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status in [200, 404]:  # 404 is ok for some endpoints
                        print(f"   ✅ {name} is responding (HTTP {response.status})")
                        return True
                    else:
                        print(f"   ⚠️  {name} returned HTTP {response.status}")
        except aiohttp.ClientConnectorError:
            print(f"   ⏳ Waiting for {name}... ({int(time.time() - start_time)}s)")
            await asyncio.sleep(2)
        except Exception as e:
            print(f"   ❌ Error testing {name}: {str(e)}")
            await asyncio.sleep(2)

    print(f"   ❌ {name} failed to respond within {timeout}s")
    return False

async def test_ollama_connection_from_container():
    """Test Ollama connection from within containers"""
    print("🔍 Testing Ollama connection from containers...")

    try:
        # Test the Ollama status endpoint through our API
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/api/ollama/status") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("connected"):
                        print("   ✅ Containers can connect to host Ollama!")
                        print(f"   📡 Base URL: {data.get('base_url')}")
                        return True
                    else:
                        print("   ❌ Containers cannot connect to host Ollama")
                        print(f"   💡 Message: {data.get('message')}")
                        return False
                else:
                    print(f"   ❌ API returned HTTP {response.status}")
                    return False
    except Exception as e:
        print(f"   ❌ Error testing Ollama connection: {str(e)}")
        return False

async def test_models_endpoint():
    """Test the models endpoint (requires auth)"""
    print("🔍 Testing models endpoint...")

    # Note: This will require JWT token for real testing
    # For now, just check if the endpoint exists
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/api/models") as response:
                if response.status == 401:  # Unauthorized is expected without JWT
                    print("   ✅ Models endpoint exists (authentication required)")
                    return True
                elif response.status == 200:
                    print("   ✅ Models endpoint accessible!")
                    return True
                else:
                    print(f"   ❌ Unexpected status: {response.status}")
                    return False
    except Exception as e:
        print(f"   ❌ Error testing models endpoint: {str(e)}")
        return False

async def main():
    print("🚀 AI Coding Agent Development Environment Test")
    print("=" * 55)

    # Test core services
    services = [
        ("http://localhost:5432", "PostgreSQL", 60),  # Database takes longer to start
        ("http://localhost:6379", "Redis", 30),
        ("http://localhost:8000/health", "AI Orchestrator", 60),
        ("http://localhost:3000", "Admin Dashboard", 45),
        ("http://localhost:8080", "Code Server", 45),
    ]

    success_count = 0
    for url, name, timeout in services:
        if await test_service(url, name, timeout):
            success_count += 1
        print()  # Empty line for readability

    print(f"📊 Service Status: {success_count}/{len(services)} services responding")

    # Test Ollama integration if AI Orchestrator is up
    if success_count >= 3:  # If most services are up
        print("\n🔗 Testing Ollama Integration...")
        ollama_ok = await test_ollama_connection_from_container()
        models_ok = await test_models_endpoint()

        if ollama_ok and models_ok:
            print("\n🎉 Development environment is fully operational!")
            print("\n📋 Next Steps:")
            print("   • Access Code Server: http://localhost:8080")
            print("   • Access Admin Dashboard: http://localhost:3000")
            print("   • AI Orchestrator API: http://localhost:8000")
            print("   • Debug port for AI Orchestrator: localhost:5678")
            print("   • Watch mode is active - code changes will hot-reload!")
            return 0
        else:
            print("\n⚠️  Environment is partially working but Ollama integration needs attention.")
            return 1
    else:
        print("\n❌ Development environment has issues. Check Docker logs.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))