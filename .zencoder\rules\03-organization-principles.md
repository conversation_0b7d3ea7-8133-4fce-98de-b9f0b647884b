# Cline Rules for AI Coding Agent Project

## Project Organization Principles

### Container-First Development
1. All development work happens within the containerized environment
2. Each service must have its own dedicated container with isolated dependencies
3. Use docker-compose for orchestrating multi-container applications
4. Never install development tools directly on the host system

### Directory Structure Standards
1. All container-specific code resides in `containers/{container-name}/`
2. Shared configurations and documentation go in root or `docs/` directory
3. Persistent data must use volumes defined in `volumes/` directory
4. Scripts for setup, deployment, and development go in `scripts/` directory

## Code Organization

### Python Code Standards
1. Use proper Python package structure with `__init__.py` files
2. Follow PEP 8 style guide for Python code
3. Use type hints for function parameters and return values
4. Implement proper error handling and logging
5. Write unit tests for all core functionality

### Container Configuration
1. Each container must have its own `Dockerfile` in its directory
2. Use `requirements.txt` for Python dependencies
3. Environment variables should be defined in `.env.example` and documented
4. Container networking should be explicitly defined in `docker-compose.yml`

## Development Workflow

### Branching Strategy
1. Main branch is always production-ready
2. Feature branches for new functionality
3. Hotfix branches for urgent fixes
4. Pull requests required for merging to main

### Documentation Standards
1. Update `docs/` when adding new features
2. Maintain README files in each major directory
3. Document environment variables in `.env.example`
4. Keep roadmap updated with progress in `docs/Projectroadmap.md`

### Testing Requirements
1. Write unit tests for all new Python code
2. Implement integration tests for container interactions
3. Test all API endpoints with valid and invalid inputs
4. Run tests before merging any changes

## File Management

### Naming Conventions
1. Use lowercase with hyphens for directory and file names
2. Use underscores for Python module names
3. Use descriptive names that clearly indicate purpose
4. Follow existing naming patterns in the project

### Version Control
1. Commit frequently with descriptive messages
2. Exclude sensitive data and large files from repository
3. Use .gitignore to prevent committing unnecessary files
4. Update .env.example when adding new environment variables

## AI Agent Development

### Agent Implementation
1. All agents inherit from BaseAgent class
2. Implement proper resource locking for sequential execution
3. Use shared context storage for agent handoffs
4. Include status reporting to Architect Agent

### LLM Integration
1. Use universal LLM service wrapper for model access
2. Implement proper API key management
3. Handle both local (Ollama) and cloud (OpenRouter, OpenAI) models
4. Include fallback mechanisms for model failures

## Security Practices

### Container Security
1. Run containers as non-root users
2. Regularly update base images
3. Scan containers for vulnerabilities
4. Implement network isolation between services

### Data Protection
1. Never commit sensitive data or API keys
2. Use environment variables for secrets
3. Encrypt sensitive data at rest
4. Implement proper access controls for user data

## Monitoring and Maintenance

### Logging Standards
1. Use structured logging in all containers
2. Include timestamps and log levels
3. Log important events and errors
4. Implement log rotation for long-running services

### Performance Optimization
1. Monitor container resource usage
2. Optimize database queries
3. Implement caching strategies
4. Regular performance testing
