# Admin Dashboard Frontend

Role-based LLM Management Dashboard for AI Coding Agent - Built with Next.js, TypeScript, and Tailwind CSS.

## 🎯 Overview

This frontend application provides a comprehensive interface for managing AI agent role configurations, including LLM provider selection, model configuration, API key management, and cost controls.

## 🏗️ Architecture

### Tech Stack

- **Framework**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Forms**: React Hook Form with validation
- **Components**: Custom components with accessibility
- **State Management**: Custom hooks with API integration
- **Testing**: Jest + React Testing Library
- **Code Quality**: ESLint, Prettier, TypeScript strict mode

### Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ApiKeyInput.tsx          # Secure API key input with validation
│   ├── ConfigManager.tsx        # Main role management container
│   ├── ModelSelector.tsx        # LLM model selection component
│   ├── ProviderSelector.tsx     # Provider selection with features
│   ├── RoleConfigPanel.tsx      # Individual role configuration
│   └── __tests__/              # Component tests
├── hooks/               # Custom React hooks
│   ├── useRoles.ts             # Role management state & actions
│   ├── useProviderModels.ts    # Provider/model data management
│   └── __tests__/              # Hook tests
├── lib/                 # Utility libraries
│   └── api.ts                  # API client with error handling
├── pages/               # Next.js pages
│   ├── _app.tsx               # App configuration
│   └── index.tsx              # Main dashboard page
├── styles/              # Global styles
│   └── globals.css            # Tailwind + custom styles
└── types/               # TypeScript type definitions
    ├── api.ts                 # API-related types
    └── role.ts                # Role configuration types
```

## 🚀 Features

### Role Management
- **Create, Read, Update, Delete** role configurations
- **Tab-based interface** for switching between roles
- **Real-time validation** with error handling
- **Bulk operations** support

### LLM Provider Support
- **Ollama**: Local models (no API key required)
- **OpenRouter**: Multi-provider access
- **OpenAI**: GPT models with cost tracking
- **Anthropic**: Claude models with advanced features

### Configuration Options
- **Provider Selection**: With feature descriptions
- **Model Selection**: Context length and cost information
- **API Key Management**: Secure input with format validation
- **Advanced Settings**: Cost limits, max tokens, temperature
- **Enable/Disable**: Role activation controls

### User Experience
- **Responsive Design**: Works on desktop and mobile
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Loading States**: Progress indicators during operations
- **Error Handling**: User-friendly error messages
- **Form Validation**: Real-time validation with helpful hints

## 🧪 Testing

### Test Coverage

The frontend maintains high test coverage across all components and hooks:

- **Component Tests**: UI behavior, user interactions, accessibility
- **Hook Tests**: State management, API interactions, error handling
- **Integration Tests**: Complete workflows and user journeys

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

### Test Structure

```
src/components/__tests__/
├── ApiKeyInput.test.tsx         # API key input validation
├── ConfigManager.test.tsx       # Integration workflows
├── ModelSelector.test.tsx       # Model selection logic
└── ProviderSelector.test.tsx    # Provider selection behavior

src/hooks/__tests__/
└── useRoles.test.ts            # State management & API calls
```

## 🎨 Design System

### Colors

- **Primary**: Blue variants for actions and focus states
- **Secondary**: Gray variants for secondary actions
- **Success**: Green for successful operations
- **Warning**: Yellow for warnings and unsaved changes
- **Error**: Red for errors and validation issues

### Components

- **Cards**: Consistent spacing and shadows
- **Forms**: Standardized inputs with validation states
- **Buttons**: Primary, secondary, and danger variants
- **Badges**: Status indicators with semantic colors
- **Alerts**: Information, success, warning, error styles

### Typography

- **Font Family**: Inter for UI, JetBrains Mono for code
- **Font Weights**: 400 (regular), 500 (medium), 600 (semibold), 700 (bold)
- **Font Sizes**: Responsive scale from xs to 3xl

## 🔧 API Integration

### API Client

The `lib/api.ts` file provides a robust HTTP client with:

- **Automatic retries** for failed requests
- **Request/response interceptors** for auth and logging
- **Error handling** with consistent error formats
- **TypeScript support** for all endpoints
- **Development logging** for debugging

### Custom Hooks

#### useRoles Hook

```typescript
const {
  // State
  roles,
  loading,
  error,

  // Actions
  refreshRoles,
  createRole,
  updateRole,
  deleteRole,
  clearError,

  // Utilities
  getRoleNames,
  getRoleByName,
  isRoleEnabled,
} = useRoles();
```

#### useProviderModels Hook

```typescript
const {
  providers,
  modelsByProvider,
  loading,
  error,
  refreshProviderModels,
  getModelsForProvider,
  getProviderInfo,
} = useProviderModels();
```

## 🔒 Security Features

### API Key Management
- **Format validation** for each provider
- **Masked input** with show/hide toggle
- **Secure transmission** with HTTPS
- **No local storage** of raw keys

### Input Validation
- **Real-time validation** with immediate feedback
- **XSS protection** through proper escaping
- **CSRF protection** via API tokens
- **Rate limiting** awareness

### Accessibility
- **WCAG 2.1 AA compliant** components
- **Keyboard navigation** support
- **Screen reader** compatibility
- **Focus management** for modals and forms

## 🚀 Development

### Setup

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Scripts

```bash
npm run dev          # Development server
npm run build        # Production build
npm run start        # Production server
npm run lint         # ESLint checking
npm run lint:fix     # Fix linting issues
npm run type-check   # TypeScript checking
npm run format       # Format code with Prettier
npm run test         # Run tests
npm run test:watch   # Test in watch mode
npm run test:coverage # Coverage report
```

### Environment Variables

```bash
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000  # Backend API URL
```

## 📋 Component Reference

### ConfigManager
Main container component that orchestrates role management.

**Props**: None
**Features**: Role tabs, create/delete workflows, error handling

### RoleConfigPanel
Individual role configuration interface.

**Props**:
- `roleName: string` - Name of the role being configured
- `configuration: RoleConfiguration` - Current configuration
- `onConfigurationChange: (updates) => void` - Handle config changes
- `onSave: () => void` - Save configuration
- `isLoading?: boolean` - Loading state
- `error?: ApiError` - Error state

### ProviderSelector
LLM provider selection with feature descriptions.

**Props**:
- `value: LLMProvider` - Selected provider
- `onChange: (provider) => void` - Handle selection
- `providers?: ProviderOption[]` - Available providers
- `disabled?: boolean` - Disabled state
- `error?: string` - Validation error

### ModelSelector
Model selection with cost and context information.

**Props**:
- `value: string` - Selected model
- `onChange: (model) => void` - Handle selection
- `models: ModelOption[]` - Available models
- `provider: LLMProvider` - Current provider
- `disabled?: boolean` - Disabled state
- `error?: string` - Validation error

### ApiKeyInput
Secure API key input with validation.

**Props**:
- `value: string` - API key value
- `onChange: (key) => void` - Handle changes
- `provider: LLMProvider` - Provider for validation
- `required?: boolean` - Required field
- `disabled?: boolean` - Disabled state
- `error?: string` - Validation error

## 🔄 Future Enhancements

### Planned Features
- **Real-time updates** via WebSocket
- **Bulk role operations** (import/export)
- **Usage analytics** and cost tracking
- **Model performance** metrics
- **Advanced configuration** templates

### Technical Improvements
- **State persistence** across sessions
- **Optimistic updates** for better UX
- **Offline support** with service workers
- **Progressive enhancement** features

## 🐛 Troubleshooting

### Common Issues

**API Connection Errors**
- Check `NEXT_PUBLIC_API_BASE_URL` environment variable
- Verify backend is running on specified port
- Check browser network tab for CORS issues

**TypeScript Errors**
- Run `npm run type-check` for detailed errors
- Ensure all props match interface definitions
- Check for missing type imports

**Test Failures**
- Clear Jest cache: `npm test -- --clearCache`
- Update snapshots: `npm test -- --updateSnapshot`
- Check mock configurations in test files

### Performance Optimization

- Use React DevTools Profiler for performance analysis
- Implement `React.memo()` for expensive components
- Use `useMemo()` and `useCallback()` for optimization
- Monitor bundle size with `npm run analyze`

---

## 📄 License

This project is part of the AI Coding Agent system. See the main project README for licensing information.