# Project: AI Coding Agent
# Purpose: Core approval management system with request lifecycle handling and user interaction

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path

from .approval_models import (
    ApprovalRequest, ApprovalResponse, ApprovalStatus, ApprovalType,
    RiskLevel, ApprovalAuditEntry, ApprovalConfiguration, ApprovalMetrics,
    ApprovalWorkflow, ApprovalWorkflowStep, ApprovalNotification
)
from ..models.validation_models import Task, Phase, Step


class ApprovalManager:
    """
    Core approval management system that handles the complete lifecycle of approval requests.

    Provides functionality for:
    - Creating and managing approval requests
    - Risk assessment and automatic approvals
    - User notification and response handling
    - Timeout management and escalation
    - Audit trail and compliance tracking
    - Workflow orchestration for complex approvals
    """

    def __init__(self, project_root: str = "/workspace"):
        self.project_root = Path(project_root)
        self.logger = logging.getLogger("approval_manager")

        # Storage for active requests and configuration
        self._active_requests: Dict[str, ApprovalRequest] = {}
        self._audit_trail: List[ApprovalAuditEntry] = []
        self._configuration = ApprovalConfiguration()
        self._metrics = ApprovalMetrics()

        # Event handlers and notifications
        self._event_handlers: Dict[str, List[Callable]] = {
            'request_created': [],
            'request_approved': [],
            'request_rejected': [],
            'request_timeout': [],
            'request_cancelled': []
        }

        # Notification system (to be integrated with WebSocket/email services)
        self._notification_queue: List[ApprovalNotification] = []
        self._websocket_connections: Dict[str, Any] = {}  # user_id -> websocket connection

        # Background task management
        self._background_tasks: set = set()
        self._timeout_checker_running = False

        self.logger.info("Approval Manager initialized")

    async def initialize(self):
        """Initialize the approval manager and start background tasks"""
        # Start timeout checker
        await self._start_timeout_checker()

        # Load configuration from environment or defaults
        await self._load_configuration()

        self.logger.info("Approval Manager fully initialized")

    # Core approval request management

    async def create_approval_request(
        self,
        user_id: str,
        approval_type: ApprovalType,
        title: str,
        description: str,
        item_type: str,
        item_id: str,
        **kwargs
    ) -> ApprovalRequest:
        """
        Create a new approval request with risk assessment and automatic handling.

        Args:
            user_id: ID of the user who will approve
            approval_type: Type of approval being requested
            title: Short title for the approval
            description: Detailed description
            item_type: Type of item being approved ('phase', 'step', 'task', etc.)
            item_id: ID of the item being approved
            **kwargs: Additional approval request parameters

        Returns:
            ApprovalRequest: The created approval request
        """
        # Create the approval request
        request = ApprovalRequest(
            user_id=user_id,
            approval_type=approval_type,
            title=title,
            description=description,
            item_type=item_type,
            item_id=item_id,
            **kwargs
        )

        # Perform risk assessment
        await self._assess_risk(request)

        # Check for auto-approval conditions
        if await self._should_auto_approve(request):
            request.status = ApprovalStatus.APPROVED
            request.responded_at = datetime.now()
            request.response_comments = "Auto-approved based on risk assessment"

            self.logger.info(f"Auto-approved request: {request.id} ({request.title})")

            # Record audit entry
            await self._record_audit_entry(
                request.id, user_id, "auto_approved",
                f"Auto-approved {approval_type} request for {item_type} {item_id}"
            )

            # Trigger approval event handlers
            await self._trigger_event_handlers('request_approved', request)

        else:
            # Store active request
            self._active_requests[request.id] = request

            # Send notifications
            await self._send_approval_notification(request)

            # Record audit entry
            await self._record_audit_entry(
                request.id, user_id, "created",
                f"Created {approval_type} request for {item_type} {item_id}"
            )

            # Trigger creation event handlers
            await self._trigger_event_handlers('request_created', request)

            self.logger.info(f"Created approval request: {request.id} ({request.title})")

        # Update metrics
        self._metrics.total_requests += 1
        if request.status == ApprovalStatus.APPROVED:
            self._metrics.auto_approved_count += 1

        return request

    async def respond_to_approval(
        self,
        approval_id: str,
        user_id: str,
        decision: ApprovalStatus,
        comments: Optional[str] = None,
        **kwargs
    ) -> bool:
        """
        Respond to an approval request with approve/reject decision.

        Args:
            approval_id: ID of the approval request
            user_id: ID of the user responding
            decision: APPROVED or REJECTED
            comments: Optional comments from the user
            **kwargs: Additional response metadata

        Returns:
            bool: True if response was processed successfully
        """
        request = self._active_requests.get(approval_id)
        if not request:
            self.logger.error(f"Approval request not found: {approval_id}")
            return False

        if request.status != ApprovalStatus.PENDING:
            self.logger.warning(f"Approval request {approval_id} is not pending (status: {request.status})")
            return False

        if request.is_expired:
            self.logger.warning(f"Approval request {approval_id} has expired")
            await self._handle_timeout(request)
            return False

        # Validate user can respond
        if user_id != request.user_id:
            self.logger.error(f"User {user_id} not authorized to respond to approval {approval_id}")
            return False

        # Record response
        response_time = time.time()
        request.status = decision
        request.responded_at = datetime.now()
        request.response_user_id = user_id
        request.response_comments = comments
        request.response_metadata.update(kwargs)

        # Calculate response time
        created_timestamp = request.created_at.timestamp()
        response_time_seconds = response_time - created_timestamp

        # Create response object
        response = ApprovalResponse(
            approval_id=approval_id,
            user_id=user_id,
            decision=decision,
            comments=comments,
            response_time_seconds=response_time_seconds,
            response_metadata=kwargs
        )

        # Remove from active requests
        del self._active_requests[approval_id]

        # Record audit entry
        await self._record_audit_entry(
            approval_id, user_id, decision.value,
            f"User responded {decision.value} to {request.approval_type} request",
            {"response_time_seconds": response_time_seconds, "comments": comments}
        )

        # Update metrics
        if decision == ApprovalStatus.APPROVED:
            self._metrics.approved_count += 1
        elif decision == ApprovalStatus.REJECTED:
            self._metrics.rejected_count += 1

        # Update response time metrics
        self._update_response_time_metrics(response_time_seconds)

        # Trigger event handlers
        event_type = 'request_approved' if decision == ApprovalStatus.APPROVED else 'request_rejected'
        await self._trigger_event_handlers(event_type, request, response)

        # Send confirmation notification
        await self._send_response_notification(request, response)

        self.logger.info(f"Approval {approval_id} {decision.value} by user {user_id}")

        return True

    async def cancel_approval_request(self, approval_id: str, user_id: str, reason: str = "") -> bool:
        """
        Cancel a pending approval request.

        Args:
            approval_id: ID of the approval request to cancel
            user_id: ID of the user cancelling (must be request creator or admin)
            reason: Reason for cancellation

        Returns:
            bool: True if cancellation was successful
        """
        request = self._active_requests.get(approval_id)
        if not request:
            self.logger.error(f"Approval request not found: {approval_id}")
            return False

        if request.status != ApprovalStatus.PENDING:
            self.logger.warning(f"Cannot cancel approval {approval_id} - status: {request.status}")
            return False

        # Update status
        request.status = ApprovalStatus.CANCELLED
        request.responded_at = datetime.now()
        request.response_comments = f"Cancelled: {reason}"

        # Remove from active requests
        del self._active_requests[approval_id]

        # Record audit entry
        await self._record_audit_entry(
            approval_id, user_id, "cancelled",
            f"Approval request cancelled: {reason}"
        )

        # Update metrics
        self._metrics.cancelled_count += 1

        # Trigger event handlers
        await self._trigger_event_handlers('request_cancelled', request)

        self.logger.info(f"Approval {approval_id} cancelled by user {user_id}: {reason}")

        return True

    # Query and management methods

    def get_approval_request(self, approval_id: str) -> Optional[ApprovalRequest]:
        """Get an approval request by ID"""
        return self._active_requests.get(approval_id)

    def get_pending_approvals(self, user_id: Optional[str] = None) -> List[ApprovalRequest]:
        """Get all pending approval requests, optionally filtered by user"""
        requests = [req for req in self._active_requests.values() if req.status == ApprovalStatus.PENDING]

        if user_id:
            requests = [req for req in requests if req.user_id == user_id]

        # Sort by priority (high risk first, then by creation time)
        return sorted(requests, key=lambda x: (
            x.risk_level == RiskLevel.CRITICAL,
            x.risk_level == RiskLevel.HIGH,
            x.created_at
        ), reverse=True)

    def get_approval_metrics(self) -> ApprovalMetrics:
        """Get current approval system metrics"""
        # Update real-time metrics
        self._metrics.active_approvers = len(set(req.user_id for req in self._active_requests.values()))

        return self._metrics

    def get_audit_trail(self, approval_id: Optional[str] = None) -> List[ApprovalAuditEntry]:
        """Get audit trail entries, optionally filtered by approval ID"""
        if approval_id:
            return [entry for entry in self._audit_trail if entry.approval_id == approval_id]
        return self._audit_trail.copy()

    # Risk assessment and auto-approval

    async def _assess_risk(self, request: ApprovalRequest):
        """Assess risk level for an approval request"""
        risk_score = 0
        risk_factors = []

        # Base risk by approval type
        type_risk_map = {
            ApprovalType.PHASE_COMPLETION: 2,
            ApprovalType.TASK_EXECUTION: 1,
            ApprovalType.DESTRUCTIVE_OPERATION: 5,
            ApprovalType.CONFIGURATION_CHANGE: 3,
            ApprovalType.DEPLOYMENT: 4,
            ApprovalType.ROLLBACK: 3,
            ApprovalType.FILE_MODIFICATION: 2,
            ApprovalType.DATABASE_MIGRATION: 4,
            ApprovalType.SECURITY_CHANGE: 5,
            ApprovalType.PRODUCTION_DEPLOY: 5
        }

        risk_score += type_risk_map.get(request.approval_type, 2)

        # Risk factors from affected resources
        if request.files_affected:
            if any('config' in f or 'security' in f for f in request.files_affected):
                risk_score += 2
                risk_factors.append("Configuration/security files affected")

            if len(request.files_affected) > 10:
                risk_score += 1
                risk_factors.append("Large number of files affected")

        if request.services_affected:
            if len(request.services_affected) > 3:
                risk_score += 2
                risk_factors.append("Multiple services affected")

            if any('database' in s or 'auth' in s for s in request.services_affected):
                risk_score += 2
                risk_factors.append("Critical services affected")

        # Determine risk level
        if risk_score <= 2:
            request.risk_level = RiskLevel.LOW
        elif risk_score <= 4:
            request.risk_level = RiskLevel.MEDIUM
        elif risk_score <= 7:
            request.risk_level = RiskLevel.HIGH
        else:
            request.risk_level = RiskLevel.CRITICAL

        # Generate risk assessment text
        if risk_factors:
            request.risk_assessment = f"Risk level {request.risk_level.value}: {', '.join(risk_factors)}"
        else:
            request.risk_assessment = f"Standard {request.risk_level.value} risk for {request.approval_type.value}"

    async def _should_auto_approve(self, request: ApprovalRequest) -> bool:
        """Determine if request should be auto-approved"""
        if not self._configuration.auto_approve_low_risk:
            return False

        if request.risk_level != RiskLevel.LOW:
            return False

        if request.approval_type in [ApprovalType.DESTRUCTIVE_OPERATION, ApprovalType.SECURITY_CHANGE]:
            return False

        if self._configuration.require_approval_for_destructive and 'delete' in request.description.lower():
            return False

        return True

    # Notification system

    async def _send_approval_notification(self, request: ApprovalRequest):
        """Send notification for new approval request"""
        notification = ApprovalNotification(
            approval_id=request.id,
            user_id=request.user_id,
            notification_type="request",
            channel="websocket",
            title=f"Approval Required: {request.title}",
            message=f"Please review and approve: {request.description}",
            priority="high" if request.is_high_risk else "normal"
        )

        await self._queue_notification(notification)

        # Also send email for high-risk requests
        if request.is_high_risk:
            email_notification = ApprovalNotification(
                approval_id=request.id,
                user_id=request.user_id,
                notification_type="request",
                channel="email",
                title=f"High-Risk Approval Required: {request.title}",
                message=f"High-risk approval needed: {request.description}\n\nRisk: {request.risk_assessment}",
                priority="high"
            )
            await self._queue_notification(email_notification)

    async def _send_response_notification(self, request: ApprovalRequest, response: ApprovalResponse):
        """Send confirmation notification after approval response"""
        notification = ApprovalNotification(
            approval_id=request.id,
            user_id=request.user_id,
            notification_type="decision",
            channel="websocket",
            title=f"Approval {response.decision.value.title()}",
            message=f"Your decision has been recorded: {response.decision.value}",
            priority="normal"
        )

        await self._queue_notification(notification)

    async def _queue_notification(self, notification: ApprovalNotification):
        """Queue a notification for delivery"""
        self._notification_queue.append(notification)

        # For WebSocket notifications, try immediate delivery
        if notification.channel == "websocket":
            await self._deliver_websocket_notification(notification)

    async def _deliver_websocket_notification(self, notification: ApprovalNotification):
        """Deliver notification via WebSocket if connected"""
        connection = self._websocket_connections.get(notification.user_id)
        if connection:
            try:
                message = {
                    "type": "approval_notification",
                    "approval_id": notification.approval_id,
                    "notification_type": notification.notification_type,
                    "title": notification.title,
                    "message": notification.message,
                    "priority": notification.priority,
                    "timestamp": notification.scheduled_at.isoformat()
                }

                # Send via WebSocket (actual implementation depends on WebSocket framework)
                # await connection.send_json(message)

                notification.sent_at = datetime.now()
                notification.delivered_at = datetime.now()
                notification.status = "delivered"

            except Exception as e:
                self.logger.error(f"Failed to deliver WebSocket notification: {e}")
                notification.status = "failed"

    # Background task management

    async def _start_timeout_checker(self):
        """Start the background task that checks for expired approvals"""
        if self._timeout_checker_running:
            return

        self._timeout_checker_running = True
        task = asyncio.create_task(self._timeout_checker_loop())
        self._background_tasks.add(task)
        task.add_done_callback(self._background_tasks.discard)

    async def _timeout_checker_loop(self):
        """Background loop to check for expired approval requests"""
        while self._timeout_checker_running:
            try:
                current_time = datetime.now()
                expired_requests = []

                for request in list(self._active_requests.values()):
                    if request.status == ApprovalStatus.PENDING and request.expires_at <= current_time:
                        expired_requests.append(request)

                for request in expired_requests:
                    await self._handle_timeout(request)

                # Check every 30 seconds
                await asyncio.sleep(30)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in timeout checker: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def _handle_timeout(self, request: ApprovalRequest):
        """Handle an expired approval request"""
        request.status = ApprovalStatus.TIMEOUT
        request.responded_at = datetime.now()
        request.response_comments = "Request expired due to timeout"

        # Remove from active requests
        if request.id in self._active_requests:
            del self._active_requests[request.id]

        # Record audit entry
        await self._record_audit_entry(
            request.id, None, "timeout",
            f"Approval request timed out after {request.timeout_minutes} minutes"
        )

        # Update metrics
        self._metrics.timeout_count += 1

        # Trigger event handlers
        await self._trigger_event_handlers('request_timeout', request)

        # Send timeout notification
        timeout_notification = ApprovalNotification(
            approval_id=request.id,
            user_id=request.user_id,
            notification_type="timeout",
            channel="websocket",
            title=f"Approval Timeout: {request.title}",
            message=f"Approval request has expired: {request.description}",
            priority="high"
        )
        await self._queue_notification(timeout_notification)

        self.logger.warning(f"Approval {request.id} timed out")

    # Event handling and audit

    async def _record_audit_entry(
        self,
        approval_id: str,
        user_id: Optional[str],
        action: str,
        details: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Record an audit trail entry"""
        entry = ApprovalAuditEntry(
            approval_id=approval_id,
            user_id=user_id,
            action=action,
            details=details,
            metadata=metadata or {}
        )

        self._audit_trail.append(entry)

        # Keep audit trail to reasonable size (last 10000 entries)
        if len(self._audit_trail) > 10000:
            self._audit_trail = self._audit_trail[-10000:]

    async def _trigger_event_handlers(self, event_type: str, *args):
        """Trigger registered event handlers"""
        handlers = self._event_handlers.get(event_type, [])
        for handler in handlers:
            try:
                await handler(*args)
            except Exception as e:
                self.logger.error(f"Error in event handler for {event_type}: {e}")

    def register_event_handler(self, event_type: str, handler: Callable):
        """Register an event handler for approval events"""
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(handler)

    # Metrics and configuration

    def _update_response_time_metrics(self, response_time_seconds: float):
        """Update response time metrics"""
        response_time_minutes = response_time_seconds / 60

        # Simple running average (in production, consider more sophisticated metrics)
        total_responses = (self._metrics.approved_count + self._metrics.rejected_count)
        if total_responses == 1:
            self._metrics.average_response_time_minutes = response_time_minutes
        else:
            current_avg = self._metrics.average_response_time_minutes
            self._metrics.average_response_time_minutes = (
                (current_avg * (total_responses - 1) + response_time_minutes) / total_responses
            )

        # Update max if needed
        if response_time_minutes > self._metrics.max_response_time_minutes:
            self._metrics.max_response_time_minutes = response_time_minutes

    async def _load_configuration(self):
        """Load approval configuration from centralized settings"""
        from ..core.config import settings

        self._configuration.approval_enabled = settings.APPROVAL_ENABLED
        self._configuration.auto_approve_low_risk = settings.AUTO_APPROVE_LOW_RISK
        self._configuration.default_timeout_minutes = settings.APPROVAL_TIMEOUT_MINUTES

    # WebSocket connection management

    def register_websocket_connection(self, user_id: str, connection: Any):
        """Register a WebSocket connection for a user"""
        self._websocket_connections[user_id] = connection
        self.logger.info(f"Registered WebSocket connection for user {user_id}")

    def unregister_websocket_connection(self, user_id: str):
        """Unregister a WebSocket connection for a user"""
        if user_id in self._websocket_connections:
            del self._websocket_connections[user_id]
            self.logger.info(f"Unregistered WebSocket connection for user {user_id}")

    # Cleanup

    async def shutdown(self):
        """Shutdown the approval manager and cleanup resources"""
        self._timeout_checker_running = False

        # Cancel all background tasks
        for task in self._background_tasks:
            task.cancel()

        # Wait for tasks to complete
        if self._background_tasks:
            await asyncio.gather(*self._background_tasks, return_exceptions=True)

        self.logger.info("Approval Manager shutdown complete")