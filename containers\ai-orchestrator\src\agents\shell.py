# Project: AI Coding Agent
# Purpose: Shell Agent for command line operations and system tasks

import asyncio
import logging
from typing import Dict, Any

from .base_agent import BaseAgent
from ..models.validation_models import (
    AgentType, Task, TaskResult, ValidationResult, TaskType
)


class ShellAgent(BaseAgent):
    """
    Shell Agent specialized in command-line operations and system tasks.

    Capabilities:
    - Command execution and automation
    - System administration tasks
    - File system operations
    - Build and deployment scripts
    """

    def __init__(self):
        super().__init__(AgentType.SHELL, max_concurrent_tasks=1)
        self.supported_operations = ["file_ops", "process_mgmt", "network_ops", "system_config"]
        self.logger.info("Shell Agent initialized with command execution capabilities")

    async def _execute_core_task(self, task: Task) -> TaskResult:
        """Execute shell-specific tasks."""
        self.logger.info(f"Shell Agent executing task: {task.title}")

        try:
            # Simulate command execution with safety checks
            command = task.parameters.get("command", "")
            if self._is_safe_command(command):
                await asyncio.sleep(0.1)

                return TaskResult(
                    success=True,
                    output=f"Shell task completed: {task.title}",
                    metadata={"agent_type": "shell", "command": command}
                )
            else:
                return TaskResult(
                    success=False,
                    error=f"Command not allowed for security reasons: {command}",
                    metadata={"error_type": "security_violation"}
                )

        except Exception as e:
            return TaskResult(
                success=False,
                error=f"Shell task failed: {str(e)}",
                metadata={"error_type": "execution_error"}
            )

    def _is_safe_command(self, command: str) -> bool:
        """Check if a command is safe to execute."""
        restricted_commands = ["rm -rf /", "del /s", "format", "fdisk"]
        return not any(restricted in command.lower() for restricted in restricted_commands)

    async def _validate_agent_specific_prerequisites(self, task: Task) -> ValidationResult:
        """Validate shell-specific task prerequisites."""
        return ValidationResult.success("Shell Agent ready for system operations")

    async def _validate_agent_specific_completion(self, task: Task, result: TaskResult) -> ValidationResult:
        """Validate shell-specific task completion."""
        if result.success:
            return ValidationResult.success("Shell task completed successfully")
        else:
            return ValidationResult.failure(f"Shell task failed: {result.error}")