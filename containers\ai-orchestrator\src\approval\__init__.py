# Project: AI Coding Agent
# Purpose: Approval system package initialization - exports approval management components

"""
Approval System Package

This package provides comprehensive user approval capabilities for the AI coding agent system,
including approval request management, interactive approval workflows, and audit trail tracking.

Components:
- ApprovalManager: Core approval management with request lifecycle handling
- ApprovalModels: Data models for approval requests, responses, and audit trails

Usage:
    from approval import ApprovalManager, ApprovalRequest, ApprovalResponse

    # Initialize approval manager
    approval_manager = ApprovalManager()

    # Create approval request
    request = await approval_manager.create_approval_request(...)
"""

from .approval_manager import ApprovalManager
from .approval_models import (
    ApprovalRequest,
    ApprovalResponse,
    ApprovalType,
    ApprovalStatus,
    ApprovalAuditEntry
)

__all__ = [
    "ApprovalManager",
    "ApprovalRequest",
    "ApprovalResponse",
    "ApprovalType",
    "ApprovalStatus",
    "ApprovalAuditEntry",
]

__version__ = "1.0.0"