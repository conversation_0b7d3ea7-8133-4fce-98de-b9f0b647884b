# Copilot Instructions for AI Coding Agent Project

1. **Follow the Roadmap:** Always refer to the latest roadmap in `docs/Projectroadmap.md` before making changes. Complete tasks in the order specified unless directed otherwise.
2. **Directory Organization:** 
   - Place backend code in `/backend`
   - Place frontend/UI code in `/frontend`
   - Place infrastructure (Docker, configs) in `/infra`
   - Place documentation in `/docs`
   - Place tests in `/tests`
   - Place scripts/utilities in `/scripts`
3. **Naming Conventions:** Use clear, descriptive names for files, folders, and variables. Follow Python and React best practices.
4. **Documentation:** Update documentation in `/docs` for any new features, changes, or setup steps.
5. **Security:** Apply container and code security best practices. Do not expose secrets or sensitive data.
6. **Authentication:** Integrate Supabase Auth for all user-facing and API endpoints.
7. **Testing:** Add or update tests for new code in `/tests`. Ensure all tests pass before merging changes.
8. **CI/CD:** Use the CI/CD pipeline for builds, tests, and deployments. Do not bypass automated checks.
9. **Monitoring:** Ensure monitoring/logging is enabled for all containers and services.
10. **Feedback:** After each milestone, review progress and update the roadmap if needed.
11. **Backups:** Ensure database backup scripts/configs are present and documented.
12. **Resource Usage:** Monitor and optimize resource usage for containers and services.
13. **Communication:** Clearly comment code and communicate changes in commit messages and documentation.
