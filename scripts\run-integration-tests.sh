#!/bin/bash

# Integration Test Runner Script
# Runs comprehensive integration tests for the AI Coding Agent system

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_DIR="$PROJECT_ROOT/tests/integration"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default configuration
DOCKER_COMPOSE_FILE="docker-compose.yml"
DOCKER_COMPOSE_DEV_FILE="docker-compose.dev.yml"
ENVIRONMENT="development"
TIMEOUT="120"
VERBOSE=""
CLEANUP="true"
SERVICES_ONLY=""
PERFORMANCE_TESTS=""

# Function definitions
print_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Run integration tests for the AI Coding Agent system"
    echo
    echo "OPTIONS:"
    echo "  -e, --environment ENV    Environment to test (development|production) [default: development]"
    echo "  -t, --timeout SECONDS    Timeout for service startup [default: 120]"
    echo "  -v, --verbose           Enable verbose test output"
    echo "  --no-cleanup            Don't cleanup test data after running"
    echo "  --services-only         Only start services, don't run tests"
    echo "  --performance           Include performance tests"
    echo "  --help                  Show this help message"
    echo
    echo "EXAMPLES:"
    echo "  $0                      # Run tests in development mode"
    echo "  $0 -e production        # Run tests against production build"
    echo "  $0 --verbose            # Run with verbose output"
    echo "  $0 --services-only      # Just start services for manual testing"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."

    local missing_deps=()

    # Check for required tools
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi

    if ! command -v docker-compose &> /dev/null; then
        missing_deps+=("docker-compose")
    fi

    if ! command -v python3 &> /dev/null; then
        missing_deps+=("python3")
    fi

    if ! command -v pip &> /dev/null; then
        missing_deps+=("pip")
    fi

    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        log_error "Please install the missing dependencies and try again"
        exit 1
    fi

    # Check Python packages
    if ! python3 -c "import pytest, requests" &> /dev/null; then
        log_warning "Installing Python test dependencies..."
        pip install -r "$PROJECT_ROOT/requirements-dev.txt" || {
            log_error "Failed to install Python dependencies"
            exit 1
        }
    fi

    log_success "All dependencies are available"
}

setup_environment() {
    log_info "Setting up test environment..."

    cd "$PROJECT_ROOT"

    # Ensure .env file exists
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            log_info "Creating .env file from .env.example"
            cp .env.example .env
        else
            log_warning "No .env or .env.example file found"
        fi
    fi

    # Set test environment variables
    export PYTEST_CURRENT_TEST=true
    export TEST_API_BASE_URL="http://localhost:8000"
    export TEST_FRONTEND_BASE_URL="http://localhost:3000"
    export TEST_TIMEOUT="$TIMEOUT"

    if [ "$ENVIRONMENT" = "development" ]; then
        export NODE_ENV=development
        export DEBUG=true
    else
        export NODE_ENV=production
    fi

    log_success "Environment setup complete"
}

start_services() {
    log_info "Starting Docker services for $ENVIRONMENT environment..."

    local compose_args=("-f" "$DOCKER_COMPOSE_FILE")

    if [ "$ENVIRONMENT" = "development" ]; then
        compose_args+=("-f" "$DOCKER_COMPOSE_DEV_FILE")
    fi

    # Stop any existing services
    docker-compose "${compose_args[@]}" down --remove-orphans || true

    # Start services
    log_info "Building and starting services..."
    docker-compose "${compose_args[@]}" up -d --build

    # Wait for services to be healthy
    log_info "Waiting for services to be healthy (timeout: ${TIMEOUT}s)..."

    local start_time=$(date +%s)
    local timeout_time=$((start_time + TIMEOUT))

    while [ $(date +%s) -lt $timeout_time ]; do
        local all_healthy=true

        # Check each service
        for service in postgresql redis ai-orchestrator admin-dashboard; do
            if ! docker-compose "${compose_args[@]}" ps | grep -q "$service.*healthy\|$service.*Up"; then
                all_healthy=false
                break
            fi
        done

        if [ "$all_healthy" = true ]; then
            log_success "All services are healthy"
            return 0
        fi

        log_info "Waiting for services to be ready..."
        sleep 5
    done

    log_error "Services failed to become healthy within ${TIMEOUT} seconds"
    log_info "Service status:"
    docker-compose "${compose_args[@]}" ps

    # Show logs for debugging
    log_info "Recent logs:"
    docker-compose "${compose_args[@]}" logs --tail=20

    return 1
}

run_tests() {
    log_info "Running integration tests..."

    local pytest_args=(
        "$TEST_DIR"
        "-v"
        "--tb=short"
        "--strict-markers"
        "-m" "integration"
    )

    if [ -n "$VERBOSE" ]; then
        pytest_args+=("-s" "--capture=no")
    fi

    if [ -n "$PERFORMANCE_TESTS" ]; then
        pytest_args+=("--runslow")
    else
        pytest_args+=("-m" "not performance")
    fi

    if [ "$CLEANUP" = "false" ]; then
        export TEST_CLEANUP_ON_FAILURE=false
    fi

    # Run tests with proper exit code handling
    set +e
    python3 -m pytest "${pytest_args[@]}"
    local test_exit_code=$?
    set -e

    if [ $test_exit_code -eq 0 ]; then
        log_success "All integration tests passed!"
    else
        log_error "Some integration tests failed (exit code: $test_exit_code)"

        # Show recent logs for debugging
        log_info "Recent service logs for debugging:"
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs --tail=50 ai-orchestrator admin-dashboard
    fi

    return $test_exit_code
}

cleanup_services() {
    if [ "$CLEANUP" = "true" ]; then
        log_info "Cleaning up Docker services..."

        local compose_args=("-f" "$DOCKER_COMPOSE_FILE")
        if [ "$ENVIRONMENT" = "development" ]; then
            compose_args+=("-f" "$DOCKER_COMPOSE_DEV_FILE")
        fi

        docker-compose "${compose_args[@]}" down --remove-orphans -v

        # Clean up any dangling images or containers
        docker system prune -f || true

        log_success "Cleanup complete"
    else
        log_info "Skipping cleanup (services left running for debugging)"
    fi
}

generate_report() {
    log_info "Generating test report..."

    local report_file="$PROJECT_ROOT/test-results/integration-test-report.html"
    local report_dir="$(dirname "$report_file")"

    mkdir -p "$report_dir"

    # Run tests again with HTML report generation
    python3 -m pytest "$TEST_DIR" \
        --html="$report_file" \
        --self-contained-html \
        -m "integration and not performance" \
        --tb=short \
        -q || true

    if [ -f "$report_file" ]; then
        log_success "Test report generated: $report_file"
    fi
}

main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -t|--timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE="true"
                shift
                ;;
            --no-cleanup)
                CLEANUP="false"
                shift
                ;;
            --services-only)
                SERVICES_ONLY="true"
                shift
                ;;
            --performance)
                PERFORMANCE_TESTS="true"
                shift
                ;;
            --help)
                print_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                print_usage
                exit 1
                ;;
        esac
    done

    # Validate environment
    if [ "$ENVIRONMENT" != "development" ] && [ "$ENVIRONMENT" != "production" ]; then
        log_error "Invalid environment: $ENVIRONMENT (must be 'development' or 'production')"
        exit 1
    fi

    log_info "Starting integration tests for AI Coding Agent"
    log_info "Environment: $ENVIRONMENT"
    log_info "Timeout: ${TIMEOUT}s"

    # Trap for cleanup on exit
    trap cleanup_services EXIT

    # Run the test sequence
    check_dependencies
    setup_environment
    start_services

    if [ -n "$SERVICES_ONLY" ]; then
        log_success "Services started successfully. Use Ctrl+C to stop."
        log_info "API available at: http://localhost:8000"
        log_info "Admin Dashboard available at: http://localhost:3000"
        log_info "Run tests manually with: python3 -m pytest tests/integration/ -v"

        # Keep services running
        trap - EXIT
        read -p "Press Enter to stop services..."
        cleanup_services
    else
        local test_exit_code=0

        run_tests || test_exit_code=$?
        generate_report

        if [ $test_exit_code -eq 0 ]; then
            log_success "Integration tests completed successfully!"
        else
            log_error "Integration tests failed!"
        fi

        exit $test_exit_code
    fi
}

# Run main function
main "$@"