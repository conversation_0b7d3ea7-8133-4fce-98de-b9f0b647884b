"""
Unit tests for WebSocket chat functionality and ArchitectAgent chat methods.

These tests verify the proper functioning of:
- WebSocket connection management
- Chat message handling
- ArchitectAgent chat integration
- Error handling and edge cases
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import Test<PERSON>lient
from fastapi import WebSocket
from datetime import datetime

# Import classes to test
from src.services.websocket_manager import WebSocketChatManager, ChatMessage, MessageType
from src.agents.architect import ArchitectAgent
from src.router.websocket_router import router as websocket_router
from src.main import app


class TestWebSocketChatManager:
    """Test cases for WebSocketChatManager."""

    @pytest.fixture
    def chat_manager(self):
        """Create a WebSocketChatManager instance for testing."""
        return WebSocketChatManager()

    @pytest.fixture
    def mock_websocket(self):
        """Create a mock WebSocket for testing."""
        mock_ws = AsyncMock()
        mock_ws.send_text = AsyncMock()
        mock_ws.close = AsyncMock()
        return mock_ws

    @pytest.mark.asyncio
    async def test_connect_user(self, chat_manager, mock_websocket):
        """Test connecting a user to the chat manager."""
        user_id = "test_user_123"

        connection_id = await chat_manager.connect(mock_websocket, user_id)

        assert connection_id is not None
        assert len(connection_id) > 0
        assert connection_id in chat_manager.connections
        assert chat_manager.connections[connection_id].user_id == user_id
        assert chat_manager.connections[connection_id].websocket == mock_websocket

    @pytest.mark.asyncio
    async def test_disconnect_user(self, chat_manager, mock_websocket):
        """Test disconnecting a user from the chat manager."""
        user_id = "test_user_123"

        # Connect first
        connection_id = await chat_manager.connect(mock_websocket, user_id)
        assert connection_id in chat_manager.connections

        # Disconnect
        await chat_manager.disconnect(connection_id)
        assert connection_id not in chat_manager.connections

    @pytest.mark.asyncio
    async def test_send_message_to_connection(self, chat_manager, mock_websocket):
        """Test sending a message to a specific connection."""
        user_id = "test_user_123"

        # Connect user
        connection_id = await chat_manager.connect(mock_websocket, user_id)

        # Send message
        test_message = {
            "type": "test_message",
            "content": "Hello, World!",
            "timestamp": datetime.utcnow().isoformat()
        }

        await chat_manager.send_to_connection(connection_id, test_message)

        # Verify WebSocket send was called
        mock_websocket.send_text.assert_called_once()
        sent_data = mock_websocket.send_text.call_args[0][0]
        sent_message = json.loads(sent_data)
        assert sent_message["content"] == "Hello, World!"

    @pytest.mark.asyncio
    async def test_broadcast_to_all(self, chat_manager):
        """Test broadcasting a message to all connected users."""
        # Connect multiple users
        user1_ws = AsyncMock()
        user2_ws = AsyncMock()

        connection1 = await chat_manager.connect(user1_ws, "user1")
        connection2 = await chat_manager.connect(user2_ws, "user2")

        # Broadcast message
        broadcast_message = {
            "type": "broadcast",
            "content": "System announcement",
            "timestamp": datetime.utcnow().isoformat()
        }

        sent_count = await chat_manager.broadcast_to_all(broadcast_message)

        # Verify message was sent to all connections
        assert sent_count == 2
        user1_ws.send_text.assert_called_once()
        user2_ws.send_text.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_ping_message(self, chat_manager, mock_websocket):
        """Test handling ping messages."""
        user_id = "test_user_123"
        connection_id = await chat_manager.connect(mock_websocket, user_id)

        ping_message = json.dumps({
            "type": "ping",
            "content": "",
            "timestamp": datetime.utcnow().isoformat()
        })

        await chat_manager.handle_message(connection_id, ping_message)

        # Should respond with pong
        mock_websocket.send_text.assert_called()
        response_data = mock_websocket.send_text.call_args[0][0]
        response = json.loads(response_data)
        assert response["type"] == MessageType.PONG.value

    @pytest.mark.asyncio
    async def test_get_connection_stats(self, chat_manager, mock_websocket):
        """Test getting connection statistics."""
        # Connect some users
        await chat_manager.connect(mock_websocket, "user1")
        await chat_manager.connect(AsyncMock(), "user2")

        stats = chat_manager.get_connection_stats()

        assert stats["total_connections"] == 2
        assert stats["unique_users"] == 2
        assert len(stats["connections"]) == 2


class TestArchitectAgentChat:
    """Test cases for ArchitectAgent chat functionality."""

    @pytest.fixture
    def architect_agent(self):
        """Create an ArchitectAgent instance for testing."""
        agent = ArchitectAgent()
        # Mock the LLM service
        agent._llm_service = AsyncMock()
        return agent

    @pytest.mark.asyncio
    async def test_handle_chat_message_success(self, architect_agent):
        """Test successful chat message handling."""
        user_id = "test_user_123"
        message = "Hello, can you help me with a coding problem?"

        # Mock LLM service response
        mock_llm_response = MagicMock()
        mock_llm_response.content = "Of course! I'd be happy to help you with your coding problem. What specific issue are you facing?"
        architect_agent._llm_service.generate.return_value = mock_llm_response

        result = await architect_agent.handle_chat_message(user_id, message)

        assert result["response"] == mock_llm_response.content
        assert result["user_id"] == user_id
        assert result["agent_type"] == "architect"
        assert "timestamp" in result
        assert "conversation_length" in result

    @pytest.mark.asyncio
    async def test_handle_chat_message_conversation_history(self, architect_agent):
        """Test that conversation history is maintained."""
        user_id = "test_user_123"

        # Mock LLM service
        mock_llm_response = MagicMock()
        mock_llm_response.content = "I understand your question."
        architect_agent._llm_service.generate.return_value = mock_llm_response

        # Send first message
        await architect_agent.handle_chat_message(user_id, "First message")

        # Send second message
        await architect_agent.handle_chat_message(user_id, "Second message")

        # Check conversation history
        history = await architect_agent.get_conversation_history(user_id)
        assert len(history) == 4  # 2 user messages + 2 assistant responses
        assert history[0]["role"] == "user"
        assert history[0]["content"] == "First message"
        assert history[1]["role"] == "assistant"
        assert history[2]["role"] == "user"
        assert history[2]["content"] == "Second message"

    @pytest.mark.asyncio
    async def test_handle_chat_message_error_handling(self, architect_agent):
        """Test error handling in chat message processing."""
        user_id = "test_user_123"
        message = "Test message"

        # Make LLM service raise an exception
        architect_agent._llm_service.generate.side_effect = Exception("LLM service error")

        result = await architect_agent.handle_chat_message(user_id, message)

        assert "error processing your message" in result["response"].lower()
        assert result["user_id"] == user_id
        assert "error" in result

    @pytest.mark.asyncio
    async def test_clear_conversation_history(self, architect_agent):
        """Test clearing conversation history."""
        user_id = "test_user_123"

        # Mock LLM service
        mock_llm_response = MagicMock()
        mock_llm_response.content = "Response"
        architect_agent._llm_service.generate.return_value = mock_llm_response

        # Add some conversation history
        await architect_agent.handle_chat_message(user_id, "Test message")
        history = await architect_agent.get_conversation_history(user_id)
        assert len(history) > 0

        # Clear history
        result = await architect_agent.clear_conversation_history(user_id)
        assert result is True

        # Verify history is cleared
        history = await architect_agent.get_conversation_history(user_id)
        assert len(history) == 0

    @pytest.mark.asyncio
    async def test_conversation_history_limit(self, architect_agent):
        """Test that conversation history respects the maximum limit."""
        user_id = "test_user_123"

        # Mock LLM service
        mock_llm_response = MagicMock()
        mock_llm_response.content = "Response"
        architect_agent._llm_service.generate.return_value = mock_llm_response

        # Set a small limit for testing
        architect_agent._max_conversation_history = 4

        # Send multiple messages (more than the limit)
        for i in range(10):
            await architect_agent.handle_chat_message(user_id, f"Message {i}")

        # Check that history is limited
        history = await architect_agent.get_conversation_history(user_id)
        assert len(history) <= architect_agent._max_conversation_history

    @pytest.mark.asyncio
    async def test_get_conversation_context(self, architect_agent):
        """Test getting conversation context for LLM."""
        user_id = "test_user_123"

        # Add some messages to history manually
        architect_agent._conversation_history[user_id] = [
            {"role": "user", "content": "Hello", "timestamp": "2023-01-01T00:00:00"},
            {"role": "assistant", "content": "Hi there!", "timestamp": "2023-01-01T00:00:01"},
            {"role": "user", "content": "How are you?", "timestamp": "2023-01-01T00:00:02"},
            {"role": "assistant", "content": "I'm doing well!", "timestamp": "2023-01-01T00:00:03"},
        ]

        context = architect_agent._get_conversation_context(user_id, max_messages=2)

        assert len(context) == 2
        assert context[0]["content"] == "How are you?"
        assert context[1]["content"] == "I'm doing well!"
        # Timestamps should be excluded from context
        assert "timestamp" not in context[0]


class TestWebSocketRouter:
    """Test cases for WebSocket router endpoints."""

    @pytest.fixture
    def client(self):
        """Create a test client for FastAPI application."""
        return TestClient(app)

    def test_websocket_stats_endpoint(self, client):
        """Test the WebSocket stats endpoint."""
        response = client.get("/ws/stats")
        assert response.status_code == 200

        data = response.json()
        assert "websocket_stats" in data
        assert "architect_agent_status" in data
        assert "chat_endpoint" in data
        assert "timestamp" in data

    @pytest.mark.asyncio
    async def test_broadcast_message_endpoint(self, client):
        """Test the broadcast message endpoint."""
        broadcast_data = {
            "content": "Test broadcast message",
            "type": "system_message"
        }

        response = client.post("/ws/broadcast", json=broadcast_data)
        assert response.status_code == 200

        data = response.json()
        assert data["success"] is True
        assert "recipients" in data
        assert "timestamp" in data


class TestChatMessage:
    """Test cases for ChatMessage model."""

    def test_chat_message_creation(self):
        """Test creating a ChatMessage instance."""
        message = ChatMessage(
            message_id="test_123",
            user_id="user_456",
            content="Hello, world!",
            message_type=MessageType.USER_MESSAGE,
            timestamp=datetime.utcnow(),
            metadata={"source": "test"}
        )

        assert message.message_id == "test_123"
        assert message.user_id == "user_456"
        assert message.content == "Hello, world!"
        assert message.message_type == MessageType.USER_MESSAGE
        assert message.metadata["source"] == "test"

    def test_chat_message_to_dict(self):
        """Test converting ChatMessage to dictionary."""
        message = ChatMessage(
            message_id="test_123",
            user_id="user_456",
            content="Hello, world!",
            message_type=MessageType.USER_MESSAGE,
            timestamp=datetime.utcnow(),
            metadata={}
        )

        message_dict = message.to_dict()

        assert message_dict["message_id"] == "test_123"
        assert message_dict["user_id"] == "user_456"
        assert message_dict["content"] == "Hello, world!"
        assert message_dict["type"] == MessageType.USER_MESSAGE.value


class TestIntegration:
    """Integration tests for the complete chat system."""

    @pytest.mark.asyncio
    async def test_end_to_end_chat_flow(self):
        """Test a complete chat flow from WebSocket to ArchitectAgent and back."""
        # This would be a more complex integration test
        # For now, we'll test the key components work together

        chat_manager = WebSocketChatManager()
        architect_agent = ArchitectAgent()

        # Mock dependencies
        mock_websocket = AsyncMock()
        architect_agent._llm_service = AsyncMock()

        mock_llm_response = MagicMock()
        mock_llm_response.content = "Hello! How can I help you today?"
        architect_agent._llm_service.generate.return_value = mock_llm_response

        # Connect user
        connection_id = await chat_manager.connect(mock_websocket, "test_user")

        # Simulate user message
        user_message = {
            "type": "user_message",
            "content": "Hello, I need help with Python",
            "metadata": {}
        }

        # Process through chat manager (this would normally call the agent)
        chat_message = ChatMessage.from_dict({
            **user_message,
            "message_id": "test_msg_1",
            "user_id": "test_user",
            "timestamp": datetime.utcnow().isoformat()
        })

        # Get agent response
        agent_response = await architect_agent.handle_chat_message(
            "test_user",
            "Hello, I need help with Python"
        )

        # Verify response
        assert agent_response["response"] == mock_llm_response.content
        assert agent_response["agent_type"] == "architect"

        # Verify connection is maintained
        assert connection_id in chat_manager.connections


# Pytest configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])