"""
Distributed Lock Manager using Redis.

Ensures only one agent operates on a given project at a time by using
an atomic Redis SET with NX (only if not exists) and EX (expiry).

Usage pattern (async):
    redis_client = await get_redis_client()
    lock = LockManager(redis_client)
    acquired = await lock.acquire_lock(project_id=1, agent_role="backend", task_id=42)
    if acquired:
        try:
            # Do work...
            pass
        finally:
            await lock.release_lock(project_id=1)
"""
from __future__ import annotations

import json
import logging
from datetime import datetime, timezone
from typing import Optional

from redis.asyncio import Redis

# Prefer using the shared Redis client provided by redis_service
try:
    # FastAPI dependency-style import (optional for DI in routes/services)
    from .redis_service import get_redis_client  # type: ignore
except Exception:  # pragma: no cover - fallback for non-FastAPI contexts
    get_redis_client = None  # type: ignore

logger = logging.getLogger(__name__)


class LockManager:
    """Manage per-project distributed locks backed by Redis.

    Attributes:
        redis: Async Redis client.
        key_prefix: Prefix for lock keys.
    """

    def __init__(self, redis_client: Redis, key_prefix: str = "lock:project:") -> None:
        """Initialize the lock manager.

        Args:
            redis_client: Async Redis client instance.
            key_prefix: Prefix used for project lock keys.
        """
        self.redis: Redis = redis_client
        self.key_prefix: str = key_prefix

    def _key(self, project_id: int) -> str:
        """Build the Redis key for a project lock."""
        return f"{self.key_prefix}{project_id}"

    async def acquire_lock(
        self,
        project_id: int,
        agent_role: str,
        task_id: int,
        timeout_seconds: int = 600,
    ) -> bool:
        """Attempt to acquire the project lock.

        Uses SET with NX and EX to atomically acquire a lock with TTL.

        Args:
            project_id: Target project identifier.
            agent_role: Agent role trying to acquire the lock (e.g., 'backend').
            task_id: Task identifier associated with this lock acquisition.
            timeout_seconds: Expiration in seconds to avoid stale locks (default: 10 minutes).

        Returns:
            True if the lock was acquired, False if already held by another actor.
        """
        key = self._key(project_id)
        value = json.dumps(
            {
                "agent_role": agent_role,
                "task_id": task_id,
                "acquired_at": datetime.now(timezone.utc).isoformat(),
            }
        )

        try:
            # redis-py (async) returns True if the key was set, otherwise False/None
            result: Optional[bool] = await self.redis.set(
                name=key, value=value, ex=timeout_seconds, nx=True
            )
            acquired = bool(result)
            if acquired:
                logger.debug(f"Lock acquired for {key} by role={agent_role} task_id={task_id}")
            else:
                logger.debug(f"Lock already held for {key}; acquisition failed")
            return acquired
        except Exception as e:
            logger.error(f"Error acquiring lock for {key}: {e}")
            return False

    async def release_lock(self, project_id: int) -> None:
        """Release the project lock by deleting the Redis key.

        Args:
            project_id: Target project identifier.
        """
        key = self._key(project_id)
        try:
            await self.redis.delete(key)
            logger.debug(f"Lock released for {key}")
        except Exception as e:
            logger.error(f"Error releasing lock for {key}: {e}")