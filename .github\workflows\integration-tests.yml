name: Integration Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run integration tests daily at 2 AM UTC
    - cron: "0 2 * * *"

env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1
  REGISTRY: ghcr.io

jobs:
  # Backend tests
  backend-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 15

    services:
      postgres:
        image: pgvector/pgvector:pg15
        env:
          POSTGRES_DB: ai_coding_agent_test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres_test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
          cache: "pip"

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r containers/ai-orchestrator/requirements.txt
          pip install -r requirements-dev.txt

      - name: Run backend unit tests
        working-directory: containers/ai-orchestrator
        env:
          DATABASE_URL: postgresql://postgres:postgres_test_password@localhost:5432/ai_coding_agent_test
          REDIS_URL: redis://localhost:6379/0
          PYTHONPATH: ${{ github.workspace }}/containers/ai-orchestrator/src
        run: |
          python -m pytest tests/ -v --cov=src --cov-report=xml --cov-report=html

      - name: Upload backend test coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./containers/ai-orchestrator/coverage.xml
          flags: backend
          name: backend-coverage

      - name: Archive backend test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: backend-test-results
          path: containers/ai-orchestrator/htmlcov/

  # Frontend tests
  frontend-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: containers/admin-dashboard/package-lock.json

      - name: Install frontend dependencies
        working-directory: containers/admin-dashboard
        run: npm ci

      - name: Run frontend linting
        working-directory: containers/admin-dashboard
        run: npm run lint

      - name: Run frontend type checking
        working-directory: containers/admin-dashboard
        run: npm run type-check

      - name: Run frontend unit tests
        working-directory: containers/admin-dashboard
        run: npm run test:coverage

      - name: Upload frontend test coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./containers/admin-dashboard/coverage/coverage-final.json
          flags: frontend
          name: frontend-coverage

      - name: Build frontend for production
        working-directory: containers/admin-dashboard
        env:
          NEXT_PUBLIC_API_BASE_URL: http://localhost:8000
        run: npm run build

      - name: Archive frontend test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: frontend-test-results
          path: containers/admin-dashboard/coverage/

  # Integration tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    timeout-minutes: 30

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
          cache: "pip"

      - name: Install test dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt

      - name: Create .env file for testing
        run: |
          cp .env.example .env
          echo "POSTGRES_PASSWORD=postgres_test_password" >> .env
          echo "JWT_SECRET=test-jwt-secret-key-for-ci" >> .env
          echo "NEXT_PUBLIC_API_BASE_URL=http://localhost:8000" >> .env

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker images
        run: |
          docker-compose -f docker-compose.yml build --parallel

      - name: Start services
        run: |
          docker-compose -f docker-compose.yml up -d

          # Wait for services to be healthy
          timeout 120s bash -c 'until docker-compose ps | grep -q "healthy\|Up"; do sleep 5; done'

      - name: Wait for services to be ready
        run: |
          # Wait for API to be responsive
          timeout 60s bash -c 'until curl -f http://localhost:8000/health; do sleep 5; done'

          # Wait for frontend to be responsive
          timeout 60s bash -c 'until curl -f http://localhost:3000/api/health; do sleep 5; done'

      - name: Run integration tests
        env:
          TEST_API_BASE_URL: http://localhost:8000
          TEST_FRONTEND_BASE_URL: http://localhost:3000
          TEST_TIMEOUT: 60
        run: |
          python -m pytest tests/integration/ -v \
            --tb=short \
            --html=test-results/integration-report.html \
            --self-contained-html \
            --junitxml=test-results/integration-results.xml

      - name: Archive integration test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: |
            test-results/
            !test-results/**/*.pyc

      - name: Show service logs on failure
        if: failure()
        run: |
          echo "=== AI Orchestrator Logs ==="
          docker-compose logs ai-orchestrator
          echo "=== Admin Dashboard Logs ==="
          docker-compose logs admin-dashboard
          echo "=== PostgreSQL Logs ==="
          docker-compose logs postgresql
          echo "=== Redis Logs ==="
          docker-compose logs redis

      - name: Cleanup
        if: always()
        run: |
          docker-compose down -v --remove-orphans
          docker system prune -af

  # Security scanning
  security-scan:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: "fs"
          scan-ref: "."
          format: "sarif"
          output: "trivy-results.sarif"

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: "trivy-results.sarif"

      - name: Run Python security audit
        run: |
          python -m pip install --upgrade pip safety bandit

          # Check for known vulnerabilities in Python dependencies
          safety check --json --output safety-report.json || true

          # Run bandit security linter on Python code
          bandit -r containers/ai-orchestrator/src -f json -o bandit-report.json || true

      - name: Upload security scan results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-scan-results
          path: |
            trivy-results.sarif
            safety-report.json
            bandit-report.json

  # Performance tests (only on main branch)
  performance-tests:
    runs-on: ubuntu-latest
    needs: [integration-tests]
    if: github.ref == 'refs/heads/main'
    timeout-minutes: 20

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt
          pip install locust

      - name: Create .env file
        run: |
          cp .env.example .env
          echo "POSTGRES_PASSWORD=postgres_perf_password" >> .env
          echo "JWT_SECRET=perf-test-jwt-secret" >> .env

      - name: Start services for performance testing
        run: |
          docker-compose -f docker-compose.yml up -d
          timeout 120s bash -c 'until curl -f http://localhost:8000/health; do sleep 5; done'

      - name: Run performance tests
        run: |
          python -m pytest tests/integration/ -v -m "performance" \
            --tb=short \
            --html=test-results/performance-report.html \
            --self-contained-html

      - name: Archive performance test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-test-results
          path: test-results/performance-report.html

      - name: Cleanup performance tests
        if: always()
        run: |
          docker-compose down -v --remove-orphans

  # Build and push Docker images (only on main branch)
  build-and-push:
    runs-on: ubuntu-latest
    needs: [integration-tests, security-scan]
    if: github.ref == 'refs/heads/main'
    timeout-minutes: 20

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Extract metadata for AI Orchestrator
        id: meta-api
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/ai-orchestrator
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix=main-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Extract metadata for Admin Dashboard
        id: meta-frontend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/admin-dashboard
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix=main-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push AI Orchestrator image
        uses: docker/build-push-action@v5
        with:
          context: ./containers/ai-orchestrator
          push: true
          tags: ${{ steps.meta-api.outputs.tags }}
          labels: ${{ steps.meta-api.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Admin Dashboard image
        uses: docker/build-push-action@v5
        with:
          context: ./containers/admin-dashboard
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Generate deployment summary
        run: |
          echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**AI Orchestrator Image:** \`${{ env.REGISTRY }}/${{ github.repository }}/ai-orchestrator:latest\`" >> $GITHUB_STEP_SUMMARY
          echo "**Admin Dashboard Image:** \`${{ env.REGISTRY }}/${{ github.repository }}/admin-dashboard:latest\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "Images have been built and pushed to GitHub Container Registry." >> $GITHUB_STEP_SUMMARY

  # Deployment notification
  notify-deployment:
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.ref == 'refs/heads/main' && success()

    steps:
      - name: Send deployment notification
        run: |
          echo "🎉 Deployment successful!"
          echo "All integration tests passed and Docker images have been built and pushed."
          echo "The AI Coding Agent system is ready for production deployment."
