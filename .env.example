# AI Coding Agent Environment Configuration Example
# Copy this file to .env and fill in the actual values
# Never commit .env to version control

# =============================================================================
# CORE DATABASE & CACHING
# =============================================================================
# PostgreSQL Configuration
# These are handled via Docker Secrets - no need to set here
# POSTGRES_PASSWORD=<set-via-secrets/postgres_password.txt>
POSTGRES_DB=ai_coding_agent
DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/ai_coding_agent

# Redis Configuration (no secrets needed for default setup)
REDIS_URL=redis://redis:6379/0

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# Supabase Configuration
# These are handled via Docker Secrets - no need to set here
# SUPABASE_URL=<set-via-secrets/supabase_url.txt>
# SUPABASE_KEY=<set-via-secrets/supabase_key.txt>
# SUPABASE_SERVICE_KEY=<set-via-secrets/supabase_service_key.txt>

# JWT Configuration
# This is handled via Docker Secrets - no need to set here
# JWT_SECRET=<set-via-secrets/jwt_secret.txt>

# =============================================================================
# AI/LLM SERVICE API KEYS
# =============================================================================
# OpenRouter API Key (for cloud LLM access)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# OpenAI API Key (optional - for direct OpenAI access)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (optional - for direct Claude access)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# LLM PROVIDER CONFIGURATION
# =============================================================================
# Local LLM Configuration
DEFAULT_LOCAL_PROVIDER=ollama
OLLAMA_BASE_URL=http://host.docker.internal:11434

# Cloud LLM Configuration
DEFAULT_CLOUD_PROVIDER=openrouter
ENABLE_CLOUD_FALLBACK=true

# =============================================================================
# DEVELOPMENT ENVIRONMENT
# =============================================================================
# Code Server Configuration
# This is handled via Docker Secrets - no need to set here
# CODE_SERVER_PASSWORD=<set-via-secrets/code_server_password.txt>
CODE_SERVER_PORT=8080

# Development Ports (only used in dev mode)
API_PORT=8000
DASHBOARD_PORT=3000

# =============================================================================
# DOCKER RESOURCE MANAGEMENT
# =============================================================================
# Container Resource Limits
DOCKER_RESOURCE_LIMITS=true
MAX_USER_CONTAINERS=10
CONTAINER_CPU_LIMIT=1.0
CONTAINER_MEMORY_LIMIT=2G

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================
# Production Environment Settings (for docker-compose.prod.yml)
ENVIRONMENT=production
WORKERS=4
MAX_CONNECTIONS=100
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# External URLs (replace with your actual domain)
NEXT_PUBLIC_API_BASE_URL=https://api.your-domain.com
NEXTAUTH_URL=https://admin.your-domain.com
ALLOWED_HOSTS=localhost,127.0.0.1,ai-orchestrator
CORS_ORIGINS=http://localhost:3000,https://admin.your-domain.com

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Monitoring Ports
PROMETHEUS_PORT=9091
GRAFANA_PORT=3001
METRICS_PORT=9090

# Monitoring Credentials
GRAFANA_ADMIN_USER=admin
# GRAFANA_ADMIN_PASSWORD=<set-via-environment-specific-config>
# GRAFANA_SECRET_KEY=<set-via-environment-specific-config>

# =============================================================================
# VOLUME PATHS (for production bind mounts)
# =============================================================================
POSTGRES_DATA_PATH=./volumes/postgres-prod-data
REDIS_DATA_PATH=./volumes/redis-prod-data

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. All sensitive values (passwords, secrets, API keys) should be:
#    - Set in ./secrets/ directory files for Docker Secrets
#    - Or set via secure environment variable injection in production
#
# 2. Never commit actual secret values to version control
#
# 3. Use strong, randomly generated passwords and secrets:
#    - PostgreSQL password: minimum 12 characters
#    - JWT secret: minimum 32 characters, cryptographically random
#    - Code server password: minimum 8 characters
#
# 4. For production deployment:
#    - Use external secret management (AWS Secrets Manager, HashiCorp Vault, etc.)
#    - Implement secret rotation policies
#    - Enable audit logging for secret access