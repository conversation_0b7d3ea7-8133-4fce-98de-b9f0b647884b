#!/bin/bash

# Production Deployment Script for AI Coding Agent
# Deploys the complete system to production environment

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_ROOT/backups}"
DEPLOY_ENV="${DEPLOY_ENV:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default configuration
DRY_RUN=""
SKIP_BACKUP=""
SKIP_TESTS=""
FORCE_DEPLOY=""
SSL_CERT_EMAIL=""
DOMAIN_API=""
DOMAIN_ADMIN=""

# Function definitions
print_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Deploy AI Coding Agent to production environment"
    echo
    echo "OPTIONS:"
    echo "  --dry-run              Show what would be deployed without making changes"
    echo "  --skip-backup          Skip database backup before deployment"
    echo "  --skip-tests           Skip running tests before deployment"
    echo "  --force                Force deployment even if tests fail"
    echo "  --ssl-email EMAIL      Email for Let's Encrypt SSL certificates"
    echo "  --api-domain DOMAIN    Domain for API server (e.g., api.example.com)"
    echo "  --admin-domain DOMAIN  Domain for admin dashboard (e.g., admin.example.com)"
    echo "  --help                 Show this help message"
    echo
    echo "EXAMPLES:"
    echo "  $0 --ssl-email <EMAIL> --api-domain api.example.com --admin-domain admin.example.com"
    echo "  $0 --dry-run           # Preview deployment without making changes"
    echo "  $0 --skip-tests        # Deploy without running tests (not recommended)"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking deployment prerequisites..."

    local missing_deps=()

    # Check for required tools
    for cmd in docker docker-compose git curl openssl; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done

    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        log_error "Please install the missing dependencies and try again"
        exit 1
    fi

    # Check Docker daemon
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running or accessible"
        exit 1
    fi

    # Check for .env file
    if [ ! -f "$PROJECT_ROOT/.env" ]; then
        log_error ".env file not found. Please create it from .env.example and configure it properly."
        exit 1
    fi

    # Check for required environment variables
    source "$PROJECT_ROOT/.env"
    local required_vars=(
        "POSTGRES_PASSWORD"
        "JWT_SECRET"
        "GRAFANA_ADMIN_PASSWORD"
        "GRAFANA_SECRET_KEY"
    )

    for var in "${required_vars[@]}"; do
        if [ -z "${!var:-}" ]; then
            log_error "Required environment variable $var is not set in .env file"
            exit 1
        fi
    done

    log_success "All prerequisites are satisfied"
}

run_pre_deployment_tests() {
    if [ -n "$SKIP_TESTS" ]; then
        log_warning "Skipping pre-deployment tests (not recommended for production)"
        return 0
    fi

    log_info "Running pre-deployment tests..."

    cd "$PROJECT_ROOT"

    # Run integration tests in CI mode
    if [ -f "scripts/run-integration-tests.sh" ]; then
        bash scripts/run-integration-tests.sh --environment production
        local test_exit_code=$?

        if [ $test_exit_code -ne 0 ] && [ -z "$FORCE_DEPLOY" ]; then
            log_error "Pre-deployment tests failed. Use --force to deploy anyway."
            exit 1
        elif [ $test_exit_code -ne 0 ]; then
            log_warning "Tests failed but continuing due to --force flag"
        fi
    else
        log_warning "Integration test script not found, skipping tests"
    fi
}

backup_database() {
    if [ -n "$SKIP_BACKUP" ]; then
        log_warning "Skipping database backup"
        return 0
    fi

    log_info "Creating database backup..."

    mkdir -p "$BACKUP_DIR"

    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="$BACKUP_DIR/postgres_backup_${timestamp}.sql"

    # Check if database is running
    if docker-compose -f docker-compose.yml ps postgresql | grep -q "Up"; then
        # Create backup using docker-compose exec
        docker-compose -f docker-compose.yml exec -T postgresql pg_dump \
            -U postgres \
            -d ai_coding_agent \
            --clean \
            --if-exists \
            --verbose > "$backup_file"

        # Compress backup
        gzip "$backup_file"

        log_success "Database backup created: ${backup_file}.gz"
    else
        log_warning "Database is not running, skipping backup"
    fi
}

setup_ssl_certificates() {
    if [ -z "$SSL_CERT_EMAIL" ] || [ -z "$DOMAIN_API" ] || [ -z "$DOMAIN_ADMIN" ]; then
        log_warning "SSL configuration not provided, using self-signed certificates"
        setup_self_signed_certs
        return 0
    fi

    log_info "Setting up SSL certificates with Let's Encrypt..."

    # Install certbot if not present
    if ! command -v certbot &> /dev/null; then
        log_info "Installing certbot..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y certbot
        elif command -v yum &> /dev/null; then
            sudo yum install -y certbot
        else
            log_error "Please install certbot manually and run the script again"
            exit 1
        fi
    fi

    # Create SSL certificate directory
    local ssl_dir="$PROJECT_ROOT/containers/nginx/ssl"
    mkdir -p "$ssl_dir"

    # Obtain certificates for both domains
    for domain in "$DOMAIN_API" "$DOMAIN_ADMIN"; do
        log_info "Obtaining SSL certificate for $domain..."

        certbot certonly \
            --standalone \
            --agree-tos \
            --no-eff-email \
            --email "$SSL_CERT_EMAIL" \
            --domains "$domain" \
            --keep-until-expiring \
            --quiet

        # Copy certificates to nginx directory
        cp "/etc/letsencrypt/live/$domain/fullchain.pem" "$ssl_dir/${domain}_cert.pem"
        cp "/etc/letsencrypt/live/$domain/privkey.pem" "$ssl_dir/${domain}_key.pem"
    done

    log_success "SSL certificates configured successfully"
}

setup_self_signed_certs() {
    log_info "Generating self-signed SSL certificates for development..."

    local ssl_dir="$PROJECT_ROOT/containers/nginx/ssl"
    mkdir -p "$ssl_dir"

    # Generate self-signed certificate
    openssl req -x509 -nodes -days 365 \
        -newkey rsa:2048 \
        -keyout "$ssl_dir/key.pem" \
        -out "$ssl_dir/cert.pem" \
        -subj "/C=US/ST=Development/L=Local/O=AI-Coding-Agent/CN=localhost"

    # Create copies for API and admin domains
    cp "$ssl_dir/cert.pem" "$ssl_dir/api_cert.pem"
    cp "$ssl_dir/key.pem" "$ssl_dir/api_key.pem"
    cp "$ssl_dir/cert.pem" "$ssl_dir/admin_cert.pem"
    cp "$ssl_dir/key.pem" "$ssl_dir/admin_key.pem"

    log_success "Self-signed certificates generated"
}

update_configuration() {
    log_info "Updating configuration for production deployment..."

    cd "$PROJECT_ROOT"

    # Update .env with production-specific values
    local env_backup=".env.backup.$(date +%Y%m%d_%H%M%S)"
    cp .env "$env_backup"

    # Set production environment
    if ! grep -q "ENVIRONMENT=production" .env; then
        echo "ENVIRONMENT=production" >> .env
    fi

    # Update domain configurations if provided
    if [ -n "$DOMAIN_API" ]; then
        if grep -q "NEXT_PUBLIC_API_BASE_URL=" .env; then
            sed -i "s|NEXT_PUBLIC_API_BASE_URL=.*|NEXT_PUBLIC_API_BASE_URL=https://$DOMAIN_API|" .env
        else
            echo "NEXT_PUBLIC_API_BASE_URL=https://$DOMAIN_API" >> .env
        fi
    fi

    if [ -n "$DOMAIN_ADMIN" ]; then
        if grep -q "NEXTAUTH_URL=" .env; then
            sed -i "s|NEXTAUTH_URL=.*|NEXTAUTH_URL=https://$DOMAIN_ADMIN|" .env
        else
            echo "NEXTAUTH_URL=https://$DOMAIN_ADMIN" >> .env
        fi
    fi

    log_success "Configuration updated (backup saved as $env_backup)"
}

deploy_services() {
    log_info "Deploying services to production..."

    cd "$PROJECT_ROOT"

    if [ -n "$DRY_RUN" ]; then
        log_info "[DRY RUN] Would deploy services with:"
        echo "  docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build"
        return 0
    fi

    # Pull latest images
    log_info "Pulling latest base images..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml pull || true

    # Build and deploy
    log_info "Building and starting services..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    local max_wait=300  # 5 minutes
    local wait_time=0

    while [ $wait_time -lt $max_wait ]; do
        local healthy_services=$(docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps | grep -c "healthy\|Up" || echo "0")
        local total_services=$(docker-compose -f docker-compose.yml -f docker-compose.prod.yml config --services | wc -l)

        if [ "$healthy_services" -ge "$((total_services * 80 / 100))" ]; then  # 80% of services healthy
            log_success "Services are ready"
            break
        fi

        log_info "Waiting for services to be ready... ($wait_time/${max_wait}s)"
        sleep 10
        wait_time=$((wait_time + 10))
    done

    if [ $wait_time -ge $max_wait ]; then
        log_error "Services failed to become healthy within $max_wait seconds"
        show_service_status
        exit 1
    fi
}

verify_deployment() {
    log_info "Verifying deployment..."

    local api_url="http://localhost:8000"
    local dashboard_url="http://localhost:3000"

    # If custom domains are configured, use HTTPS
    if [ -n "$DOMAIN_API" ]; then
        api_url="https://$DOMAIN_API"
    fi

    if [ -n "$DOMAIN_ADMIN" ]; then
        dashboard_url="https://$DOMAIN_ADMIN"
    fi

    # Test API health
    log_info "Testing API health endpoint..."
    if curl -f -s "$api_url/health" > /dev/null; then
        log_success "API is responding"
    else
        log_error "API health check failed"
        return 1
    fi

    # Test dashboard health
    log_info "Testing admin dashboard health endpoint..."
    if curl -f -s "$dashboard_url/api/health" > /dev/null; then
        log_success "Admin dashboard is responding"
    else
        log_error "Admin dashboard health check failed"
        return 1
    fi

    # Test database connectivity through API
    log_info "Testing database connectivity..."
    if curl -f -s "$api_url/api/roles" > /dev/null; then
        log_success "Database connectivity verified"
    else
        log_warning "Database connectivity test failed (might be authentication required)"
    fi

    log_success "Deployment verification completed"
}

show_service_status() {
    log_info "Current service status:"
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps

    log_info "Recent logs:"
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs --tail=10
}

show_deployment_summary() {
    log_success "🎉 Production deployment completed successfully!"
    echo
    echo "=== Deployment Summary ==="
    echo "Environment: $DEPLOY_ENV"
    echo "Timestamp: $(date)"
    echo
    echo "=== Service URLs ==="
    if [ -n "$DOMAIN_API" ]; then
        echo "API Server: https://$DOMAIN_API"
    else
        echo "API Server: http://localhost:8000"
    fi

    if [ -n "$DOMAIN_ADMIN" ]; then
        echo "Admin Dashboard: https://$DOMAIN_ADMIN"
    else
        echo "Admin Dashboard: http://localhost:3000"
    fi

    echo "Grafana Dashboard: http://localhost:3001"
    echo "Prometheus Metrics: http://localhost:9091"
    echo
    echo "=== Next Steps ==="
    echo "1. Configure monitoring alerts in Grafana"
    echo "2. Set up SSL certificate auto-renewal (if using Let's Encrypt)"
    echo "3. Configure backup automation"
    echo "4. Review and configure firewall rules"
    echo "5. Set up log rotation"
    echo
    echo "=== Useful Commands ==="
    echo "View logs: docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f"
    echo "Scale services: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --scale ai-orchestrator=3"
    echo "Update services: docker-compose -f docker-compose.yml -f docker-compose.prod.yml pull && docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d"
    echo
}

main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                DRY_RUN="true"
                shift
                ;;
            --skip-backup)
                SKIP_BACKUP="true"
                shift
                ;;
            --skip-tests)
                SKIP_TESTS="true"
                shift
                ;;
            --force)
                FORCE_DEPLOY="true"
                shift
                ;;
            --ssl-email)
                SSL_CERT_EMAIL="$2"
                shift 2
                ;;
            --api-domain)
                DOMAIN_API="$2"
                shift 2
                ;;
            --admin-domain)
                DOMAIN_ADMIN="$2"
                shift 2
                ;;
            --help)
                print_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                print_usage
                exit 1
                ;;
        esac
    done

    if [ -n "$DRY_RUN" ]; then
        log_info "🔍 DRY RUN MODE - No changes will be made"
    fi

    log_info "🚀 Starting production deployment for AI Coding Agent"
    log_info "Environment: $DEPLOY_ENV"

    # Deployment sequence
    check_prerequisites
    run_pre_deployment_tests
    backup_database
    setup_ssl_certificates
    update_configuration
    deploy_services

    if [ -z "$DRY_RUN" ]; then
        verify_deployment
        show_deployment_summary
    else
        log_info "🔍 DRY RUN completed - no changes were made"
    fi

    log_success "✅ Deployment script completed successfully!"
}

# Trap for cleanup on exit
trap 'log_info "Deployment script interrupted"' INT TERM

# Run main function
main "$@"