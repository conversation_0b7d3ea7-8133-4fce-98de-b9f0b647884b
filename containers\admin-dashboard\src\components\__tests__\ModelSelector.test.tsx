/**
 * ModelSelector Component Tests
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ModelSelector from '../ModelSelector';
import { LLMProvider } from '@/types/role';
import type { ModelOption } from '@/types/role';

// Mock react-select
jest.mock('react-select', () => {
  return ({ value, onChange, options, placeholder, isDisabled, noOptionsMessage }: any) => {
    return (
      <div data-testid="model-select">
        <select
          value={value?.value || ''}
          onChange={(e) => {
            const selectedOption = options.find((opt: any) => opt.value === e.target.value);
            onChange(selectedOption);
          }}
          disabled={isDisabled}
        >
          <option value="" disabled>
            {placeholder}
          </option>
          {options.length === 0 ? (
            <option disabled>{noOptionsMessage()}</option>
          ) : (
            options.map((option: any) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))
          )}
        </select>
      </div>
    );
  };
});

const mockModels: ModelOption[] = [
  {
    value: 'gpt-4',
    label: 'GPT-4',
    description: 'Most capable OpenAI model',
    cost_per_token: 0.00003,
    context_length: 8192,
  },
  {
    value: 'gpt-3.5-turbo',
    label: 'GPT-3.5 Turbo',
    description: 'Fast and cost-effective',
    cost_per_token: 0.0000005,
    context_length: 16385,
  },
];

const defaultProps = {
  value: 'gpt-4',
  onChange: jest.fn(),
  models: mockModels,
  provider: LLMProvider.OPENAI,
};

describe('ModelSelector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with proper label', () => {
    render(<ModelSelector {...defaultProps} />);

    expect(screen.getByLabelText(/Model Selection/)).toBeInTheDocument();
    expect(screen.getByText('Model Selection')).toBeInTheDocument();
    expect(screen.getByText('*')).toBeInTheDocument(); // Required indicator
  });

  it('displays the selected model', () => {
    render(<ModelSelector {...defaultProps} />);

    const select = screen.getByTestId('model-select').querySelector('select');
    expect(select).toHaveValue('gpt-4');
  });

  it('calls onChange when selection changes', async () => {
    const user = userEvent.setup();
    const mockOnChange = jest.fn();

    render(<ModelSelector {...defaultProps} onChange={mockOnChange} />);

    const select = screen.getByTestId('model-select').querySelector('select')!;
    await user.selectOptions(select, 'gpt-3.5-turbo');

    expect(mockOnChange).toHaveBeenCalledWith('gpt-3.5-turbo');
  });

  it('displays error message when provided', () => {
    const errorMessage = 'Model is required';
    render(<ModelSelector {...defaultProps} error={errorMessage} />);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('is disabled when disabled prop is true', () => {
    render(<ModelSelector {...defaultProps} disabled />);

    const select = screen.getByTestId('model-select').querySelector('select');
    expect(select).toBeDisabled();
  });

  it('shows model details when a model is selected', () => {
    render(<ModelSelector {...defaultProps} value="gpt-4" />);

    expect(screen.getByText('Model Details:')).toBeInTheDocument();
    expect(screen.getByText('GPT-4')).toBeInTheDocument();
    expect(screen.getByText('Most capable OpenAI model')).toBeInTheDocument();
    expect(screen.getByText(/8,192 tokens/)).toBeInTheDocument();
    expect(screen.getByText(/\$0\.000030 per token/)).toBeInTheDocument();
  });

  it('shows empty state when no models available', () => {
    render(
      <ModelSelector
        {...defaultProps}
        models={[]}
        value=""
      />
    );

    expect(screen.getByText('No models available for openai')).toBeInTheDocument();
    expect(screen.getByText('Select a provider to see available models')).toBeInTheDocument();
  });

  it('uses fallback models when models prop is empty', () => {
    render(
      <ModelSelector
        {...defaultProps}
        models={[]}
      />
    );

    // Should show fallback content for empty models
    const select = screen.getByTestId('model-select').querySelector('select');
    expect(select).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    const errorMessage = 'Test error';
    render(<ModelSelector {...defaultProps} error={errorMessage} />);

    const select = screen.getByTestId('model-select');
    expect(select).toHaveAttribute('aria-describedby', 'model-error');
  });

  it('shows no options message when provider has no models', () => {
    render(
      <ModelSelector
        {...defaultProps}
        models={[]}
        provider={LLMProvider.OPENAI}
      />
    );

    expect(screen.getByText(/No models available for openai/)).toBeInTheDocument();
  });
});