# Project: AI Coding Agent
# Purpose: Monitoring module exports with comprehensive optional dependency management

"""
Monitoring Package for AI Orchestrator

This package contains monitoring and metrics collection components for the AI Orchestrator,
including LLM performance metrics, resource monitoring, and Prometheus integration.
"""

import logging

logger = logging.getLogger(__name__)

# Monitoring package availability flag
MONITORING_AVAILABLE = False

# Optional dependency imports with graceful degradation
try:
    from .llm_metrics import (
        LLMResourceMonitor,
        ResourceMetrics,
        LLMPerformanceMetrics,
        monitor,
        start_monitoring,
        stop_monitoring,
        update_llm_metrics,
        get_metrics_summary,
        PROMETHEUS_AVAILABLE
    )
    MONITORING_AVAILABLE = True
    logger.info("LLM metrics monitoring loaded successfully")
except ImportError as e:
    logger.warning(f"LLM metrics monitoring not available: {e}")

    # Mock implementations for graceful degradation
    class MockLLMResourceMonitor:
        def __init__(self, *args, **kwargs): pass
        async def start_monitoring(self): pass
        def stop_monitoring(self): pass
        def update_llm_performance(self, *args, **kwargs): pass
        def get_metrics_summary(self, *args, **kwargs): return {"error": "Monitoring not available"}

    class MockResourceMetrics:
        def __init__(self, *args, **kwargs): pass

    class MockLLMPerformanceMetrics:
        def __init__(self, *args, **kwargs): pass

    # Assign mock implementations
    LLMResourceMonitor = MockLLMResourceMonitor
    ResourceMetrics = MockResourceMetrics
    LLMPerformanceMetrics = MockLLMPerformanceMetrics
    monitor = MockLLMResourceMonitor()
    PROMETHEUS_AVAILABLE = False

    async def start_monitoring(*args, **kwargs):
        logger.warning("Monitoring not available - start_monitoring is a no-op")

    def stop_monitoring():
        logger.warning("Monitoring not available - stop_monitoring is a no-op")

    def update_llm_metrics(*args, **kwargs):
        logger.debug("Monitoring not available - update_llm_metrics is a no-op")

    def get_metrics_summary(*args, **kwargs):
        return {"error": "Monitoring not available", "available": False}

def get_monitoring_status():
    """Get the availability status of monitoring components."""
    return {
        "monitoring_available": MONITORING_AVAILABLE,
        "prometheus_available": PROMETHEUS_AVAILABLE if MONITORING_AVAILABLE else False,
        "llm_metrics_available": MONITORING_AVAILABLE
    }

# Define comprehensive exports for clean imports and proper IDE support
__all__ = [
    # Core monitoring classes
    "LLMResourceMonitor",
    "ResourceMetrics",
    "LLMPerformanceMetrics",

    # Global monitor instance
    "monitor",

    # Control functions
    "start_monitoring",
    "stop_monitoring",
    "update_llm_metrics",
    "get_metrics_summary",

    # Status functions
    "get_monitoring_status",

    # Availability flags
    "MONITORING_AVAILABLE",
    "PROMETHEUS_AVAILABLE",
]