# Docker Compose Watch Development Workflow

This document explains the enhanced Docker Compose configuration with watch functionality for local development.

## Configuration Overview

The Docker Compose setup now uses modern Docker Compose v2+ syntax with `develop` sections for watch functionality, replacing the deprecated `version` attribute and old `watch` syntax.

## File Structure

- `docker-compose.yml` - Base configuration (works for both dev and prod)
- `docker-compose.dev.yml` - Development overrides with live reload
- `docker-compose.prod.yml` - Production overrides for deployment

## Key Improvements

### 1. Removed Deprecated Version Attribute
- No more `version: '3.8'` warnings
- Uses modern Docker Compose v2+ syntax
- Cleaner configuration without obsolete attributes

### 2. Docker Compose Watch Implementation

#### AI Orchestrator (FastAPI Backend)
- **Watch Paths**: `./containers/ai-orchestrator/` and subdirectories
- **Sync Target**: `/app` in container
- **Exclusions**: `.git/`, `__pycache__/`, `*.pyc`, `.pytest_cache/`, etc.
- **Live Reload**: Uses `uvicorn --reload` for automatic restart on code changes
- **Debug Support**: Debug port 5678 exposed for remote debugging

#### Admin Dashboard (Next.js Frontend)
- **Watch Paths**: `./containers/admin-dashboard/` and `src/` directory
- **Sync Target**: `/app` in container
- **Exclusions**: `node_modules/`, `.next/`, `.nyc_output/`, `coverage/`
- **Hot Reload**: Next.js dev server with built-in hot module replacement
- **Development Mode**: `npm run dev` command for active development

#### Code Server
- **Watch Paths**: `./workspace/` directory
- **Sync Target**: `/home/<USER>/workspace` in container
- **Exclusions**: Common build artifacts and cache directories
- **File Sync**: Real-time synchronization of workspace files

### 3. Development vs Production Configuration

#### Development Mode
```bash
# Method 1: Using profile (Docker Compose v2.23+)
docker-compose --profile dev up

# Method 2: Using override file
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

#### Production Mode
```bash
# Method 1: Base configuration only (production by default)
docker-compose up

# Method 2: Explicit production override
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up
```

### 4. Volume Optimization

#### Bind Mounts for Development
- `./containers/ai-orchestrator:/app:delegated` - Fast file synchronization
- `./containers/admin-dashboard:/app:delegated` - Efficient source code sharing
- `./workspace:/home/<USER>/workspace` - Real-time workspace sync

#### Named Volumes for Persistence
- `postgres_data` - PostgreSQL data persistence
- `redis_data` - Redis session data
- `code_server_data` - Code Server user data
- `ollama_models` - Ollama model storage

## Usage Examples

### Basic Development Workflow
```bash
# Start development environment with watch
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Make code changes locally - they automatically sync to containers
# FastAPI backend reloads on Python file changes
# Next.js frontend hot-reloads on JS/TS file changes
# Workspace files sync in real-time to code-server
```

### Watch Specific Services
```bash
# Watch only specific services
docker-compose watch ai-orchestrator admin-dashboard
```

### Production Deployment
```bash
# Deploy to production
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## Cross-Platform Compatibility

### Windows Support
- Uses `:delegated` flag for optimized file sharing
- Proper line ending handling
- Volume mount compatibility with Docker Desktop

### Linux/Mac Support
- Native file system performance
- Proper user permissions
- Efficient file watching mechanisms

## Performance Optimizations

### File Exclusion Patterns
- Ignores cache directories (`__pycache__`, `.next`, `node_modules`)
- Excludes temporary files (`*.pyc`, `.pytest_cache`)
- Skips version control directories (`.git`)
- Avoids build artifacts and coverage reports

### Resource Management
- Proper container resource limits
- Efficient volume mounting strategies
- Optimized file synchronization

## Troubleshooting

### Common Issues

1. **Watch not working**: Ensure Docker Compose v2.23+ is installed
2. **File permission errors**: Check user permissions in containers
3. **Slow file sync**: Use `:delegated` flag on macOS/Windows
4. **Port conflicts**: Verify ports 8000, 3000, 8080, 11434 are available

### Debug Commands
```bash
# Check Docker Compose version
docker-compose version

# View running services
docker-compose ps

# Check logs for specific service
docker-compose logs ai-orchestrator

# Stop all services
docker-compose down
```

## Environment Variables

The development workflow uses the same `.env` file as production:
- `CODE_SERVER_PASSWORD` - Code Server authentication
- `OPENROUTER_API_KEY` - LLM provider key
- `SUPABASE_URL` - Database connection
- All other configuration variables from `.env.example`

## Next Steps

1. **Enhanced Monitoring**: Add Prometheus/Grafana for performance metrics
2. **Advanced Debugging**: Configure remote debugging for all services
3. **CI/CD Integration**: Automate deployment workflows
4. **Scaling**: Add load balancing for production deployments

This enhanced Docker Compose configuration provides a robust, efficient development workflow while maintaining production-ready deployment capabilities.
