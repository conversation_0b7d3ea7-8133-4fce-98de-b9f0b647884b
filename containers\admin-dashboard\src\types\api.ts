/**
 * API-related types for the admin dashboard
 */

import { RoleConfiguration, RoleConfigurationUpdate } from './role';

export interface ApiResponse<T> {
  data?: T;
  error?: ApiError;
  status: number;
}

export interface ApiError {
  message: string;
  details?: string;
  field?: string;
  code?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// Request types for API calls
export interface CreateRoleRequest {
  role_name: string;
  configuration: Omit<RoleConfiguration, 'created_at' | 'updated_at'>;
}

export interface UpdateRoleRequest {
  role_name: string;
  updates: RoleConfigurationUpdate;
}

export interface DeleteRoleRequest {
  role_name: string;
}

// HTTP client configuration
export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

// Request interceptor types
export interface RequestInterceptor {
  onRequest?: (config: any) => any;
  onRequestError?: (error: any) => Promise<any>;
}

export interface ResponseInterceptor {
  onResponse?: (response: any) => any;
  onResponseError?: (error: any) => Promise<any>;
}

// Loading states
export enum LoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
}

export interface AsyncState<T> {
  data: T | null;
  loading: LoadingState;
  error: ApiError | null;
}

// WebSocket types for real-time updates
export interface WebSocketMessage<T = any> {
  type: string;
  payload: T;
  timestamp: string;
}

export interface RoleUpdateMessage {
  type: 'role_update';
  payload: {
    role_name: string;
    configuration: RoleConfiguration;
    action: 'created' | 'updated' | 'deleted';
  };
}