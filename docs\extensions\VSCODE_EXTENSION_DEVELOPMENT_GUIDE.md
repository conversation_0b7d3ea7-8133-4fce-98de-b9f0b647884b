# VS Code Extension Development Guide for AI Coding Agent

## Overview

This guide covers VS Code extension development for the AI Coding Agent's **AI Chat Extension** in Phase 5, focusing on webview APIs, messaging patterns, and container auto-installation.

## Table of Contents

1. [Extension Architecture](#extension-architecture)
2. [Project Setup](#project-setup)
3. [Webview Implementation](#webview-implementation)
4. [Communication Patterns](#communication-patterns)
5. [TypeScript Development](#typescript-development)
6. [Container Auto-Installation](#container-auto-installation)
7. [Testing & Deployment](#testing--deployment)

## Extension Architecture

### Core Components Architecture

```
VS Code Extension Host
├── Extension Activation & Commands
├── Webview Panel (Chat UI)
├── Message Handler (Communication)
├── API Service (HTTP/WebSocket)
└── Auto-Installation (Container)
```

### Key Integration Points

- **Webview Panel**: Main chat interface with HTML/CSS/JS
- **Message Passing**: Extension ↔ Webview ↔ AI Orchestrator
- **Real-time Updates**: WebSocket for agent progress
- **Context Awareness**: Access to active files and workspace

## Project Setup

### Essential Files Structure

```
ai-chat-extension/
├── package.json              # Extension manifest
├── src/
│   ├── extension.ts          # Main entry point
│   ├── webview/
│   │   ├── chatPanel.ts      # Webview management
│   │   └── messageHandler.ts # Message routing
│   ├── services/
│   │   ├── apiService.ts     # Backend communication
│   │   └── websocketService.ts # Real-time updates
│   └── types/               # TypeScript definitions
├── media/                   # Assets and webview files
└── scripts/install.js       # Auto-installation
```

### Package.json Configuration

```json
{
  "name": "ai-coding-agent-chat",
  "displayName": "AI Coding Agent Chat",
  "version": "1.0.0",
  "engines": { "vscode": "^1.74.0" },
  "main": "./dist/extension.js",
  "contributes": {
    "commands": [
      {
        "command": "aiCodingAgent.openChat",
        "title": "Open AI Chat",
        "icon": "$(comment-discussion)"
      }
    ],
    "keybindings": [
      {
        "command": "aiCodingAgent.openChat",
        "key": "ctrl+shift+a"
      }
    ],
    "configuration": {
      "properties": {
        "aiCodingAgent.apiEndpoint": {
          "type": "string",
          "default": "http://localhost:8000"
        },
        "aiChat.serverUrl": {
          "type": "string",
          "default": "ws://localhost:8000",
          "description": "WebSocket server URL for AI chat communication"
        },
        "aiChat.maxReconnectAttempts": {
          "type": "number",
          "default": 5,
          "description": "Maximum number of WebSocket reconnection attempts"
        },
        "aiChat.reconnectDelay": {
          "type": "number",
          "default": 1000,
          "description": "Base delay in milliseconds between reconnection attempts"
        },
        "aiChat.heartbeatInterval": {
          "type": "number",
          "default": 30000,
          "description": "Heartbeat interval in milliseconds to keep connection alive"
        }
      }
    }
  }
}
```

## Webview Implementation

### Chat Panel Core Class

```typescript
// src/webview/chatPanel.ts
import * as vscode from 'vscode';

export class ChatPanel {
  public static currentPanel: ChatPanel | undefined;
  private readonly _panel: vscode.WebviewPanel;
  private _messages: ChatMessage[] = [];

  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
    this._panel = panel;
    this._setWebviewMessageListener();
    this._update();
  }

  public static createOrShow(extensionUri: vscode.Uri): ChatPanel {
    if (ChatPanel.currentPanel) {
      ChatPanel.currentPanel._panel.reveal();
      return ChatPanel.currentPanel;
    }

    const panel = vscode.window.createWebviewPanel(
      'aiCodingAgentChat',
      'AI Chat',
      vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [vscode.Uri.joinPath(extensionUri, 'media')]
      }
    );

    ChatPanel.currentPanel = new ChatPanel(panel, extensionUri);
    return ChatPanel.currentPanel;
  }

  private _setWebviewMessageListener(): void {
    this._panel.webview.onDidReceiveMessage(async (message) => {
      switch (message.type) {
        case 'userMessage':
          await this._handleUserMessage(message.content);
          break;
        case 'insertCode':
          await this._insertCodeInEditor(message.code);
          break;
      }
    });
  }

  private async _handleUserMessage(content: string): Promise<void> {
    // Add user message
    this._messages.push({
      id: Date.now().toString(),
      role: 'user',
      content,
      timestamp: Date.now()
    });

    // Send to AI Orchestrator via API
    const apiService = new ApiService();
    const response = await apiService.processMessage(content);

    // Add AI response
    this._messages.push({
      id: Date.now().toString(),
      role: 'assistant',
      content: response.content,
      timestamp: Date.now()
    });

    this._updateWebview();
  }

  private async _insertCodeInEditor(code: string): Promise<void> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    await editor.edit(editBuilder => {
      editBuilder.insert(editor.selection.active, code);
    });
  }

  private _update(): void {
    this._panel.webview.html = this._getHtmlContent();
  }

  private _getHtmlContent(): string {
    return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Chat</title>
  <style>
    body { font-family: var(--vscode-font-family); }
    .chat-container { display: flex; flex-direction: column; height: 100vh; }
    .messages { flex: 1; overflow-y: auto; padding: 10px; }
    .message { margin: 10px 0; padding: 8px; border-radius: 4px; }
    .user { background: var(--vscode-button-background); color: var(--vscode-button-foreground); }
    .assistant { background: var(--vscode-editor-background); }
    .input-area { padding: 10px; border-top: 1px solid var(--vscode-panel-border); }
    #messageInput { width: 100%; padding: 8px; background: var(--vscode-input-background); }
  </style>
</head>
<body>
  <div class="chat-container">
    <div class="messages" id="messages"></div>
    <div class="input-area">
      <input type="text" id="messageInput" placeholder="Ask AI to help with your code..." />
      <button onclick="sendMessage()">Send</button>
    </div>
  </div>

  <script>
    const vscode = acquireVsCodeApi();

    function sendMessage() {
      const input = document.getElementById('messageInput');
      const content = input.value.trim();
      if (!content) return;

      vscode.postMessage({
        type: 'userMessage',
        content: content
      });

      input.value = '';
    }

    function insertCode(code) {
      vscode.postMessage({
        type: 'insertCode',
        code: code
      });
    }

    window.addEventListener('message', event => {
      const message = event.data;
      if (message.type === 'updateMessages') {
        updateMessages(message.messages);
      }
    });
  </script>
</body>
</html>`;
  }
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}
```

## Communication Patterns

### API Service for Backend Communication

```typescript
// src/services/apiService.ts
import axios, { AxiosInstance } from 'axios';
import * as vscode from 'vscode';

export class ApiService {
  private client: AxiosInstance;

  constructor() {
    const config = vscode.workspace.getConfiguration('aiCodingAgent');
    const baseURL = config.get<string>('apiEndpoint', 'http://localhost:8000');

    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  async processMessage(content: string): Promise<any> {
    const context = this.getCurrentContext();

    try {
      const response = await this.client.post('/api/v1/chat/message', {
        message: content,
        context: context
      });
      return response.data;
    } catch (error) {
      throw new Error(`API call failed: ${error}`);
    }
  }

  async generateCode(prompt: string, language: string): Promise<any> {
    try {
      const response = await this.client.post('/api/v1/llm/generate-code', {
        prompt,
        language,
        context: this.getCurrentContext()
      });
      return response.data;
    } catch (error) {
      throw new Error(`Code generation failed: ${error}`);
    }
  }

  private getCurrentContext(): any {
    const editor = vscode.window.activeTextEditor;
    return {
      activeFile: editor?.document.fileName,
      selectedText: editor?.document.getText(editor.selection),
      language: editor?.document.languageId,
      workspacePath: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
    };
  }
}
```

### WebSocket Service for Real-time Updates

```typescript
// src/services/webSocketManager.ts
import * as vscode from 'vscode';

export interface ChatMessage {
    id: string;
    type: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: string;
    agent_type?: string;
    user_id?: string;
}

export interface ServerMessage {
    type: 'connection_established' | 'pong' | 'chat_response' | 'system' | 'error';
    content?: string;
    response?: string;
    agent_type?: string;
    user_id?: string;
    timestamp?: string;
    id?: string;
    error?: string;
}

export interface MessageHandler {
    (message: ChatMessage): void;
}

export interface ConnectionConfig {
    serverUrl: string;
    maxReconnectAttempts: number;
    reconnectDelay: number;
    heartbeatInterval: number;
}

export class WebSocketManager {
    private ws: WebSocket | null = null;
    private config: ConnectionConfig;
    private reconnectAttempts: number = 0;
    private reconnectTimeout: NodeJS.Timeout | null = null;
    private heartbeatTimeout: NodeJS.Timeout | null = null;
    private messageHandlers: MessageHandler[] = [];
    private isConnecting: boolean = false;
    private disposed: boolean = false;

    constructor(config?: Partial<ConnectionConfig>) {
        const vscodeConfig = vscode.workspace.getConfiguration('aiChat');

        this.config = {
            serverUrl: config?.serverUrl || vscodeConfig.get('serverUrl', 'ws://localhost:8000'),
            maxReconnectAttempts: config?.maxReconnectAttempts || vscodeConfig.get('maxReconnectAttempts', 5),
            reconnectDelay: config?.reconnectDelay || vscodeConfig.get('reconnectDelay', 1000),
            heartbeatInterval: config?.heartbeatInterval || vscodeConfig.get('heartbeatInterval', 30000)
        };
    }

    public connect(): void {
        if (this.disposed) {
            console.warn('WebSocketManager is disposed, cannot connect');
            return;
        }

        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
            return;
        }

        this.isConnecting = true;
        const wsUrl = this.buildWebSocketUrl();

        try {
            this.ws = new WebSocket(wsUrl);

            this.ws.onopen = () => {
                console.log('Connected to AI Orchestrator');
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.showStatusMessage('Connected to AI service', false);
                this.startHeartbeat();

                // Send initial connection message
                this.sendMessage({
                    type: 'system',
                    content: 'Connected to VS Code extension',
                    timestamp: new Date().toISOString()
                });
            };

            this.ws.onmessage = (event: MessageEvent) => {
                try {
                    const message = this.parseMessage(event.data);
                    if (message) {
                        this.handleIncomingMessage(message);
                    }
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                    this.showStatusMessage(`Message parsing error: ${error}`, true);
                }
            };

            this.ws.onclose = (event: CloseEvent) => {
                console.log(`WebSocket closed: ${event.code} - ${event.reason}`);
                this.isConnecting = false;
                this.ws = null;
                this.stopHeartbeat();

                if (!this.disposed) {
                    this.showStatusMessage('Disconnected from AI service', true);
                    this.attemptReconnect();
                }
            };

            this.ws.onerror = (event: Event) => {
                console.error('WebSocket error:', event);
                this.isConnecting = false;
                this.showStatusMessage('Connection error occurred', true);
            };

        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.isConnecting = false;
            this.showStatusMessage(`Failed to connect: ${error}`, true);
        }
    }

    public sendChatMessage(content: string): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!content || content.trim().length === 0) {
                reject(new Error('Message content cannot be empty'));
                return;
            }

            if (!this.isConnected()) {
                vscode.window.showWarningMessage('Not connected to AI service. Attempting to reconnect...');
                this.connect();
                reject(new Error('Not connected to AI service'));
                return;
            }

            try {
                const message: ChatMessage = {
                    id: this.generateMessageId(),
                    type: 'user',
                    content: content.trim(),
                    timestamp: new Date().toISOString(),
                    user_id: this.getUserId()
                };

                this.sendMessage(message);
                resolve();
            } catch (error) {
                reject(error);
            }
        });
    }

    public addMessageHandler(handler: MessageHandler): void {
        this.messageHandlers.push(handler);
    }

    public removeMessageHandler(handler: MessageHandler): void {
        const index = this.messageHandlers.indexOf(handler);
        if (index > -1) {
            this.messageHandlers.splice(index, 1);
        }
    }

    public isConnected(): boolean {
        return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
    }

    public dispose(): void {
        this.disposed = true;
        this.clearTimeouts();

        if (this.ws) {
            this.ws.close(1000, 'Extension disposing');
            this.ws = null;
        }

        this.messageHandlers = [];
        this.reconnectAttempts = 0;
        this.isConnecting = false;
    }

    // Private helper methods...
    private buildWebSocketUrl(): string {
        let serverUrl = this.config.serverUrl;

        // Ensure URL format is correct
        if (!serverUrl.startsWith('ws://') && !serverUrl.startsWith('wss://')) {
            // Convert http/https to ws/wss
            if (serverUrl.startsWith('https://')) {
                serverUrl = serverUrl.replace('https://', 'wss://');
            } else if (serverUrl.startsWith('http://')) {
                serverUrl = serverUrl.replace('http://', 'ws://');
            } else {
                // Default to ws if no protocol specified
                serverUrl = `ws://${serverUrl}`;
            }
        }

        // Remove trailing slash
        serverUrl = serverUrl.replace(/\/$/, '');

        // Add WebSocket endpoint
        return `${serverUrl}/ws`;
    }

    private startHeartbeat(): void {
        this.stopHeartbeat(); // Clear any existing heartbeat

        this.heartbeatTimeout = setTimeout(() => {
            if (this.isConnected()) {
                try {
                    this.sendMessage({
                        type: 'ping',
                        content: '',
                        timestamp: new Date().toISOString()
                    });

                    // Schedule next heartbeat
                    this.startHeartbeat();
                } catch (error) {
                    console.error('Failed to send heartbeat:', error);
                }
            }
        }, this.config.heartbeatInterval);
    }
}
```

## TypeScript Development

### Main Extension Entry Point

```typescript
// src/extension.ts
import * as vscode from 'vscode';
import { ChatPanel } from './webview/chatPanel';

export function activate(context: vscode.ExtensionContext) {
  console.log('AI Coding Agent extension activated');

  // Register commands
  const openChatCommand = vscode.commands.registerCommand(
    'aiCodingAgent.openChat',
    () => {
      ChatPanel.createOrShow(context.extensionUri);
    }
  );

  const newConversationCommand = vscode.commands.registerCommand(
    'aiCodingAgent.newConversation',
    () => {
      // Reset conversation in current panel
      if (ChatPanel.currentPanel) {
        ChatPanel.currentPanel.clearConversation();
      }
    }
  );

  // Add to subscriptions for cleanup
  context.subscriptions.push(openChatCommand, newConversationCommand);

  // Auto-open chat if configured
  const config = vscode.workspace.getConfiguration('aiCodingAgent');
  if (config.get<boolean>('autoOpenOnStartup', false)) {
    ChatPanel.createOrShow(context.extensionUri);
  }
}

export function deactivate() {
  console.log('AI Coding Agent extension deactivated');
}
```

### Type Definitions

```typescript
// src/types/api.ts
export interface CodeGenerationRequest {
  prompt: string;
  language: string;
  context?: string;
  file_path?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  error?: string;
  timestamp: string;
}

export interface AgentStatus {
  agentType: 'architect' | 'frontend' | 'backend' | 'shell' | 'issue_fix';
  status: 'idle' | 'processing' | 'completed' | 'failed';
  progress?: number;
  message?: string;
}
```

## Container Auto-Installation

### Auto-Installation Script

```javascript
// scripts/install.js
const { execSync } = require('child_process');
const fs = require('fs');

class ExtensionInstaller {
  constructor() {
    this.extensionId = 'ai-coding-agent.ai-coding-agent-chat';
    this.vsixPath = './dist/ai-coding-agent-chat.vsix';
  }

  async install() {
    try {
      if (!this.isCodeServerEnvironment()) {
        console.log('Not in code-server, skipping auto-install');
        return;
      }

      if (this.isExtensionInstalled()) {
        console.log('Extension already installed');
        return;
      }

      console.log('Installing AI Chat Extension...');
      execSync(`code-server --install-extension ${this.vsixPath}`, { stdio: 'inherit' });

      console.log('Configuring extension settings...');
      this.configureSettings();

      console.log('Extension installed successfully');
    } catch (error) {
      console.error('Installation failed:', error);
    }
  }

  isCodeServerEnvironment() {
    return process.env.CODE_SERVER === 'true' ||
           fs.existsSync('/home/<USER>/.local/share/code-server');
  }

  isExtensionInstalled() {
    try {
      const output = execSync('code-server --list-extensions', { encoding: 'utf8' });
      return output.includes(this.extensionId);
    } catch {
      return false;
    }
  }

  configureSettings() {
    const settingsPath = '/home/<USER>/.local/share/code-server/User/settings.json';
    const settings = {
      "aiCodingAgent.apiEndpoint": "http://ai-orchestrator:8000",
      "aiCodingAgent.websocketEndpoint": "ws://ai-orchestrator:8000/ws",
      "aiCodingAgent.autoOpenOnStartup": true
    };

    if (fs.existsSync(settingsPath)) {
      const existing = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
      Object.assign(existing, settings);
      fs.writeFileSync(settingsPath, JSON.stringify(existing, null, 2));
    } else {
      fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
    }
  }
}

// Run installer
new ExtensionInstaller().install();
```

### Docker Integration

```dockerfile
# In code-server Dockerfile
FROM codercom/code-server:latest

# Copy extension files
COPY ./ai-chat-extension /tmp/ai-chat-extension

# Install extension
RUN cd /tmp/ai-chat-extension && \
    npm install && \
    npm run package && \
    node scripts/install.js

# Set default settings
RUN mkdir -p /home/<USER>/.local/share/code-server/User && \
    echo '{"aiCodingAgent.apiEndpoint": "http://ai-orchestrator:8000"}' > \
    /home/<USER>/.local/share/code-server/User/settings.json
```

## Testing & Deployment

### Build & Package Commands

```bash
# Build extension
npm run compile

# Package as VSIX
npm install -g @vscode/vsce
vsce package

# Test installation
code --install-extension ai-coding-agent-chat-1.0.0.vsix
```

### Key Testing Strategies

1. **Unit Tests**: Test API service and message handling
2. **Integration Tests**: Test webview communication
3. **Container Tests**: Verify auto-installation in Docker
4. **E2E Tests**: Full workflow testing with backend

## Key Takeaways

1. **Webview API**: Use `createWebviewPanel` for custom UI with HTML/CSS/JS
2. **Message Passing**: Bi-directional communication via `postMessage`
3. **TypeScript**: Type-safe development with `@types/vscode`
4. **Container Integration**: Auto-installation via scripts in Docker
5. **Real-time Updates**: WebSocket for agent progress monitoring
6. **Context Awareness**: Access active files and workspace data
7. **VS Code Integration**: Commands, keybindings, and settings
8. **Extension Lifecycle**: Proper activation, disposal, and cleanup

This guide provides the foundation for building a robust VS Code extension that integrates seamlessly with the AI Coding Agent's orchestrator service.