# Entry point for ai-orchestrator
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
try:
    from slowapi import Limiter, _rate_limit_exceeded_handler  # type: ignore
    from slowapi.util import get_remote_address  # type: ignore
    from slowapi.errors import RateLimitExceeded  # type: ignore
    SLOWAPI_AVAILABLE = True
except ImportError:
    # Fallback when slowapi is not available
    SLOWAPI_AVAILABLE = False
    Limiter = None  # type: ignore
    RateLimitExceeded = Exception
    def get_remote_address(request):  # type: ignore
        return request.client.host if request.client else "unknown"
    def _rate_limit_exceeded_handler(request, exc):  # type: ignore
        return JSONResponse({"error": "Rate limit exceeded"}, status_code=429)
import logging
import os
from datetime import datetime

# Internal imports with centralized configuration
from .core.config import settings

from .router.routers import router
from .router.llm_router import router as llm_router, initialize_llm_service
from .router.role_management import router as role_router, initialize_role_configuration
from .router.supabase_router import router as supabase_router
from .router.redis_router import router as redis_router
from .router.websocket_router import router as websocket_router, initialize_websocket_chat
from .router.github_auth_router import router as github_auth_router
from .router.project_router import router as project_router
# Import new UserRepository-based router
from .routers.user_router import router as user_router
# Import workspace management router
from .routers.workspace_router import router as workspace_router
# Import comprehensive health check router
from .routers.health_router import router as health_router
# Import security monitoring router
from .routers.security_router import router as security_router
from .utils.auth import init_supabase, get_current_user
from .services.supabase_service import get_supabase_service, cleanup_supabase_service
from .services.vector_service import get_vector_service
from .services.rag_service import get_rag_service
from .services.redis_service import get_redis_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Rate limiting
if SLOWAPI_AVAILABLE and Limiter:
    limiter = Limiter(key_func=get_remote_address)
else:
    limiter = None

# Conditional rate limiting decorator
def rate_limit(limit_string: str):
    """Conditional rate limiting decorator."""
    def decorator(func):
        if SLOWAPI_AVAILABLE and limiter:
            return limiter.limit(limit_string)(func)
        return func
    return decorator

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    # Startup
    logger.info("Starting AI Orchestrator service...")

    # Initialize Redis services (always available)
    try:
        redis_manager = get_redis_manager()
        await redis_manager.initialize()
        logger.info("Redis services initialized successfully")
    except Exception as e:
        logger.warning(f"Redis initialization failed: {str(e)}. Some features may be unavailable.")

    # Check if Supabase is enabled
    use_supabase = settings.USE_SUPABASE

    if use_supabase:
        logger.info("Initializing Supabase services...")

        # Initialize Supabase service
        supabase_service = await get_supabase_service()
        logger.info("Supabase service initialized")

        # Initialize vector service
        vector_service = await get_vector_service()
        logger.info("Vector service initialized")

        # Initialize RAG service
        rag_service = await get_rag_service()
        logger.info("RAG service initialized")

    else:
        # Initialize legacy Supabase (auth only)
        init_supabase()
        logger.info("Legacy Supabase initialized")

    # Initialize enhanced LLM service
    await initialize_llm_service()
    logger.info("Enhanced LLM service initialized")

    # Initialize role configuration
    await initialize_role_configuration()
    logger.info("Role configuration initialized")

    # Initialize WebSocket chat services
    try:
        await initialize_websocket_chat()
        logger.info("WebSocket chat services initialized")
    except Exception as e:
        logger.warning(f"WebSocket chat initialization failed: {str(e)}. Chat features may be unavailable.")

    logger.info("AI Orchestrator service startup completed")
    yield

    # Shutdown
    logger.info("Shutting down AI Orchestrator service...")

    # Clean up Redis connections
    try:
        redis_manager = get_redis_manager()
        await redis_manager.close()
        logger.info("Redis connections closed")
    except Exception as e:
        logger.warning(f"Redis cleanup warning: {str(e)}")

    if use_supabase:
        await cleanup_supabase_service()
        logger.info("Supabase services cleaned up")

    logger.info("AI Orchestrator service shutdown completed")

app = FastAPI(
    title="AI Orchestrator",
    description="AI Coding Agent Orchestration Service with Enhanced LLM Integration",
    version="2.0.0",
    lifespan=lifespan
)


# =====================================================================================
# GLOBAL ERROR HANDLER
# =====================================================================================

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Global exception handler for all unhandled exceptions.

    This handler ensures that:
    1. No sensitive internal error details are leaked to clients
    2. All exceptions are properly logged for debugging
    3. Clients receive consistent, professional error responses
    4. The server continues running despite unexpected errors

    Args:
        request: The FastAPI request object
        exc: The unhandled exception

    Returns:
        JSONResponse: Standardized error response
    """
    # Generate a unique error ID for tracking
    import uuid
    error_id = str(uuid.uuid4())[:8]

    # Extract request information for logging
    client_host = getattr(request.client, 'host', 'unknown') if request.client else 'unknown'
    method = request.method
    url = str(request.url)

    # Log the full exception with context for debugging
    logger.exception(
        f"Global exception handler caught unhandled exception "
        f"[Error ID: {error_id}] "
        f"[Client: {client_host}] "
        f"[{method} {url}]: {str(exc)}",
        extra={
            "error_id": error_id,
            "client_host": client_host,
            "method": method,
            "url": url,
            "exception_type": type(exc).__name__,
            "exception_message": str(exc)
        }
    )

    # Return a generic, non-revealing error response
    return JSONResponse(
        status_code=500,
        content={
            "detail": "An internal server error occurred.",
            "error_id": error_id,
            "timestamp": datetime.utcnow().isoformat(),
            "status": "error"
        },
        headers={
            "X-Error-ID": error_id,
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY"
        }
    )


# Add rate limiting
if SLOWAPI_AVAILABLE and limiter:
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)

# Include routers
app.include_router(router)
app.include_router(llm_router)
app.include_router(role_router)
app.include_router(redis_router)  # Always include Redis router
app.include_router(websocket_router, prefix="/ws")  # WebSocket chat functionality
# Include new UserRepository-based user management router
app.include_router(user_router, prefix="/api/v2")  # Modern user management API
# Include workspace management router
app.include_router(workspace_router)  # Multi-tenant workspace management
# Include comprehensive health check router
app.include_router(health_router)  # System health monitoring
# Include security monitoring router
app.include_router(security_router)  # Security monitoring and validation
# Include dispatcher/task management router
from .routers.dispatcher_router import router as dispatcher_router
app.include_router(dispatcher_router)

# Include Supabase router if enabled
if settings.USE_SUPABASE:
    app.include_router(supabase_router)
    logger.info("Supabase router included")

@app.get("/health")
async def health_check():
    """Basic health check endpoint - no rate limiting for Docker health checks."""
    try:
        # Simple health check without complex dependencies
        return {
            "status": "ok",
            "service": "ai-orchestrator",
            "version": "2.0.0",
            "timestamp": datetime.utcnow().isoformat(),
            "comprehensive_health": "/api/v1/health/detailed",
            "readiness_probe": "/api/v1/health/ready",
            "liveness_probe": "/api/v1/health/live",
            "metrics": "/api/v1/health/metrics"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "error",
            "service": "ai-orchestrator",
            "version": "2.0.0",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/")
async def read_root():
    """Root endpoint with service information."""
    use_supabase = settings.USE_SUPABASE

    features = [
        "Enhanced LLM integration",
        "Multi-provider support",
        "Rate limiting and cost control",
        "Comprehensive monitoring",
        "Fallback mechanisms",
        "WebSocket real-time chat with AI agents"
    ]

    endpoints = {
        "health": "/health",
        "websocket_chat": "/ws (WebSocket for real-time chat)",
        "llm_health": "/api/llm/health",
        "providers": "/api/llm/providers",
        "models": "/api/llm/models",
        "generate": "/api/llm/generate",
        "agents": "/api/agents",
        "roles": "/api/roles",
        "role_config": "/api/roles/{role_name}",
        "provider_models": "/api/roles/providers/models",
        "redis_health": "/api/v1/redis/health",
        "redis_stats": "/api/v1/redis/stats",
        "cache_management": "/api/v1/redis/cache/*"
    }

    if use_supabase:
        features.extend([
            "Supabase authentication",
            "Vector storage and search",
            "RAG (Retrieval-Augmented Generation)",
            "Row Level Security (RLS)",
            "Permission-aware operations"
        ])

        endpoints.update({
            "supabase_health": "/api/v1/supabase/health",
            "auth_register": "/api/v1/supabase/auth/register",
            "auth_login": "/api/v1/supabase/auth/login",
            "user_profile": "/api/v1/supabase/user/profile",
            "projects": "/api/v1/supabase/projects",
            "search": "/api/v1/supabase/search",
            "rag_generate": "/api/v1/supabase/rag/generate",
            "user_stats": "/api/v1/supabase/stats/user"
        })

    # Always add Redis features
    features.extend([
        "Redis caching and session management",
        "Real-time messaging via pub/sub",
        "Background task processing",
        "Rate limiting and API protection"
    ])

    return {
        "message": "AI Coding Agent Orchestrator",
        "version": "2.0.0",
        "status": "running",
        "supabase_enabled": use_supabase,
        "features": features,
        "endpoints": endpoints
    }

@app.get("/api/agents")
@rate_limit("60/minute")
async def list_agents(request: Request, current_user = Depends(get_current_user)):
    """List available AI agents."""
    return {
        "agents": [
            {
                "name": "architect",
                "status": "ready",
                "description": "System architecture and design agent",
                "capabilities": ["system_design", "architecture_review", "documentation"]
            },
            {
                "name": "backend",
                "status": "ready",
                "description": "Backend development agent",
                "capabilities": ["api_development", "database_design", "server_optimization"]
            },
            {
                "name": "frontend",
                "status": "ready",
                "description": "Frontend development agent",
                "capabilities": ["ui_development", "responsive_design", "user_experience"]
            },
            {
                "name": "issue_fix",
                "status": "ready",
                "description": "Bug fixing and debugging agent",
                "capabilities": ["bug_analysis", "code_debugging", "performance_optimization"]
            },
            {
                "name": "shell",
                "status": "ready",
                "description": "Command line and system operations agent",
                "capabilities": ["command_execution", "system_administration", "automation"]
            }
        ]
    }

@app.get("/api/system/info")
@rate_limit("30/minute")
async def system_info(request: Request):
    """Get system information and configuration."""
    try:
        return {
            "service": "ai-orchestrator",
            "version": "2.0.0",
            "environment": settings.ENVIRONMENT.value,
            "features": {
                "enhanced_llm": True,
                "multi_provider": True,
                "rate_limiting": True,
                "cost_control": True,
                "monitoring": True,
                "fallback_support": True
            },
            "providers": {
                "ollama": {
                    "enabled": True,
                    "base_url": str(settings.OLLAMA_BASE_URL)
                },
                "openrouter": {
                    "enabled": settings.openrouter_available,
                    "configured": settings.openrouter_available
                },
                "openai": {
                    "enabled": settings.openai_available,
                    "configured": settings.openai_available
                },
                "anthropic": {
                    "enabled": settings.anthropic_available,
                    "configured": settings.anthropic_available
                }
            },
            "configuration": {
                "default_local_provider": settings.DEFAULT_LOCAL_PROVIDER.value,
                "default_cloud_provider": settings.DEFAULT_CLOUD_PROVIDER.value,
                "cloud_fallback_enabled": settings.ENABLE_CLOUD_FALLBACK,
                "redis_available": settings.redis_available
            }
        }
    except Exception as e:
        logger.error(f"Failed to get system info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve system information")

# Legacy endpoints for backward compatibility
@app.get("/api/models")
@rate_limit("60/minute")
async def list_models_legacy(request: Request, current_user = Depends(get_current_user)):
    """Legacy endpoint - redirects to new LLM models endpoint."""
    # This redirects to the new enhanced endpoint
    from .router.llm_router import get_llm_service

    try:
        llm_service = await get_llm_service()
        models = await llm_service.list_available_models()

        # Convert to legacy format for compatibility
        models_legacy = []
        for model in models:
            models_legacy.append({
                "name": model.name,
                "provider": model.provider.value,
                "status": model.status.value,
                "size": model.size,
                "modified_at": model.modified_at
            })

        return {"models": models_legacy}
    except Exception as e:
        logger.error(f"Failed to list models (legacy): {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/ollama/status")
@rate_limit("60/minute")
async def ollama_status_legacy(request: Request):
    """Legacy Ollama status endpoint."""
    from .router.llm_router import get_llm_service
    from .models.llm_models import LLMProvider

    try:
        llm_service = await get_llm_service()
        status = await llm_service.test_provider_connection(LLMProvider.OLLAMA)

        return {
            "connected": status.available,
            "base_url": llm_service.providers[LLMProvider.OLLAMA]['base_url'],
            "message": "Connected to Ollama on host" if status.available else f"Cannot connect to Ollama: {status.error_message}",
            "response_time_ms": status.response_time_ms
        }
    except Exception as e:
        logger.error(f"Failed to check Ollama status: {str(e)}")
        return {
            "connected": False,
            "message": f"Error checking Ollama status: {str(e)}"
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
