# Project: AI Coding Agent
# Purpose: Main source package initialization with comprehensive exports

"""
AI Orchestrator Source Package

This is the main source package for the AI Orchestrator service,
containing all agents, models, services, routers, and utilities
for the containerized AI coding assistant.
"""

import logging

logger = logging.getLogger(__name__)

# Package availability flags
AGENTS_AVAILABLE = False
MODELS_AVAILABLE = False
SERVICES_AVAILABLE = False
ROUTERS_AVAILABLE = False
UTILS_AVAILABLE = False
MONITORING_AVAILABLE = False

# ===== AGENTS PACKAGE =====
try:
    from .agents import *  # noqa: F403, F401
    AGENTS_AVAILABLE = True
    logger.info("Agents package loaded successfully")
except ImportError as e:
    logger.error(f"Failed to import agents package: {e}")
    AGENTS_AVAILABLE = False

# ===== MODELS PACKAGE =====
try:
    from .models import *  # noqa: F403, F401
    MODELS_AVAILABLE = True
    logger.info("Models package loaded successfully")
except ImportError as e:
    logger.error(f"Failed to import models package: {e}")
    MODELS_AVAILABLE = False

# ===== SERVICES PACKAGE =====
try:
    from .services import *  # noqa: F403, F401
    SERVICES_AVAILABLE = True
    logger.info("Services package loaded successfully")
except ImportError as e:
    logger.error(f"Failed to import services package: {e}")
    SERVICES_AVAILABLE = False

# ===== ROUTERS PACKAGE =====
try:
    from .router import *  # noqa: F403, F401
    ROUTERS_AVAILABLE = True
    logger.info("Routers package loaded successfully")
except ImportError as e:
    logger.error(f"Failed to import routers package: {e}")
    ROUTERS_AVAILABLE = False

# ===== UTILS PACKAGE =====
try:
    from .utils import *  # noqa: F403, F401
    UTILS_AVAILABLE = True
    logger.info("Utils package loaded successfully")
except ImportError as e:
    logger.error(f"Failed to import utils package: {e}")
    UTILS_AVAILABLE = False

# ===== MONITORING PACKAGE =====
try:
    from .monitoring import *  # noqa: F403, F401
    MONITORING_AVAILABLE = True
    logger.info("Monitoring package loaded successfully")
except ImportError as e:
    logger.debug(f"Monitoring package not available: {e}")
    MONITORING_AVAILABLE = False

# Package status information
def get_package_status():
    """Get the availability status of all packages."""
    return {
        "agents": AGENTS_AVAILABLE,
        "models": MODELS_AVAILABLE,
        "services": SERVICES_AVAILABLE,
        "routers": ROUTERS_AVAILABLE,
        "utils": UTILS_AVAILABLE,
        "monitoring": MONITORING_AVAILABLE
    }

# Log package availability summary
status = get_package_status()
available_packages = [pkg for pkg, available in status.items() if available]
unavailable_packages = [pkg for pkg, available in status.items() if not available]

logger.info(f"AI Orchestrator package initialization complete")
logger.info(f"Available packages: {', '.join(available_packages) if available_packages else 'None'}")
if unavailable_packages:
    logger.warning(f"Unavailable packages: {', '.join(unavailable_packages)}")

# Make availability flags easily accessible
__version__ = "1.0.0"
__package_status__ = status

# Define comprehensive exports for clean imports and proper IDE support
__all__ = [
    # Package status function
    "get_package_status",

    # Agent classes (from agents package)
    "BaseAgent",
    "ArchitectAgent",
    "BackendAgent",
    "FrontendAgent",
    "ShellAgent",
    "IssueFixAgent",
    "get_agent_class",
    "get_available_agents",
    "AGENT_CLASSES",

    # Model classes (from models package)
    "LLMProvider",
    "ModelStatus",
    "LLMModel",
    "GenerateRequest",
    "UsageStats",
    "LLMResponse",
    "ProviderStatus",
    "ModelPullRequest",
    "ModelPullResponse",
    "HealthCheckResponse",
    "RateLimitInfo",
    "LLMError",
    "ProviderUnavailableError",
    "InvalidAPIKeyError",
    "RateLimitExceededError",
    "ModelNotFoundError",
    "GenerationError",
    "RoleConfigError",
    "InvalidRoleConfigError",
    "RoleNotFoundError",
    "ConfigPersistenceError",
    "RoleConfiguration",
    "RoleConfigurationUpdate",
    "RoleConfigurationList",
    "RoleConfigurationResponse",
    "RoleListResponse",
    "ProviderModelsResponse",
    "TaskType",
    "ErrorType",
    "AgentType",
    "ExecutionStatus",
    "ApprovalStatus",
    "ValidationResult",
    "TaskResult",
    "Task",
    "Step",
    "Phase",
    "Roadmap",
    "RecoveryResult",
    "HealthCheckResult",
    "ApprovalRequest",
    "Checkpoint",
    "ProductionReadinessResult",

    # Service classes (from services package)
    "TaskValidator",
    "ErrorRecoverySystem",
    "CheckpointManager",
    "EnhancedLLMService",

    # Router components (from router package)
    "auth_router",
    "llm_router",
    "role_management_router",
    "main_router",
    "initialize_llm_service",
    "initialize_role_configuration",
    "get_llm_service",
    "get_redis_client",
    "RoleConfigurationManager",

    # Utility functions (from utils package)
    "init_supabase",
    "get_supabase",
    "verify_token",
    "create_access_token",
    "get_current_user",
    "hash_password",
    "verify_password",
    "get_auth_status",
    "TokenData",
    "UniversalLLMService",
    "LLMService",
    "llm_service",
    "generate_request_id",
    "sanitize_filename",
    "ensure_directory",
    "safe_json_loads",
    "safe_json_dumps",
    "get_environment_info",
    "format_bytes",
    "is_valid_email",
    "truncate_string",
    "merge_dicts",

    # Monitoring components (from monitoring package)
    "LLMResourceMonitor",
    "ResourceMetrics",
    "LLMPerformanceMetrics",
    "monitor",
    "start_monitoring",
    "stop_monitoring",
    "update_llm_metrics",
    "get_metrics_summary",
    "get_monitoring_status",
    "MONITORING_AVAILABLE",
    "PROMETHEUS_AVAILABLE",

    # Package availability flags
    "AGENTS_AVAILABLE",
    "MODELS_AVAILABLE",
    "SERVICES_AVAILABLE",
    "ROUTERS_AVAILABLE",
    "UTILS_AVAILABLE",
]