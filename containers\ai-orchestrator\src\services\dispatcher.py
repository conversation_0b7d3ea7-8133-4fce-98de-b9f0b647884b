"""
Dispatcher service: runs a single pass of the agent processing loop.

This implementation:
- Finds projects that have pending tasks
- Tries to acquire a per-project lock
- If locked, fetches the next pending task, marks it running in AgentState
- Selects the appropriate sequential agent and executes it
- Saves output and marks task completed
- Releases the lock

Note: DB interactions are sync via SQLAlchemy Session, but this class is async-compatible to
integrate with other async services (like Redis).
"""
from __future__ import annotations

import logging
from typing import List, Optional, Dict, Callable, Type

from sqlalchemy import select
from sqlalchemy.orm import Session

from ..models.project import Project
from ..models.task import Task
from ..models.agent_state import AgentState
from ..models.database import SessionLocal

from .lock_manager import LockManager
from ..repository.task_repository import TaskRepository

# Import simplified sequential agents
from ..sequential_agents.architect import ArchitectAgent
from ..sequential_agents.backend import BackendAgent
from ..sequential_agents.frontend import FrontendAgent
from ..sequential_agents.shell import ShellAgent
from ..sequential_agents.issue_fix import IssueFixAgent
from ..sequential_agents.base import BaseAgent as SeqBaseAgent

logger = logging.getLogger(__name__)


class Dispatcher:
    """Coordinates task execution across projects using locking."""

    def __init__(self, lock_manager: LockManager, session_factory=SessionLocal) -> None:
        self.lock_manager = lock_manager
        self.session_factory = session_factory
        # Map task.agent_role to a sequential agent class
        self.agent_registry: Dict[str, Callable[[], SeqBaseAgent]] = {
            "architect": lambda: ArchitectAgent(),
            "backend": lambda: BackendAgent(),
            "frontend": lambda: FrontendAgent(),
            "shell": lambda: ShellAgent(),
            "issue_fix": lambda: IssueFixAgent(),
        }

    async def run_once(self) -> None:
        """Run a single dispatcher cycle."""
        db: Session = self.session_factory()
        try:
            # 1) Find projects that have at least one pending task
            projects_with_pending = self._get_projects_with_pending_tasks(db)
            if not projects_with_pending:
                logger.debug("No projects with pending tasks found")
                return

            for project in projects_with_pending:
                # 2) Try to acquire a lock for this project
                acquired = await self.lock_manager.acquire_lock(
                    project_id=project.id,
                    agent_role="dispatcher",
                    task_id=0,
                    timeout_seconds=600,
                )
                if not acquired:
                    logger.debug(f"Lock not acquired for project {project.id}")
                    continue

                try:
                    # 3) Fetch the next pending task for this project
                    task = await TaskRepository.get_next_pending_task_for_project(db, project.id)
                    if not task:
                        logger.debug(f"No pending tasks for project {project.id} after lock acquisition")
                        continue

                    # 4) Update AgentState to reflect running task/agent
                    self._mark_agent_running(db, project_id=project.id, task_id=task.id, agent_role=task.agent_role)

                    # 5) Select and execute the appropriate sequential agent
                    agent_fn = self.agent_registry.get(task.agent_role.lower())
                    if not agent_fn:
                        logger.warning(f"No agent registered for role '{task.agent_role}', skipping task {task.id}")
                        # Mark task failed to avoid starvation
                        await TaskRepository.update_task_status(db, task_id=task.id, status="failed")
                        continue

                    agent = agent_fn()
                    result = await agent.execute(task_input=task.input_data or {})

                    # 6) Persist output and mark as completed
                    await TaskRepository.record_task_output(db, task_id=task.id, output_data=result)
                    await TaskRepository.update_task_status(db, task_id=task.id, status="completed")

                finally:
                    # 7) Always release the lock for this project
                    await self.lock_manager.release_lock(project.id)

        except Exception as e:
            logger.error(f"Dispatcher run_once error: {e}")
        finally:
            db.close()

    # Internal helpers

    def _get_projects_with_pending_tasks(self, db: Session) -> List[Project]:
        """Return list of projects that have at least one pending task."""
        subq = select(Task.project_id).where(Task.status == "pending").distinct().subquery()
        stmt = select(Project).where(Project.id.in_(select(subq.c.project_id)))
        result = db.execute(stmt)
        return list(result.scalars().all())

    def _mark_agent_running(self, db: Session, *, project_id: int, task_id: int, agent_role: str) -> None:
        """Upsert AgentState to reflect an active agent working on a task."""
        # Find existing AgentState for the project
        stmt = select(AgentState).where(AgentState.project_id == project_id)
        state: Optional[AgentState] = db.execute(stmt).scalars().first()

        if state is None:
            state = AgentState(
                project_id=project_id,
                active_agent_role=agent_role,
                current_task_id=task_id,
            )
            db.add(state)
        else:
            state.active_agent_role = agent_role
            state.current_task_id = task_id

        # Also mark the task as running immediately
        task_stmt = select(Task).where(Task.id == task_id)
        task = db.execute(task_stmt).scalars().first()
        if task:
            task.status = "running"
            db.add(task)

        db.commit()
        if state:
            db.refresh(state)