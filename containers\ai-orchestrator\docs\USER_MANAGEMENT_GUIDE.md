# 🎭 User Management System Guide

## **Complete AI Coding Agent User Management**

This guide covers the comprehensive user management system that bridges **Supabase Auth** with custom user profiles, implementing the Repository Pattern for clean, scalable data access.

---

## **🏗️ Architecture Overview**

```
🔐 Supabase Auth ←→ 🎭 UserRepository ←→ 🧠 Vector Database
├─ JWT tokens          ├─ Profile mgmt        ├─ User-scoped docs
├─ Login/Register      ├─ Project relations   ├─ Permission-aware RAG
└─ Session mgmt        └─ Activity tracking   └─ AI context building
```

### **Key Components:**
- **Supabase Auth**: Handles authentication, JWT tokens, and sessions
- **UserRepository**: Bridge layer implementing Repository Pattern
- **Custom User Model**: Extended user profiles with AI-specific data
- **API Endpoints**: RESTful interface for user management
- **Pydantic Schemas**: Type-safe request/response validation

---

## **📋 Quick Start**

### **1. Database Migration**
Apply the database schema changes:

```bash
# Navigate to AI orchestrator directory
cd containers/ai-orchestrator

# Generate migration for supabase_user_id field
alembic revision --autogenerate -m "Add supabase_user_id foreign key"

# Apply migration
alembic upgrade head
```

### **2. Environment Configuration**
Set required environment variables:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_JWT_SECRET=your-jwt-secret

# Development settings
ENVIRONMENT=development  # or production
USE_MOCK_AUTH=false      # Set to true for development testing
```

### **3. API Access**
The user management API is available at `/api/v2/users/*`:

```bash
# Get current user profile (requires authentication)
GET /api/v2/users/me

# Update current user profile
PUT /api/v2/users/me

# List users (admin only)
GET /api/v2/users/

# Get specific user (admin only)
GET /api/v2/users/{user_id}

# Health check
GET /api/v2/users/health
```

---

## **🔧 Using the UserRepository**

### **Basic Repository Usage**

```python
from src.repository.user_repository import UserRepository
from src.models.database import get_db
from src.schemas.user_schemas import SupabaseUser

# Initialize repository
user_repo = UserRepository()

# Get database session
db = next(get_db())

# Example: Get or create user from Supabase
supabase_user = SupabaseUser(
    id=uuid4(),
    email="<EMAIL>",
    created_at=datetime.utcnow(),
    updated_at=datetime.utcnow(),
    user_metadata={"full_name": "John Doe"},
    app_metadata={}
)

result = user_repo.get_or_create_user_from_supabase(db, supabase_user)
print(f"User: {result.user.username} (Created: {result.created_from_supabase})")
```

### **Profile Management**

```python
from src.schemas.user_schemas import UserProfileUpdateSchema

# Update user profile
profile_update = UserProfileUpdateSchema(
    full_name="Updated Name",
    preferences={"theme": "dark", "language": "en"}
)

user_model = user_repo.get_by_id(db, user_id)
updated_user = user_repo.update_profile(db, user_model, profile_update)
```

### **Pagination and Search**

```python
# Get paginated users with search
result = user_repo.get_users_paginated(
    db=db,
    page=1,
    page_size=20,
    search="john",
    is_active=True
)

print(f"Found {result.total} users, showing page {result.page}")
for user in result.users:
    print(f"- {user.username} ({user.email})")
```

---

## **🌐 API Endpoints Guide**

### **Authentication**
All endpoints require valid Supabase JWT token in Authorization header:

```http
Authorization: Bearer <supabase-jwt-token>
```

### **Get Current User Profile**
```http
GET /api/v2/users/me
```

**Response:**
```json
{
  "id": 1,
  "username": "johndoe",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "is_active": true,
  "is_superuser": false,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "last_login": "2024-01-01T12:00:00Z",
  "display_name": "John Doe"
}
```

### **Update Current User Profile**
```http
PUT /api/v2/users/me
Content-Type: application/json

{
  "full_name": "John Smith",
  "preferences": {
    "theme": "dark",
    "language": "en",
    "notifications": true
  }
}
```

### **List Users (Admin Only)**
```http
GET /api/v2/users/?page=1&page_size=20&search=john&is_active=true
```

**Response:**
```json
{
  "users": [...],
  "total": 150,
  "page": 1,
  "page_size": 20,
  "total_pages": 8,
  "has_next": true,
  "has_previous": false
}
```

---

## **🔐 Authentication System**

### **Production Authentication**
Uses real Supabase JWT validation:

```python
from src.services.auth_dependencies import get_current_user_from_supabase

# Validates JWT token and returns SupabaseUser
current_user = await get_current_user_from_supabase(credentials)
```

### **Development Authentication**
For development, you can use mock authentication:

```env
ENVIRONMENT=development
USE_MOCK_AUTH=true
```

This bypasses JWT validation and provides mock users for testing.

### **Admin Authorization**
Admin endpoints check for admin role in user metadata:

```python
# User must have role="admin" in app_metadata or user_metadata
is_admin = (
    current_user.app_metadata.get("role") == "admin" or
    current_user.user_metadata.get("admin") is True
)
```

---

## **📊 Database Schema**

### **User Model Schema**
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    supabase_user_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_superuser BOOLEAN DEFAULT false,
    profile_data TEXT, -- JSON field for additional data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);
```

### **Indexes for Performance**
```sql
CREATE INDEX idx_users_supabase_user_id ON users(supabase_user_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_active ON users(is_active);
```

---

## **🧪 Testing**

### **Run Repository Tests**
```bash
# Run all user repository tests
pytest tests/test_user_repository_integration.py -v

# Run specific test categories
pytest tests/test_user_repository_integration.py::TestUserRepository -v
pytest tests/test_user_repository_integration.py::TestUserAPI -v
```

### **Test Coverage**
The test suite covers:
- ✅ Repository CRUD operations
- ✅ Supabase integration
- ✅ API endpoints
- ✅ Authentication flows
- ✅ Error handling
- ✅ Performance validation
- ✅ Complete user lifecycle

---

## **🚀 Advanced Usage**

### **Custom Authentication Dependencies**
Create specialized authentication requirements:

```python
from src.services.auth_dependencies import require_user_permissions

# Require specific permissions
require_project_access = require_user_permissions(["project.read", "project.write"])

@router.get("/projects/")
async def get_projects(
    current_user: SupabaseUser = Depends(require_project_access)
):
    # User has verified project permissions
    pass
```

### **Repository Extension**
Extend repository for custom business logic:

```python
class CustomUserRepository(UserRepository):
    async def get_users_by_project(self, db: Session, project_id: str):
        """Get all users associated with a project."""
        # Custom query implementation
        pass

    async def update_user_activity(self, db: Session, user_id: int):
        """Track user activity."""
        # Custom activity tracking
        pass
```

### **Bulk Operations**
Handle multiple users efficiently:

```python
async def bulk_create_users(
    supabase_users: List[SupabaseUser],
    user_repo: UserRepository,
    db: Session
):
    """Create multiple users from Supabase data."""
    results = []

    for supabase_user in supabase_users:
        result = user_repo.get_or_create_user_from_supabase(db, supabase_user)
        results.append(result)

    return results
```

---

## **🛡️ Security Considerations**

### **1. JWT Token Validation**
- Always validate JWT signature
- Check token expiration
- Verify audience claims
- Handle token refresh

### **2. Authorization Patterns**
```python
# Role-based access control
@require_role("admin")
async def admin_endpoint():
    pass

# Permission-based access control
@require_permissions(["user.read", "user.write"])
async def user_management_endpoint():
    pass

# Resource-based access control
@require_resource_access("project", "read")
async def project_endpoint(project_id: str):
    pass
```

### **3. Data Validation**
- All inputs validated with Pydantic schemas
- SQL injection protection via SQLAlchemy
- XSS protection in API responses
- Rate limiting on endpoints

---

## **📈 Performance Optimization**

### **1. Database Optimization**
- Proper indexing on frequently queried fields
- Connection pooling configured
- Query optimization with eager loading
- Pagination for large datasets

### **2. Caching Strategy**
```python
# Example Redis caching for user profiles
async def get_cached_user_profile(user_id: int):
    cache_key = f"user_profile:{user_id}"
    cached = await redis.get(cache_key)

    if cached:
        return UserResponse.parse_raw(cached)

    # Fetch from database and cache
    user = await user_repo.get_by_id(db, user_id)
    user_response = UserResponse.from_user_model(user)

    await redis.setex(cache_key, 3600, user_response.json())
    return user_response
```

### **3. Async Operations**
```python
async def concurrent_user_operations():
    """Handle multiple user operations concurrently."""
    tasks = [
        user_repo.get_by_id(db, user_id)
        for user_id in user_ids
    ]

    users = await asyncio.gather(*tasks)
    return users
```

---

## **🔧 Troubleshooting**

### **Common Issues**

**1. JWT Token Invalid**
```
Error: "Invalid authentication credentials"
Solution: Check SUPABASE_JWT_SECRET configuration
```

**2. User Creation Fails**
```
Error: "User creation failed due to data constraint"
Solution: Check for duplicate email/username conflicts
```

**3. Database Connection Issues**
```
Error: "Failed to retrieve user"
Solution: Verify DATABASE_URL and connection pool settings
```

### **Debug Mode**
Enable detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Repository operations will show detailed SQL queries
# Authentication will show JWT validation steps
# API calls will show request/response details
```

---

## **🎯 Best Practices**

### **1. Repository Pattern**
- Keep repositories focused on data access only
- Use dependency injection for testability
- Handle errors at repository level
- Return domain models, not database models

### **2. Authentication Flow**
```python
# ✅ Good: Use dependency injection
async def endpoint(current_user: SupabaseUser = Depends(get_current_user)):
    pass

# ❌ Bad: Manual token handling
async def endpoint(authorization: str = Header()):
    token = extract_token(authorization)  # Don't do this
```

### **3. Error Handling**
```python
try:
    result = user_repo.get_or_create_user_from_supabase(db, supabase_user)
    return result.user
except UserRepositoryError as e:
    # Specific repository errors
    logger.error(f"Repository error: {e}")
    raise HTTPException(status_code=500, detail="User operation failed")
except Exception as e:
    # Unexpected errors
    logger.error(f"Unexpected error: {e}")
    raise HTTPException(status_code=500, detail="Internal server error")
```

---

## **📚 Additional Resources**

- **API Documentation**: Available at `/docs` when running the service
- **Database Migrations**: See `alembic/versions/` directory
- **Test Examples**: Check `tests/test_user_repository_integration.py`
- **Supabase Docs**: [https://supabase.com/docs](https://supabase.com/docs)
- **FastAPI Docs**: [https://fastapi.tiangolo.com](https://fastapi.tiangolo.com)

---

## **🎉 Conclusion**

You now have a **production-ready user management system** that:

- ✅ **Seamlessly integrates** Supabase Auth with custom profiles
- ✅ **Implements Repository Pattern** for clean, testable code
- ✅ **Provides comprehensive APIs** for all user operations
- ✅ **Handles authentication** with JWT validation and role-based access
- ✅ **Scales efficiently** with pagination, caching, and async operations
- ✅ **Maintains security** with proper validation and authorization
- ✅ **Supports testing** with comprehensive test coverage

This foundation enables your AI Coding Agent to have **permission-aware operations**, **user-scoped data access**, and **robust user management** - essential for production deployment! 🚀