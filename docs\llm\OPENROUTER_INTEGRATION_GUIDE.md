# OpenRouter Integration Guide for AI Coding Agent

## Overview

OpenRouter provides a unified API gateway to 400+ AI models from different providers, offering automatic fallbacks, cost optimization, and model diversity. This guide demonstrates how to integrate OpenRouter as the primary LLM provider for the AI Coding Agent, replacing or supplementing Ollama for enhanced model access and reliability.

## Table of Contents

- [OpenRouter Integration Guide for AI Coding Agent](#openrouter-integration-guide-for-ai-coding-agent)
  - [Overview](#overview)
  - [Table of Contents](#table-of-contents)
  - [Why OpenRouter for AI Coding Agent](#why-openrouter-for-ai-coding-agent)
    - [Key Benefits](#key-benefits)
    - [Use Cases in AI Coding Agent](#use-cases-in-ai-coding-agent)
  - [Architecture Integration](#architecture-integration)
    - [Updated System Architecture](#updated-system-architecture)
  - [Model Selection Strategy](#model-selection-strategy)
    - [Task-Specific Model Mapping](#task-specific-model-mapping)
  - [OpenRouter Service Implementation](#openrouter-service-implementation)
    - [Core OpenRouter Service](#core-openrouter-service)
    - [Cost Optimization Service](#cost-optimization-service)
  - [FastAPI Integration](#fastapi-integration)
    - [Updated LLM Endpoints](#updated-llm-endpoints)
  - [Environment Configuration](#environment-configuration)
    - [OpenRouter Environment Variables](#openrouter-environment-variables)
  - [Key Takeaways](#key-takeaways)

## Why OpenRouter for AI Coding Agent

### Key Benefits

1. **Model Diversity**: Access to 400+ models from OpenAI, Anthropic, Google, Meta, and more
2. **Cost Optimization**: Automatic routing to most cost-effective models
3. **Reliability**: Built-in fallbacks and redundancy
4. **Unified API**: Single interface for all providers (OpenAI-compatible)
5. **Real-time Pricing**: Dynamic pricing optimization
6. **Streaming Support**: Real-time response streaming
7. **Specialized Models**: Access to coding-specific models

### Use Cases in AI Coding Agent

- **Code Generation**: Use coding-specialized models (CodeLlama, DeepSeek-Coder)
- **Code Review**: Leverage reasoning models (GPT-4, Claude-3.5-Sonnet)
- **Documentation**: Use fast, cost-effective models for docs generation
- **Security Analysis**: Employ specialized security-focused models
- **Validation**: Multi-model consensus for critical validations

## Architecture Integration

### Updated System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                   AI Coding Agent                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Code Gen    │  │ Validation  │  │ Approval    │        │
│  │ Workflow    │  │ Pipeline    │  │ Workflow    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                 OpenRouter LLM Service                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Model       │  │ Cost        │  │ Fallback    │        │
│  │ Router      │  │ Optimizer   │  │ Manager     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                  OpenRouter API Gateway                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ GPT-4o      │  │ Claude-3.5  │  │ CodeLlama   │        │
│  │ OpenAI      │  │ Anthropic   │  │ Meta        │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Gemini Pro  │  │ DeepSeek    │  │ Local       │        │
│  │ Google      │  │ Coder       │  │ Ollama      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## Model Selection Strategy

### Task-Specific Model Mapping

```python
# src/services/model_selector.py
from typing import Dict, List, Optional, Literal
from dataclasses import dataclass
from enum import Enum

class TaskType(Enum):
    CODE_GENERATION = "code_generation"
    CODE_REVIEW = "code_review"
    DOCUMENTATION = "documentation"
    SECURITY_ANALYSIS = "security_analysis"
    SYNTAX_VALIDATION = "syntax_validation"
    BUG_FIXING = "bug_fixing"
    REFACTORING = "refactoring"
    EXPLANATION = "explanation"

@dataclass
class ModelConfig:
    """Configuration for a specific model."""
    id: str
    name: str
    provider: str
    cost_per_1k_tokens: float
    context_length: int
    capabilities: List[str]
    performance_tier: Literal["fast", "balanced", "premium"]
    best_for: List[TaskType]

class ModelSelector:
    """Smart model selection based on task requirements."""

    MODELS_CONFIG = {
        # Coding Specialists
        "deepseek/deepseek-coder": ModelConfig(
            id="deepseek/deepseek-coder",
            name="DeepSeek Coder",
            provider="DeepSeek",
            cost_per_1k_tokens=0.00014,
            context_length=16384,
            capabilities=["code_generation", "code_completion", "debugging"],
            performance_tier="balanced",
            best_for=[TaskType.CODE_GENERATION, TaskType.BUG_FIXING]
        ),
        "meta-llama/codellama-34b": ModelConfig(
            id="meta-llama/codellama-34b",
            name="Code Llama 34B",
            provider="Meta",
            cost_per_1k_tokens=0.00076,
            context_length=16384,
            capabilities=["code_generation", "code_completion"],
            performance_tier="balanced",
            best_for=[TaskType.CODE_GENERATION, TaskType.REFACTORING]
        ),

        # General Purpose Premium
        "openai/gpt-4o": ModelConfig(
            id="openai/gpt-4o",
            name="GPT-4o",
            provider="OpenAI",
            cost_per_1k_tokens=0.0025,
            context_length=128000,
            capabilities=["reasoning", "analysis", "code_review"],
            performance_tier="premium",
            best_for=[TaskType.CODE_REVIEW, TaskType.SECURITY_ANALYSIS]
        ),
        "anthropic/claude-3.5-sonnet": ModelConfig(
            id="anthropic/claude-3.5-sonnet",
            name="Claude 3.5 Sonnet",
            provider="Anthropic",
            cost_per_1k_tokens=0.003,
            context_length=200000,
            capabilities=["reasoning", "analysis", "long_context"],
            performance_tier="premium",
            best_for=[TaskType.CODE_REVIEW, TaskType.DOCUMENTATION]
        ),

        # Fast & Cost-Effective
        "google/gemini-flash-1.5": ModelConfig(
            id="google/gemini-flash-1.5",
            name="Gemini 1.5 Flash",
            provider="Google",
            cost_per_1k_tokens=0.00075,
            context_length=1000000,
            capabilities=["fast_inference", "long_context"],
            performance_tier="fast",
            best_for=[TaskType.SYNTAX_VALIDATION, TaskType.EXPLANATION]
        ),

        # Local Fallback
        "local/ollama": ModelConfig(
            id="local/ollama",
            name="Local Ollama",
            provider="Local",
            cost_per_1k_tokens=0.0,
            context_length=8192,
            capabilities=["local_inference", "privacy"],
            performance_tier="fast",
            best_for=[TaskType.EXPLANATION, TaskType.SYNTAX_VALIDATION]
        )
    }

    def select_model(
        self,
        task_type: TaskType,
        context_length_needed: int = 4096,
        budget_tier: Literal["economy", "balanced", "premium"] = "balanced",
        require_privacy: bool = False
    ) -> ModelConfig:
        """Select optimal model based on task requirements."""

        candidates = []

        for model_id, config in self.MODELS_CONFIG.items():
            # Filter by requirements
            if require_privacy and config.provider != "Local":
                continue

            if context_length_needed > config.context_length:
                continue

            if task_type not in config.best_for:
                continue

            # Score based on budget tier preference
            budget_score = self._calculate_budget_score(config, budget_tier)
            task_score = self._calculate_task_score(config, task_type)

            total_score = (budget_score * 0.4) + (task_score * 0.6)
            candidates.append((total_score, config))

        if not candidates:
            # Fallback to local model
            return self.MODELS_CONFIG["local/ollama"]

        # Return highest scoring model
        candidates.sort(key=lambda x: x[0], reverse=True)
        return candidates[0][1]

    def _calculate_budget_score(self, config: ModelConfig, budget_tier: str) -> float:
        """Calculate budget compatibility score."""
        tier_preferences = {
            "economy": {"fast": 1.0, "balanced": 0.7, "premium": 0.3},
            "balanced": {"fast": 0.8, "balanced": 1.0, "premium": 0.8},
            "premium": {"fast": 0.5, "balanced": 0.8, "premium": 1.0}
        }
        return tier_preferences[budget_tier][config.performance_tier]

    def _calculate_task_score(self, config: ModelConfig, task_type: TaskType) -> float:
        """Calculate task compatibility score."""
        if task_type in config.best_for:
            return 1.0
        else:
            return 0.5

# Global model selector instance
model_selector = ModelSelector()
```

## OpenRouter Service Implementation

### Core OpenRouter Service

```python
# src/services/openrouter_service.py
import os
import aiohttp
import asyncio
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from openai import AsyncOpenAI
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class OpenRouterService:
    """OpenRouter LLM service with fallback and optimization."""

    def __init__(self):
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.site_url = os.getenv("SITE_URL", "https://ai-coding-agent.local")
        self.app_name = os.getenv("APP_NAME", "AI Coding Agent")

        # Initialize OpenAI client with OpenRouter endpoint
        self.client = AsyncOpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=self.api_key,
        )

        # Rate limiting and cost tracking
        self.request_count = 0
        self.total_cost = 0.0
        self.daily_cost_limit = float(os.getenv("DAILY_COST_LIMIT", "10.0"))

        # Model cache and health status
        self.available_models = {}
        self.model_health = {}
        self.last_models_update = None

    async def initialize(self):
        """Initialize service and fetch available models."""
        await self.refresh_models()
        logger.info("OpenRouter service initialized")

    async def refresh_models(self):
        """Refresh available models from OpenRouter API."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://openrouter.ai/api/v1/models",
                    headers={"Authorization": f"Bearer {self.api_key}"}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.available_models = {
                            model["id"]: model for model in data["data"]
                        }
                        self.last_models_update = datetime.utcnow()
                        logger.info(f"Refreshed {len(self.available_models)} models")
        except Exception as e:
            logger.error(f"Failed to refresh models: {e}")

    async def generate_completion(
        self,
        messages: List[Dict[str, str]],
        model_id: Optional[str] = None,
        task_type: str = "code_generation",
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate completion with automatic model selection."""

        # Select optimal model if not specified
        if not model_id:
            from .model_selector import model_selector, TaskType
            task_enum = TaskType(task_type) if task_type in [t.value for t in TaskType] else TaskType.CODE_GENERATION
            selected_model = model_selector.select_model(task_enum)
            model_id = selected_model.id

        # Check daily cost limit
        if self.total_cost > self.daily_cost_limit:
            logger.warning("Daily cost limit exceeded, using local fallback")
            return await self._fallback_to_local(messages, **kwargs)

        try:
            extra_headers = {
                "HTTP-Referer": self.site_url,
                "X-Title": self.app_name,
            }

            if stream:
                return await self._stream_completion(
                    messages, model_id, extra_headers, **kwargs
                )
            else:
                return await self._non_stream_completion(
                    messages, model_id, extra_headers, **kwargs
                )

        except Exception as e:
            logger.error(f"OpenRouter request failed: {e}")
            return await self._fallback_to_local(messages, **kwargs)

    async def _non_stream_completion(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        extra_headers: Dict[str, str],
        **kwargs
    ) -> Dict[str, Any]:
        """Non-streaming completion."""

        response = await self.client.chat.completions.create(
            extra_headers=extra_headers,
            model=model_id,
            messages=messages,
            max_tokens=kwargs.get("max_tokens", 2000),
            temperature=kwargs.get("temperature", 0.7),
            top_p=kwargs.get("top_p", 1.0),
            stream=False
        )

        # Track usage and cost
        usage = response.usage
        cost = self._calculate_cost(model_id, usage.prompt_tokens, usage.completion_tokens)
        self.total_cost += cost
        self.request_count += 1

        return {
            "content": response.choices[0].message.content,
            "model": model_id,
            "usage": {
                "prompt_tokens": usage.prompt_tokens,
                "completion_tokens": usage.completion_tokens,
                "total_tokens": usage.total_tokens
            },
            "cost": cost,
            "timestamp": datetime.utcnow().isoformat()
        }

    async def _stream_completion(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        extra_headers: Dict[str, str],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Streaming completion."""

        stream = await self.client.chat.completions.create(
            extra_headers=extra_headers,
            model=model_id,
            messages=messages,
            max_tokens=kwargs.get("max_tokens", 2000),
            temperature=kwargs.get("temperature", 0.7),
            stream=True
        )

        async for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content

    def _calculate_cost(self, model_id: str, prompt_tokens: int, completion_tokens: int) -> float:
        """Calculate cost for API call."""
        if model_id not in self.available_models:
            return 0.0

        model_info = self.available_models[model_id]
        pricing = model_info.get("pricing", {})

        prompt_cost = float(pricing.get("prompt", "0")) * prompt_tokens
        completion_cost = float(pricing.get("completion", "0")) * completion_tokens

        return prompt_cost + completion_cost

    async def _fallback_to_local(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Fallback to local Ollama service."""
        from .ollama_service import OllamaService

        ollama = OllamaService()
        return await ollama.generate_completion(messages, **kwargs)

    async def get_model_health(self) -> Dict[str, Any]:
        """Get health status of models."""
        return {
            "available_models": len(self.available_models),
            "total_requests": self.request_count,
            "total_cost": self.total_cost,
            "daily_limit": self.daily_cost_limit,
            "last_update": self.last_models_update.isoformat() if self.last_models_update else None
        }

# Global OpenRouter service instance
openrouter_service = OpenRouterService()
```

### Cost Optimization Service

```python
# src/services/cost_optimizer.py
from typing import Dict, List, Tuple
from datetime import datetime, timedelta
import asyncio

class CostOptimizer:
    """Optimize LLM costs through smart routing and caching."""

    def __init__(self):
        self.cost_history = []
        self.model_performance = {}
        self.cache_hit_rate = 0.0

    def analyze_cost_effectiveness(
        self,
        model_id: str,
        task_type: str,
        quality_score: float,
        cost: float,
        response_time: float
    ) -> float:
        """Analyze cost-effectiveness of model for task."""

        # Calculate cost per quality point
        cost_per_quality = cost / max(quality_score, 0.1)

        # Factor in response time
        time_penalty = max(0, (response_time - 5.0) * 0.1)  # Penalty for slow responses

        # Historical performance
        historical_score = self.model_performance.get(f"{model_id}:{task_type}", 0.5)

        effectiveness = (quality_score * historical_score) / (cost_per_quality + time_penalty)

        # Update performance tracking
        self._update_performance(model_id, task_type, effectiveness)

        return effectiveness

    def recommend_model_for_budget(
        self,
        task_type: str,
        remaining_budget: float,
        required_quality: float = 0.7
    ) -> str:
        """Recommend model that fits budget and quality requirements."""

        candidates = []

        for model_config in model_selector.MODELS_CONFIG.values():
            if task_type in [t.value for t in model_config.best_for]:
                estimated_cost = self._estimate_cost(model_config.id, task_type)

                if estimated_cost <= remaining_budget:
                    historical_quality = self._get_historical_quality(model_config.id, task_type)

                    if historical_quality >= required_quality:
                        candidates.append((
                            historical_quality / estimated_cost,  # Quality per dollar
                            model_config.id
                        ))

        if candidates:
            candidates.sort(reverse=True)
            return candidates[0][1]

        # Fallback to local model
        return "local/ollama"

    def _estimate_cost(self, model_id: str, task_type: str) -> float:
        """Estimate cost for model and task."""
        # Average token usage by task type
        task_tokens = {
            "code_generation": 1500,
            "code_review": 800,
            "documentation": 600,
            "syntax_validation": 300
        }

        tokens = task_tokens.get(task_type, 1000)
        model_config = model_selector.MODELS_CONFIG.get(model_id)

        if model_config:
            return model_config.cost_per_1k_tokens * (tokens / 1000)

        return 0.01  # Default estimate

    def _get_historical_quality(self, model_id: str, task_type: str) -> float:
        """Get historical quality score for model and task."""
        key = f"{model_id}:{task_type}"
        return self.model_performance.get(key, 0.7)  # Default quality

    def _update_performance(self, model_id: str, task_type: str, score: float):
        """Update performance tracking."""
        key = f"{model_id}:{task_type}"
        current = self.model_performance.get(key, 0.5)
        # Weighted average with recent bias
        self.model_performance[key] = (current * 0.8) + (score * 0.2)

# Global cost optimizer
cost_optimizer = CostOptimizer()
```

## FastAPI Integration

### Updated LLM Endpoints

```python
# src/api/llm_endpoints.py
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from typing import Dict, Any, List, Optional
from pydantic import BaseModel

from ..services.openrouter_service import openrouter_service
from ..services.cost_optimizer import cost_optimizer
from ..services.auth_service import get_current_user

router = APIRouter(prefix="/api/v1/llm", tags=["LLM"])

class CompletionRequest(BaseModel):
    messages: List[Dict[str, str]]
    task_type: str = "code_generation"
    model_id: Optional[str] = None
    stream: bool = False
    max_tokens: int = 2000
    temperature: float = 0.7
    budget_tier: str = "balanced"

class CodeGenerationRequest(BaseModel):
    prompt: str
    language: str = "python"
    context: Optional[str] = None
    style_preferences: Dict[str, Any] = {}
    stream: bool = False

@router.post("/completion")
async def create_completion(
    request: CompletionRequest,
    current_user = Depends(get_current_user)
):
    """Create completion using OpenRouter with cost optimization."""

    try:
        start_time = datetime.utcnow()

        result = await openrouter_service.generate_completion(
            messages=request.messages,
            model_id=request.model_id,
            task_type=request.task_type,
            stream=request.stream,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )

        end_time = datetime.utcnow()
        response_time = (end_time - start_time).total_seconds()

        # Track cost effectiveness (simplified quality score)
        quality_score = len(result["content"]) / request.max_tokens  # Basic quality metric

        cost_optimizer.analyze_cost_effectiveness(
            model_id=result["model"],
            task_type=request.task_type,
            quality_score=quality_score,
            cost=result["cost"],
            response_time=response_time
        )

        return {
            "completion": result,
            "performance": {
                "response_time": response_time,
                "quality_score": quality_score,
                "cost_effectiveness": quality_score / max(result["cost"], 0.001)
            }
        }

    except Exception as e:
        logger.error(f"Completion failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate-code")
async def generate_code(
    request: CodeGenerationRequest,
    current_user = Depends(get_current_user)
):
    """Generate code with RAG context and optimization."""

    # Retrieve context from RAG if needed
    context = request.context or ""
    if not context:
        # Use RAG service to get relevant context
        from ..services.rag_service import RAGService
        rag = RAGService()
        context_result = await rag.get_relevant_context(
            prompt=request.prompt,
            project_id=current_user.get("current_project"),
            user_jwt=current_user.get("access_token")
        )
        context = context_result.get("formatted_context", "")

    # Build enhanced prompt
    enhanced_prompt = f"""
    Context from codebase:
    {context}

    Task: Generate {request.language} code
    Requirements: {request.prompt}
    Style Preferences: {request.style_preferences}

    Generate clean, well-documented code following the patterns from the context.
    """

    messages = [
        {"role": "system", "content": f"You are an expert {request.language} developer."},
        {"role": "user", "content": enhanced_prompt}
    ]

    if request.stream:
        async def generate():
            async for chunk in openrouter_service._stream_completion(
                messages=messages,
                model_id=None,  # Auto-select
                extra_headers={
                    "HTTP-Referer": openrouter_service.site_url,
                    "X-Title": openrouter_service.app_name
                },
                max_tokens=request.max_tokens if hasattr(request, 'max_tokens') else 2000
            ):
                yield f"data: {json.dumps({'content': chunk})}\n\n"
            yield "data: [DONE]\n\n"

        return StreamingResponse(generate(), media_type="text/event-stream")
    else:
        result = await openrouter_service.generate_completion(
            messages=messages,
            task_type="code_generation",
            stream=False
        )

        return {
            "generated_code": result["content"],
            "model_used": result["model"],
            "usage": result["usage"],
            "cost": result["cost"],
            "context_used": bool(context)
        }

@router.get("/models")
async def get_available_models(current_user = Depends(get_current_user)):
    """Get available models and their capabilities."""

    await openrouter_service.refresh_models()

    models_info = []
    for model_id, model_data in openrouter_service.available_models.items():
        models_info.append({
            "id": model_id,
            "name": model_data.get("name", model_id),
            "context_length": model_data.get("context_length", 0),
            "pricing": model_data.get("pricing", {}),
            "capabilities": model_data.get("architecture", {}).get("input_modalities", [])
        })

    return {"models": models_info}

@router.get("/cost-analysis")
async def get_cost_analysis(current_user = Depends(get_current_user)):
    """Get cost analysis and optimization recommendations."""

    health = await openrouter_service.get_model_health()

    return {
        "current_usage": health,
        "optimization_recommendations": {
            "cache_hit_rate": cost_optimizer.cache_hit_rate,
            "model_performance": cost_optimizer.model_performance,
            "budget_recommendations": [
                "Consider using more cost-effective models for simple tasks",
                "Enable response caching for repeated queries",
                "Use local models for privacy-sensitive operations"
            ]
        }
    }
```

## Environment Configuration

### OpenRouter Environment Variables

```bash
# .env additions for OpenRouter
# OpenRouter Configuration
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
SITE_URL=https://ai-coding-agent.local
APP_NAME=AI Coding Agent

# Cost Management
DAILY_COST_LIMIT=10.0
MONTHLY_COST_LIMIT=200.0
COST_ALERT_THRESHOLD=8.0

# Model Preferences
DEFAULT_CODING_MODEL=deepseek/deepseek-coder
DEFAULT_REVIEW_MODEL=anthropic/claude-3.5-sonnet
DEFAULT_FAST_MODEL=google/gemini-flash-1.5
FALLBACK_MODEL=local/ollama

# Performance Settings
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=30
RETRY_ATTEMPTS=3
```

## Key Takeaways

1. **Unified API Access**: Single interface to 400+ models from multiple providers
2. **Cost Optimization**: Automatic model selection based on budget and quality requirements
3. **Reliability**: Built-in fallbacks to local Ollama when needed
4. **Streaming Support**: Real-time code generation with progress updates
5. **Task-Specific Models**: Specialized models for different coding tasks
6. **Performance Tracking**: Cost-effectiveness analysis and optimization
7. **Enterprise Features**: Rate limiting, cost controls, and usage analytics
8. **Seamless Integration**: Drop-in replacement for existing LLM service
9. **Model Diversity**: Access to latest models without vendor lock-in
10. **Production Ready**: Monitoring, error handling, and scalability

OpenRouter transforms your AI Coding Agent into a powerful, cost-effective platform with access to the best models for each specific task while maintaining fallback capabilities and cost control.