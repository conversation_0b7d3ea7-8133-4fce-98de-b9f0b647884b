name: Deploy to Environments

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        default: "staging"
        type: choice
        options:
          - staging
          - production

jobs:
  deploy-staging:
    name: Deploy to Staging
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy to Staging
        run: |
          echo "Deploying to staging environment..."
          # Add deployment commands here
          # Example: docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d

      - name: Run health checks
        run: |
          # Add health check commands
          curl -f http://staging.ai-coding-agent.com/health || exit 1

  deploy-production:
    name: Deploy to Production
    if: github.event.inputs.environment == 'production'
    runs-on: ubuntu-latest
    environment: production
    needs: deploy-staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy to Production
        run: |
          echo "Deploying to production environment..."
          # Add deployment commands here
          # Example: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

      - name: Run health checks
        run: |
          # Add health check commands
          curl -f http://ai-coding-agent.com/health || exit 1
