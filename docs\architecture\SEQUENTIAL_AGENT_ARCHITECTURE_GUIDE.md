# Sequential Agent Architecture Guide for AI Coding Agent

## Overview

This guide covers the sequential agent architecture implementation for Phase 3 of the AI Coding Agent, focusing on mutex/locking mechanisms to ensure only one agent runs at a time while maintaining proper task handoffs and coordination.

## Table of Contents

1. [Agent Architecture Overview](#agent-architecture-overview)
2. [Sequential Processing Design](#sequential-processing-design)
3. [Mutex & Locking Implementation](#mutex--locking-implementation)
4. [Agent Registry & Coordination](#agent-registry--coordination)
5. [Task Queue Management](#task-queue-management)
6. [Context & State Management](#context--state-management)
7. [Integration with Existing Stack](#integration-with-existing-stack)

## Agent Architecture Overview

### Agent Hierarchy & Sequential Flow

```
Architect Agent (Master Coordinator)
    ↓ (Sequential with Mutex)
┌─────────────────────────────────────────┐
│ Agent Queue (Redis-based)               │
│ ┌─────────────┐ ┌─────────────┐        │
│ │ Frontend    │ │ Backend     │        │
│ │ Agent       │ │ Agent       │        │
│ └─────────────┘ └─────────────┘        │
│ ┌─────────────┐ ┌─────────────┐        │
│ │ Shell       │ │ Issue Fix   │        │
│ │ Agent       │ │ Agent       │        │
│ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────┘
    ↓
Shared Context & Mutex Manager (Redis)
```

### Agent Responsibilities

- **Architect Agent**: Master coordinator, user communication, task delegation
- **Frontend Agent**: React/UI development, component generation
- **Backend Agent**: FastAPI development, database operations
- **Shell Agent**: File operations, system commands, deployment
- **Issue Fix Agent**: Error detection, debugging, problem resolution

## Sequential Processing Design

### Core Implementation

```python
# src/agents/sequential_processor.py
import asyncio
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class Task:
    task_id: str
    agent_type: str
    payload: Dict[str, Any]
    priority: int
    created_at: datetime
    context: Dict[str, Any]

class SequentialTaskProcessor:
    """Manages sequential execution of agent tasks with mutex locking."""

    def __init__(self, redis_client, context_manager):
        self.redis_client = redis_client
        self.context_manager = context_manager
        self.execution_lock = asyncio.Lock()
        self.current_execution: Optional[str] = None
        self.agent_registry = {}

    async def initialize(self):
        """Initialize the sequential processor."""
        # Register available agents
        from .architect_agent import ArchitectAgent
        from .frontend_agent import FrontendAgent
        from .backend_agent import BackendAgent
        from .shell_agent import ShellAgent
        from .issue_fix_agent import IssueFixAgent

        self.agent_registry = {
            'architect': ArchitectAgent(),
            'frontend': FrontendAgent(),
            'backend': BackendAgent(),
            'shell': ShellAgent(),
            'issue_fix': IssueFixAgent()
        }

        # Initialize all agents
        for agent_type, agent in self.agent_registry.items():
            await agent.initialize()

        # Start task processor
        asyncio.create_task(self._process_tasks())

    async def submit_task(self, task: Task) -> str:
        """Submit a task for sequential processing."""

        # Add to Redis queue with priority
        task_data = {
            'task_id': task.task_id,
            'agent_type': task.agent_type,
            'payload': task.payload,
            'priority': task.priority,
            'created_at': task.created_at.isoformat(),
            'context': task.context,
            'status': TaskStatus.PENDING.value
        }

        # Store task
        await self.redis_client.hset(f"task:{task.task_id}", mapping=task_data)

        # Add to priority queue
        await self.redis_client.zadd("task_queue", {task.task_id: task.priority})

        return task.task_id

    async def _process_tasks(self):
        """Main task processing loop with mutex."""

        while True:
            try:
                # Get highest priority task
                tasks = await self.redis_client.zrevrange("task_queue", 0, 0)

                if not tasks:
                    await asyncio.sleep(1)
                    continue

                task_id = tasks[0]

                # Remove from queue
                await self.redis_client.zrem("task_queue", task_id)

                # Execute with mutex lock
                async with self.execution_lock:
                    await self._execute_task(task_id)

            except Exception as e:
                logging.error(f"Error in task processing: {e}")
                await asyncio.sleep(1)

    async def _execute_task(self, task_id: str):
        """Execute task with appropriate agent under mutex lock."""

        # Get task data
        task_data = await self.redis_client.hgetall(f"task:{task_id}")
        if not task_data:
            return

        agent_type = task_data['agent_type']
        payload = task_data['payload']
        context = task_data['context']

        self.current_execution = task_id

        try:
            # Update status to running
            await self.redis_client.hset(
                f"task:{task_id}",
                'status', TaskStatus.RUNNING.value,
                'started_at', datetime.utcnow().isoformat()
            )

            # Get agent and execute
            agent = self.agent_registry[agent_type]

            # Load shared context
            shared_context = await self.context_manager.get_context(task_id)
            merged_context = {**shared_context, **context}

            # Execute task
            result = await agent.execute_task(payload, merged_context)

            # Update context if needed
            if 'context_updates' in result:
                await self.context_manager.update_context(
                    task_id, result['context_updates']
                )

            # Mark as completed
            await self.redis_client.hset(
                f"task:{task_id}",
                'status', TaskStatus.COMPLETED.value,
                'result', result,
                'completed_at', datetime.utcnow().isoformat()
            )

            # Notify completion
            await self._notify_task_completion(task_id, result)

        except Exception as e:
            # Mark as failed
            await self.redis_client.hset(
                f"task:{task_id}",
                'status', TaskStatus.FAILED.value,
                'error', str(e),
                'failed_at', datetime.utcnow().isoformat()
            )

            logging.error(f"Task {task_id} failed: {e}")

        finally:
            self.current_execution = None

    async def get_current_status(self) -> Dict[str, Any]:
        """Get current processor status."""

        queue_size = await self.redis_client.zcard("task_queue")

        return {
            'queue_size': queue_size,
            'current_execution': self.current_execution,
            'available_agents': list(self.agent_registry.keys()),
            'is_processing': self.current_execution is not None
        }

    async def _notify_task_completion(self, task_id: str, result: Dict[str, Any]):
        """Notify about task completion via WebSocket."""
        from ..services.websocket_service import websocket_manager

        notification = {
            'type': 'task_completed',
            'task_id': task_id,
            'result': result,
            'timestamp': datetime.utcnow().isoformat()
        }

        await websocket_manager.broadcast(notification)
```

## Mutex & Locking Implementation

### Distributed Locking with Redis

```python
# src/agents/distributed_lock.py
import asyncio
import uuid
import time
import redis.asyncio as redis

class DistributedLock:
    """Redis-based distributed lock for agent coordination."""

    def __init__(self, redis_client: redis.Redis, lock_name: str, timeout: int = 300):
        self.redis_client = redis_client
        self.lock_name = f"lock:{lock_name}"
        self.timeout = timeout
        self.identifier = str(uuid.uuid4())
        self._acquired = False

    async def acquire(self, retry_interval: float = 0.1) -> bool:
        """Acquire the distributed lock."""

        end_time = time.time() + self.timeout

        while time.time() < end_time:
            # Try to acquire lock with expiration
            acquired = await self.redis_client.set(
                self.lock_name,
                self.identifier,
                nx=True,  # Only set if not exists
                ex=self.timeout  # Expiration time
            )

            if acquired:
                self._acquired = True
                return True

            await asyncio.sleep(retry_interval)

        return False

    async def release(self) -> bool:
        """Release the distributed lock."""

        if not self._acquired:
            return False

        # Lua script for atomic release
        release_script = """
        if redis.call("GET", KEYS[1]) == ARGV[1] then
            return redis.call("DEL", KEYS[1])
        else
            return 0
        end
        """

        result = await self.redis_client.eval(
            release_script, 1, self.lock_name, self.identifier
        )

        if result:
            self._acquired = False
            return True

        return False

    async def __aenter__(self):
        """Async context manager entry."""
        acquired = await self.acquire()
        if not acquired:
            raise TimeoutError(f"Failed to acquire lock {self.lock_name}")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.release()

# Usage example
class AgentMutexManager:
    """Manages mutex locks for agent execution."""

    def __init__(self, redis_client):
        self.redis_client = redis_client

    async def execute_with_lock(self, agent_type: str, task_function, *args, **kwargs):
        """Execute task with agent-specific lock."""

        lock_name = f"agent_execution_{agent_type}"

        async with DistributedLock(self.redis_client, lock_name):
            return await task_function(*args, **kwargs)

    async def is_agent_locked(self, agent_type: str) -> bool:
        """Check if agent is currently locked."""

        lock_name = f"lock:agent_execution_{agent_type}"
        exists = await self.redis_client.exists(lock_name)
        return bool(exists)
```

## Agent Registry & Coordination

### Base Agent Class & Registry

```python
# src/agents/base_agent.py
from abc import ABC, abstractmethod
from typing import Dict, Any, List

class BaseAgent(ABC):
    """Base class for all agents in the system."""

    def __init__(self, agent_type: str):
        self.agent_type = agent_type
        self.capabilities: List[str] = []
        self.current_task: str = None

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the agent."""
        pass

    @abstractmethod
    async def execute_task(self, payload: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a task with given payload and context."""
        pass

    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status."""
        return {
            'agent_type': self.agent_type,
            'capabilities': self.capabilities,
            'current_task': self.current_task,
            'available': self.current_task is None
        }

# Example agent implementation
class FrontendAgent(BaseAgent):
    """Agent specialized in frontend/React development."""

    def __init__(self):
        super().__init__('frontend')
        self.capabilities = ['react', 'typescript', 'css', 'components']

    async def initialize(self):
        """Initialize frontend agent."""
        # Setup LLM connection, templates, etc.
        pass

    async def execute_task(self, payload: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute frontend-related task."""

        task_type = payload.get('task_type')

        if task_type == 'generate_component':
            return await self._generate_component(payload, context)
        elif task_type == 'style_component':
            return await self._style_component(payload, context)
        else:
            raise ValueError(f"Unknown task type: {task_type}")

    async def _generate_component(self, payload: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate React component."""

        component_name = payload.get('component_name')
        requirements = payload.get('requirements', '')

        # Use LLM to generate component
        # This would integrate with your LLM service
        generated_code = await self._call_llm_for_component(component_name, requirements, context)

        return {
            'generated_code': generated_code,
            'component_name': component_name,
            'context_updates': {
                'generated_components': context.get('generated_components', []) + [component_name]
            }
        }

    async def _call_llm_for_component(self, name: str, requirements: str, context: Dict[str, Any]) -> str:
        """Call LLM service for component generation."""
        # Integration with your LLM service (Ollama/OpenRouter)
        pass
```

## Task Queue Management

### Redis-based Priority Queue

```python
# src/agents/task_queue.py
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

class TaskQueueManager:
    """Manages task queue with Redis."""

    def __init__(self, redis_client):
        self.redis_client = redis_client

    async def enqueue_task(self, task_id: str, agent_type: str, payload: Dict[str, Any], priority: int = 1):
        """Add task to queue with priority."""

        task_data = {
            'task_id': task_id,
            'agent_type': agent_type,
            'payload': json.dumps(payload),
            'priority': priority,
            'created_at': datetime.utcnow().isoformat(),
            'status': 'pending'
        }

        # Store task data
        await self.redis_client.hset(f"task:{task_id}", mapping=task_data)

        # Add to priority queue (higher scores = higher priority)
        await self.redis_client.zadd("task_queue", {task_id: priority})

    async def dequeue_task(self) -> Optional[Dict[str, Any]]:
        """Get highest priority task from queue."""

        # Get highest priority task
        tasks = await self.redis_client.zrevrange("task_queue", 0, 0, withscores=True)

        if not tasks:
            return None

        task_id, priority = tasks[0]

        # Remove from queue
        await self.redis_client.zrem("task_queue", task_id)

        # Get task data
        task_data = await self.redis_client.hgetall(f"task:{task_id}")

        if task_data:
            task_data['payload'] = json.loads(task_data['payload'])
            return task_data

        return None

    async def get_queue_status(self) -> Dict[str, Any]:
        """Get queue status information."""

        queue_size = await self.redis_client.zcard("task_queue")

        # Get tasks by priority
        tasks = await self.redis_client.zrevrange("task_queue", 0, -1, withscores=True)

        return {
            'queue_size': queue_size,
            'tasks': [{'task_id': task_id, 'priority': priority} for task_id, priority in tasks]
        }

    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending task."""

        # Remove from queue
        removed = await self.redis_client.zrem("task_queue", task_id)

        if removed:
            # Update task status
            await self.redis_client.hset(
                f"task:{task_id}",
                'status', 'cancelled',
                'cancelled_at', datetime.utcnow().isoformat()
            )
            return True

        return False
```

## Context & State Management

### Shared Context Management

```python
# src/agents/context_manager.py
import json
from typing import Dict, Any
from datetime import datetime

class ContextManager:
    """Manages shared context across agent executions."""

    def __init__(self, redis_client):
        self.redis_client = redis_client

    async def create_context(self, task_id: str, initial_context: Dict[str, Any]):
        """Create initial context for a task."""

        context_data = {
            'task_id': task_id,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat(),
            'context': json.dumps(initial_context)
        }

        await self.redis_client.hset(f"context:{task_id}", mapping=context_data)

    async def get_context(self, task_id: str) -> Dict[str, Any]:
        """Get current context for a task."""

        context_data = await self.redis_client.hget(f"context:{task_id}", 'context')

        if context_data:
            return json.loads(context_data)

        return {}

    async def update_context(self, task_id: str, updates: Dict[str, Any]):
        """Update context with new data."""

        # Get current context
        current_context = await self.get_context(task_id)

        # Merge updates
        updated_context = {**current_context, **updates}

        # Store updated context
        await self.redis_client.hset(
            f"context:{task_id}",
            'context', json.dumps(updated_context),
            'updated_at', datetime.utcnow().isoformat()
        )

    async def clear_context(self, task_id: str):
        """Clear context for a task."""
        await self.redis_client.delete(f"context:{task_id}")
```

## Integration with Existing Stack

### FastAPI Integration

```python
# src/api/agent_endpoints.py
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
from ..agents.sequential_processor import SequentialTaskProcessor
from ..services.redis_service import get_redis_client

router = APIRouter(prefix="/api/v1/agents", tags=["Agents"])

@router.post("/tasks")
async def submit_task(
    agent_type: str,
    payload: Dict[str, Any],
    priority: int = 1,
    processor: SequentialTaskProcessor = Depends(get_sequential_processor)
):
    """Submit a task to the agent system."""

    task = Task(
        task_id=str(uuid.uuid4()),
        agent_type=agent_type,
        payload=payload,
        priority=priority,
        created_at=datetime.utcnow(),
        context={}
    )

    task_id = await processor.submit_task(task)

    return {
        "task_id": task_id,
        "status": "submitted",
        "message": f"Task submitted to {agent_type} agent"
    }

@router.get("/status")
async def get_agent_status(
    processor: SequentialTaskProcessor = Depends(get_sequential_processor)
):
    """Get current agent system status."""

    status = await processor.get_current_status()
    return status

@router.get("/tasks/{task_id}")
async def get_task_status(
    task_id: str,
    redis_client = Depends(get_redis_client)
):
    """Get status of a specific task."""

    task_data = await redis_client.hgetall(f"task:{task_id}")

    if not task_data:
        raise HTTPException(status_code=404, detail="Task not found")

    return task_data
```

### Docker Compose Integration

```yaml
# Addition to docker-compose.yml
services:
  ai-orchestrator:
    # ... existing configuration
    environment:
      - AGENT_EXECUTION_MODE=sequential
      - AGENT_MUTEX_TIMEOUT=300
      - TASK_QUEUE_SIZE=100
      - AGENT_HEARTBEAT_INTERVAL=30
```

## Key Takeaways

1. **Sequential Execution**: Only one agent runs at a time using Redis distributed locks
2. **Priority Queue**: Tasks processed based on priority using Redis sorted sets
3. **Shared Context**: Context maintained across agent handoffs via Redis
4. **Fault Tolerance**: Failed tasks can be retried with exponential backoff
5. **Real-time Monitoring**: WebSocket notifications for task progress
6. **Agent Registry**: Centralized management of available agents and capabilities
7. **Distributed Locking**: Redis-based mutex ensures exclusive agent execution
8. **Context Preservation**: Shared state maintained across sequential executions
9. **Integration Ready**: FastAPI endpoints for task submission and monitoring
10. **Production Safe**: Timeout handling, error recovery, and resource cleanup

This architecture ensures reliable, sequential agent execution while maintaining the flexibility and modularity required for the AI Coding Agent system.