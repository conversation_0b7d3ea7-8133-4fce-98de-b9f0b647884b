-- =====================================================================================
-- Vector Search Functions with Row Level Security
-- Version: 002
-- Description: Advanced vector search functions that respect user permissions
-- =====================================================================================

-- =====================================================================================
-- VECTOR SIMILARITY SEARCH FUNCTIONS
-- =====================================================================================

-- Basic vector similarity search with RLS enforcement
CREATE OR REPLACE FUNCTION match_document_sections(
    query_embedding vector(384),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10
)
RETURNS TABLE(
    id uuid,
    document_id uuid,
    content text,
    similarity float,
    metadata jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- This function automatically respects RLS policies
    -- Only documents accessible to the current user will be returned
    RETURN QUERY
    SELECT
        ds.id,
        ds.document_id,
        ds.content,
        1 - (ds.embedding <=> query_embedding) AS similarity,
        ds.metadata
    FROM public.document_sections ds
    WHERE 1 - (ds.embedding <=> query_embedding) > match_threshold
    ORDER BY ds.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Advanced vector search with project filtering
CREATE OR REPLACE FUNCTION match_document_sections_by_project(
    query_embedding vector(384),
    project_id uuid,
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10
)
RETURNS TABLE(
    id uuid,
    document_id uuid,
    content text,
    similarity float,
    metadata jsonb,
    document_name text,
    file_type text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Search within a specific project, respecting RLS
    RETURN QUERY
    SELECT
        ds.id,
        ds.document_id,
        ds.content,
        1 - (ds.embedding <=> query_embedding) AS similarity,
        ds.metadata,
        d.name AS document_name,
        d.file_type
    FROM public.document_sections ds
    JOIN public.documents d ON ds.document_id = d.id
    WHERE
        d.project_id = match_document_sections_by_project.project_id
        AND 1 - (ds.embedding <=> query_embedding) > match_threshold
    ORDER BY ds.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Hybrid search: Vector + Full-text search
CREATE OR REPLACE FUNCTION hybrid_search_documents(
    query_text text,
    query_embedding vector(384),
    vector_threshold float DEFAULT 0.7,
    vector_weight float DEFAULT 0.7,
    text_weight float DEFAULT 0.3,
    match_count int DEFAULT 10
)
RETURNS TABLE(
    id uuid,
    document_id uuid,
    content text,
    vector_similarity float,
    text_rank float,
    combined_score float,
    metadata jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Combine vector similarity and full-text search
    RETURN QUERY
    SELECT
        ds.id,
        ds.document_id,
        ds.content,
        (1 - (ds.embedding <=> query_embedding)) AS vector_similarity,
        ts_rank_cd(to_tsvector('english', ds.content), plainto_tsquery('english', query_text)) AS text_rank,
        (
            vector_weight * (1 - (ds.embedding <=> query_embedding)) +
            text_weight * ts_rank_cd(to_tsvector('english', ds.content), plainto_tsquery('english', query_text))
        ) AS combined_score,
        ds.metadata
    FROM public.document_sections ds
    WHERE
        (1 - (ds.embedding <=> query_embedding)) > vector_threshold
        OR to_tsvector('english', ds.content) @@ plainto_tsquery('english', query_text)
    ORDER BY combined_score DESC
    LIMIT match_count;
END;
$$;

-- =====================================================================================
-- DOCUMENT PROCESSING FUNCTIONS
-- =====================================================================================

-- Function to add document sections with embeddings
CREATE OR REPLACE FUNCTION add_document_section(
    p_document_id uuid,
    p_content text,
    p_embedding vector(384),
    p_section_index int DEFAULT 0,
    p_metadata jsonb DEFAULT '{}'
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    section_id uuid;
BEGIN
    -- Insert new document section (RLS will enforce permissions)
    INSERT INTO public.document_sections (
        document_id,
        content,
        embedding,
        section_index,
        metadata
    ) VALUES (
        p_document_id,
        p_content,
        p_embedding,
        p_section_index,
        p_metadata
    ) RETURNING id INTO section_id;

    RETURN section_id;
END;
$$;

-- Function to update document embedding
CREATE OR REPLACE FUNCTION update_document_embedding(
    p_section_id uuid,
    p_embedding vector(384),
    p_metadata jsonb DEFAULT NULL
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Update existing document section (RLS will enforce permissions)
    UPDATE public.document_sections
    SET
        embedding = p_embedding,
        metadata = COALESCE(p_metadata, metadata),
        created_at = NOW()  -- Update timestamp for re-indexing
    WHERE id = p_section_id;

    RETURN FOUND;
END;
$$;

-- =====================================================================================
-- ANALYTICS AND STATISTICS FUNCTIONS
-- =====================================================================================

-- Get user's document statistics
CREATE OR REPLACE FUNCTION get_user_document_stats(user_id uuid DEFAULT auth.uid())
RETURNS TABLE(
    total_projects bigint,
    total_documents bigint,
    total_sections bigint,
    total_embeddings bigint,
    avg_sections_per_doc numeric,
    recent_documents bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM public.projects WHERE owner_id = user_id) AS total_projects,
        (SELECT COUNT(*) FROM public.documents d
         JOIN public.projects p ON d.project_id = p.id
         WHERE p.owner_id = user_id) AS total_documents,
        (SELECT COUNT(*) FROM public.document_sections ds
         JOIN public.documents d ON ds.document_id = d.id
         JOIN public.projects p ON d.project_id = p.id
         WHERE p.owner_id = user_id) AS total_sections,
        (SELECT COUNT(*) FROM public.document_sections ds
         JOIN public.documents d ON ds.document_id = d.id
         JOIN public.projects p ON d.project_id = p.id
         WHERE p.owner_id = user_id AND ds.embedding IS NOT NULL) AS total_embeddings,
        (SELECT ROUND(AVG(section_count), 2)
         FROM (
             SELECT COUNT(*) as section_count
             FROM public.document_sections ds
             JOIN public.documents d ON ds.document_id = d.id
             JOIN public.projects p ON d.project_id = p.id
             WHERE p.owner_id = user_id
             GROUP BY d.id
         ) sub) AS avg_sections_per_doc,
        (SELECT COUNT(*) FROM public.documents d
         JOIN public.projects p ON d.project_id = p.id
         WHERE p.owner_id = user_id AND d.created_at > NOW() - INTERVAL '7 days') AS recent_documents;
END;
$$;

-- =====================================================================================
-- UTILITY FUNCTIONS FOR VECTOR OPERATIONS
-- =====================================================================================

-- Calculate cosine similarity between two vectors
CREATE OR REPLACE FUNCTION cosine_similarity(
    vector1 vector(384),
    vector2 vector(384)
)
RETURNS float
LANGUAGE sql
IMMUTABLE STRICT
AS $$
    SELECT 1 - (vector1 <=> vector2);
$$;

-- Find similar document sections within a document
CREATE OR REPLACE FUNCTION find_similar_sections_in_document(
    document_id uuid,
    reference_section_id uuid,
    similarity_threshold float DEFAULT 0.8,
    max_results int DEFAULT 5
)
RETURNS TABLE(
    id uuid,
    content text,
    similarity float,
    section_index int
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    reference_embedding vector(384);
BEGIN
    -- Get the reference embedding
    SELECT embedding INTO reference_embedding
    FROM public.document_sections
    WHERE id = reference_section_id;

    IF reference_embedding IS NULL THEN
        RETURN;
    END IF;

    -- Find similar sections in the same document
    RETURN QUERY
    SELECT
        ds.id,
        ds.content,
        cosine_similarity(ds.embedding, reference_embedding) AS similarity,
        ds.section_index
    FROM public.document_sections ds
    WHERE
        ds.document_id = find_similar_sections_in_document.document_id
        AND ds.id != reference_section_id
        AND ds.embedding IS NOT NULL
        AND cosine_similarity(ds.embedding, reference_embedding) > similarity_threshold
    ORDER BY similarity DESC
    LIMIT max_results;
END;
$$;

-- =====================================================================================
-- CONTENT ANALYSIS FUNCTIONS
-- =====================================================================================

-- Get document content summary with vector clustering
CREATE OR REPLACE FUNCTION analyze_document_topics(
    p_document_id uuid,
    cluster_threshold float DEFAULT 0.85
)
RETURNS TABLE(
    cluster_id int,
    representative_content text,
    section_count bigint,
    avg_similarity float
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- This is a simplified clustering approach
    -- In production, you might want to use more sophisticated clustering algorithms
    RETURN QUERY
    WITH similarity_matrix AS (
        SELECT
            ds1.id as id1,
            ds2.id as id2,
            ds1.content as content1,
            ds2.content as content2,
            cosine_similarity(ds1.embedding, ds2.embedding) as similarity
        FROM public.document_sections ds1
        CROSS JOIN public.document_sections ds2
        WHERE
            ds1.document_id = p_document_id
            AND ds2.document_id = p_document_id
            AND ds1.id != ds2.id
            AND ds1.embedding IS NOT NULL
            AND ds2.embedding IS NOT NULL
    ),
    clusters AS (
        SELECT
            ROW_NUMBER() OVER (ORDER BY id1) as cluster_id,
            id1,
            content1,
            COUNT(*) as similar_sections,
            AVG(similarity) as avg_sim
        FROM similarity_matrix
        WHERE similarity > cluster_threshold
        GROUP BY id1, content1
    )
    SELECT
        cluster_id::int,
        LEFT(content1, 200) as representative_content,
        similar_sections,
        ROUND(avg_sim::numeric, 3)::float as avg_similarity
    FROM clusters
    ORDER BY similar_sections DESC, avg_similarity DESC;
END;
$$;

-- =====================================================================================
-- SEARCH OPTIMIZATION FUNCTIONS
-- =====================================================================================

-- Recompute vector index statistics (for performance optimization)
CREATE OR REPLACE FUNCTION refresh_vector_index_stats()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Refresh statistics for the vector index
    ANALYZE public.document_sections;

    -- Optionally rebuild the index if needed
    -- REINDEX INDEX idx_document_sections_embedding;

    RETURN true;
END;
$$;

-- =====================================================================================
-- GRANTS AND PERMISSIONS
-- =====================================================================================

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION match_document_sections(vector(384), float, int) TO authenticated;
GRANT EXECUTE ON FUNCTION match_document_sections_by_project(vector(384), uuid, float, int) TO authenticated;
GRANT EXECUTE ON FUNCTION hybrid_search_documents(text, vector(384), float, float, float, int) TO authenticated;
GRANT EXECUTE ON FUNCTION add_document_section(uuid, text, vector(384), int, jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION update_document_embedding(uuid, vector(384), jsonb) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_document_stats(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION cosine_similarity(vector(384), vector(384)) TO authenticated;
GRANT EXECUTE ON FUNCTION find_similar_sections_in_document(uuid, uuid, float, int) TO authenticated;
GRANT EXECUTE ON FUNCTION analyze_document_topics(uuid, float) TO authenticated;

-- Service role gets additional permissions
GRANT EXECUTE ON FUNCTION refresh_vector_index_stats() TO service_role;

-- =====================================================================================
-- FUNCTION COMMENTS
-- =====================================================================================

COMMENT ON FUNCTION match_document_sections IS 'Basic vector similarity search with RLS enforcement';
COMMENT ON FUNCTION match_document_sections_by_project IS 'Project-scoped vector search with document metadata';
COMMENT ON FUNCTION hybrid_search_documents IS 'Combined vector and full-text search with weighted scoring';
COMMENT ON FUNCTION add_document_section IS 'Add new document section with vector embedding';
COMMENT ON FUNCTION update_document_embedding IS 'Update existing document section embedding';
COMMENT ON FUNCTION get_user_document_stats IS 'Get comprehensive document statistics for a user';
COMMENT ON FUNCTION cosine_similarity IS 'Calculate cosine similarity between two vectors';
COMMENT ON FUNCTION find_similar_sections_in_document IS 'Find similar sections within the same document';
COMMENT ON FUNCTION analyze_document_topics IS 'Analyze document content using vector clustering';
COMMENT ON FUNCTION refresh_vector_index_stats IS 'Refresh vector index statistics for performance';

-- =====================================================================================
-- MIGRATION COMPLETE
-- =====================================================================================