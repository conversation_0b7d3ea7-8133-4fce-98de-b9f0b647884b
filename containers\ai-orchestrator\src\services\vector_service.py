"""
Vector Storage Service for AI Coding Agent with Supabase.

This module provides comprehensive vector storage and retrieval functionality
with permission-aware search, embedding management, and RAG capabilities
using pgvector and Supabase Row Level Security.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import hashlib
import json
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging
import numpy as np

# External imports
try:
    import openai
    from sentence_transformers import SentenceTransformer
except ImportError:
    openai = None
    SentenceTransformer = None

# Internal imports
from .supabase_service import SupabaseService
from .auth_service import UserProfile

# Configure logging
logger = logging.getLogger(__name__)


class EmbeddingProvider(str, Enum):
    """Supported embedding providers."""
    OPENAI = "openai"
    SENTENCE_TRANSFORMERS = "sentence_transformers"
    HUGGINGFACE = "huggingface"


@dataclass
class VectorConfig:
    """Configuration for vector storage service."""
    embedding_provider: EmbeddingProvider = EmbeddingProvider.SENTENCE_TRANSFORMERS
    embedding_model: str = "all-MiniLM-L6-v2"
    embedding_dimension: int = 384
    chunk_size: int = 512
    chunk_overlap: int = 50
    similarity_threshold: float = 0.7
    max_search_results: int = 10
    enable_hybrid_search: bool = True
    cache_embeddings: bool = True

    @classmethod
    def from_env(cls) -> 'VectorConfig':
        """Create configuration from centralized settings."""
        from ..core.config import settings

        return cls(
            embedding_provider=EmbeddingProvider(settings.EMBEDDING_PROVIDER.value),
            embedding_model=settings.EMBEDDING_MODEL,
            embedding_dimension=settings.EMBEDDING_DIMENSION,
            chunk_size=settings.CHUNK_SIZE,
            chunk_overlap=settings.CHUNK_OVERLAP,
            similarity_threshold=settings.SIMILARITY_THRESHOLD,
            max_search_results=settings.MAX_SEARCH_RESULTS,
            enable_hybrid_search=settings.ENABLE_HYBRID_SEARCH,
            cache_embeddings=settings.CACHE_EMBEDDINGS
        )


@dataclass
class DocumentChunk:
    """Document chunk with metadata."""
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    embedding: Optional[List[float]] = None
    chunk_index: int = 0

    def __post_init__(self):
        """Post-initialization processing."""
        if not self.metadata:
            self.metadata = {}

        # Add content hash for deduplication
        self.metadata['content_hash'] = hashlib.md5(
            self.content.encode('utf-8')
        ).hexdigest()


@dataclass
class SearchResult:
    """Vector search result."""
    id: str
    document_id: str
    content: str
    similarity: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    document_name: Optional[str] = None
    file_type: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'document_id': self.document_id,
            'content': self.content,
            'similarity': self.similarity,
            'metadata': self.metadata,
            'document_name': self.document_name,
            'file_type': self.file_type
        }


@dataclass
class DocumentStats:
    """Document processing statistics."""
    total_documents: int = 0
    total_chunks: int = 0
    total_embeddings: int = 0
    avg_chunks_per_document: float = 0.0
    storage_size_mb: float = 0.0
    last_updated: Optional[datetime] = None


class VectorStorageError(Exception):
    """Base exception for vector storage errors."""
    pass


class EmbeddingError(VectorStorageError):
    """Embedding generation errors."""
    pass


class SearchError(VectorStorageError):
    """Search operation errors."""
    pass


class VectorStorageService:
    """
    Comprehensive vector storage service providing:
    - Document chunking and embedding generation
    - Permission-aware vector search
    - Hybrid search (vector + full-text)
    - Document management with metadata
    - Performance optimization and caching
    """

    def __init__(
        self,
        supabase_service: SupabaseService,
        config: Optional[VectorConfig] = None
    ):
        """
        Initialize vector storage service.

        Args:
            supabase_service: Supabase service instance.
            config: Vector storage configuration.
        """
        self.supabase_service = supabase_service
        self.config = config or VectorConfig.from_env()
        self._embedding_model = None
        self._embedding_cache: Dict[str, List[float]] = {}

        logger.info(f"Vector storage service initialized with {self.config.embedding_provider}")

    async def initialize(self) -> None:
        """Initialize embedding model and dependencies."""
        try:
            await self._initialize_embedding_model()
            logger.info("Vector storage service initialization completed")
        except Exception as e:
            logger.error(f"Failed to initialize vector storage service: {str(e)}")
            raise VectorStorageError(f"Initialization failed: {str(e)}") from e

    async def _initialize_embedding_model(self) -> None:
        """Initialize the embedding model based on configuration."""
        if self.config.embedding_provider == EmbeddingProvider.SENTENCE_TRANSFORMERS:
            if SentenceTransformer is None:
                raise EmbeddingError(
                    "sentence-transformers not installed. "
                    "Install with: pip install sentence-transformers"
                )

            # Load model in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self._embedding_model = await loop.run_in_executor(
                None,
                lambda: SentenceTransformer(self.config.embedding_model) if SentenceTransformer else None
            )

        elif self.config.embedding_provider == EmbeddingProvider.OPENAI:
            if openai is None:
                raise EmbeddingError(
                    "openai not installed. Install with: pip install openai"
                )
            # OpenAI embeddings are called via API, no model loading needed

        else:
            raise EmbeddingError(f"Unsupported embedding provider: {self.config.embedding_provider}")

    # ==================================================================================
    # DOCUMENT PROCESSING
    # ==================================================================================

    async def process_document(
        self,
        document_id: str,
        content: str,
        user_id: str,
        metadata: Optional[Dict[str, Any]] = None,
        overwrite: bool = False
    ) -> List[str]:
        """
        Process document by chunking and generating embeddings.

        Args:
            document_id: Document identifier.
            content: Document content.
            user_id: User identifier for permissions.
            metadata: Additional metadata.
            overwrite: Whether to overwrite existing chunks.

        Returns:
            List of created chunk IDs.

        Raises:
            VectorStorageError: If processing fails.
        """
        try:
            logger.info(f"Processing document {document_id} for user {user_id}")

            # Check if document exists and user has permission
            if not await self._verify_document_permission(document_id, user_id):
                raise VectorStorageError("Document not found or access denied")

            # Remove existing chunks if overwriting
            if overwrite:
                await self._remove_document_chunks(document_id, user_id)

            # Chunk the document
            chunks = self._chunk_document(content, metadata or {})

            # Generate embeddings and store chunks
            chunk_ids = []
            for i, chunk in enumerate(chunks):
                try:
                    # Generate embedding
                    embedding = await self.generate_embedding(chunk.content)

                    # Store chunk with embedding
                    chunk_id = await self._store_document_chunk(
                        document_id=document_id,
                        content=chunk.content,
                        embedding=embedding,
                        section_index=i,
                        metadata=chunk.metadata,
                        user_id=user_id
                    )

                    chunk_ids.append(chunk_id)

                except Exception as e:
                    logger.error(f"Failed to process chunk {i}: {str(e)}")
                    # Continue with other chunks
                    continue

            logger.info(f"Processed {len(chunk_ids)} chunks for document {document_id}")
            return chunk_ids

        except Exception as e:
            logger.error(f"Document processing failed: {str(e)}")
            raise VectorStorageError(f"Document processing failed: {str(e)}") from e

    def _chunk_document(
        self,
        content: str,
        metadata: Dict[str, Any]
    ) -> List[DocumentChunk]:
        """Split document into chunks for processing."""
        chunks = []
        chunk_size = self.config.chunk_size
        chunk_overlap = self.config.chunk_overlap

        # Simple text chunking (can be enhanced with more sophisticated methods)
        words = content.split()

        for i in range(0, len(words), chunk_size - chunk_overlap):
            chunk_words = words[i:i + chunk_size]
            chunk_content = ' '.join(chunk_words)

            chunk_metadata = {
                **metadata,
                'chunk_index': len(chunks),
                'word_count': len(chunk_words),
                'start_word': i,
                'end_word': min(i + chunk_size, len(words))
            }

            chunks.append(DocumentChunk(
                content=chunk_content,
                metadata=chunk_metadata,
                chunk_index=len(chunks)
            ))

        return chunks

    # ==================================================================================
    # EMBEDDING GENERATION
    # ==================================================================================

    async def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for text.

        Args:
            text: Text to embed.

        Returns:
            Embedding vector.

        Raises:
            EmbeddingError: If embedding generation fails.
        """
        try:
            # Check cache first
            if self.config.cache_embeddings:
                cache_key = hashlib.md5(text.encode('utf-8')).hexdigest()
                if cache_key in self._embedding_cache:
                    return self._embedding_cache[cache_key]

            # Generate embedding based on provider
            if self.config.embedding_provider == EmbeddingProvider.SENTENCE_TRANSFORMERS:
                embedding = await self._generate_sentence_transformer_embedding(text)
            elif self.config.embedding_provider == EmbeddingProvider.OPENAI:
                embedding = await self._generate_openai_embedding(text)
            else:
                raise EmbeddingError(f"Unsupported provider: {self.config.embedding_provider}")

            # Cache the embedding
            if self.config.cache_embeddings:
                self._embedding_cache[cache_key] = embedding

            return embedding

        except Exception as e:
            logger.error(f"Embedding generation failed: {str(e)}")
            raise EmbeddingError(f"Embedding generation failed: {str(e)}") from e

    async def _generate_sentence_transformer_embedding(self, text: str) -> List[float]:
        """Generate embedding using Sentence Transformers."""
        if not self._embedding_model:
            raise EmbeddingError("Sentence Transformer model not initialized")

        loop = asyncio.get_event_loop()
        embedding = await loop.run_in_executor(
            None,
            lambda: self._embedding_model.encode(text).tolist()
        )

        return embedding

    async def _generate_openai_embedding(self, text: str) -> List[float]:
        """Generate embedding using OpenAI API."""
        try:
            # Run OpenAI API call in executor to avoid blocking
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: openai.embeddings.create(
                    model=self.config.embedding_model,
                    input=text
                )
            )
            return response.data[0].embedding
        except Exception as e:
            raise EmbeddingError(f"OpenAI embedding failed: {str(e)}") from e

    # ==================================================================================
    # VECTOR SEARCH
    # ==================================================================================

    async def search_documents(
        self,
        query: str,
        user_id: str,
        project_id: Optional[str] = None,
        similarity_threshold: Optional[float] = None,
        max_results: Optional[int] = None,
        include_metadata: bool = True
    ) -> List[SearchResult]:
        """
        Search documents using vector similarity.

        Args:
            query: Search query.
            user_id: User identifier for permissions.
            project_id: Optional project filter.
            similarity_threshold: Minimum similarity score.
            max_results: Maximum number of results.
            include_metadata: Whether to include metadata.

        Returns:
            List of search results.

        Raises:
            SearchError: If search fails.
        """
        try:
            # Generate query embedding
            query_embedding = await self.generate_embedding(query)

            # Set defaults
            threshold = similarity_threshold or self.config.similarity_threshold
            limit = max_results or self.config.max_search_results

            # Perform search
            if project_id:
                results = await self._search_by_project(
                    query_embedding, project_id, threshold, limit, user_id
                )
            else:
                results = await self._search_all_documents(
                    query_embedding, threshold, limit, user_id
                )

            # Convert to SearchResult objects
            search_results = []
            for result in results:
                search_result = SearchResult(
                    id=result['id'],
                    document_id=result['document_id'],
                    content=result['content'],
                    similarity=result['similarity'],
                    metadata=result.get('metadata', {}) if include_metadata else {},
                    document_name=result.get('document_name'),
                    file_type=result.get('file_type')
                )
                search_results.append(search_result)

            logger.info(f"Vector search returned {len(search_results)} results")
            return search_results

        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            raise SearchError(f"Vector search failed: {str(e)}") from e

    async def hybrid_search(
        self,
        query: str,
        user_id: str,
        project_id: Optional[str] = None,
        vector_weight: float = 0.7,
        text_weight: float = 0.3,
        similarity_threshold: Optional[float] = None,
        max_results: Optional[int] = None
    ) -> List[SearchResult]:
        """
        Perform hybrid search combining vector and full-text search.

        Args:
            query: Search query.
            user_id: User identifier for permissions.
            project_id: Optional project filter.
            vector_weight: Weight for vector similarity.
            text_weight: Weight for text search.
            similarity_threshold: Minimum similarity score.
            max_results: Maximum number of results.

        Returns:
            List of search results.
        """
        try:
            if not self.config.enable_hybrid_search:
                return await self.search_documents(
                    query, user_id, project_id, similarity_threshold, max_results
                )

            # Generate query embedding
            query_embedding = await self.generate_embedding(query)

            # Set defaults
            threshold = similarity_threshold or self.config.similarity_threshold
            limit = max_results or self.config.max_search_results

            # Perform hybrid search using database function
            results = await self._hybrid_search_database(
                query, query_embedding, threshold, vector_weight, text_weight, limit, user_id
            )

            # Convert to SearchResult objects
            search_results = []
            for result in results:
                search_result = SearchResult(
                    id=result['id'],
                    document_id=result['document_id'],
                    content=result['content'],
                    similarity=result['combined_score'],  # Use combined score as similarity
                    metadata=result.get('metadata', {})
                )
                search_results.append(search_result)

            logger.info(f"Hybrid search returned {len(search_results)} results")
            return search_results

        except Exception as e:
            logger.error(f"Hybrid search failed: {str(e)}")
            raise SearchError(f"Hybrid search failed: {str(e)}") from e

    # ==================================================================================
    # DATABASE OPERATIONS
    # ==================================================================================

    async def _store_document_chunk(
        self,
        document_id: str,
        content: str,
        embedding: List[float],
        section_index: int,
        metadata: Dict[str, Any],
        user_id: str
    ) -> str:
        """Store document chunk with embedding in database."""
        try:
            # Use the database function for RLS enforcement
            query = """
                SELECT add_document_section($1, $2, $3, $4, $5) as chunk_id
            """

            result = await self.supabase_service.execute_query(
                query,
                document_id,
                content,
                embedding,
                section_index,
                json.dumps(metadata),
                fetch_type='one'
            )

            if isinstance(result, dict) and 'chunk_id' in result:
                return result['chunk_id']
            else:
                raise VectorStorageError("Invalid response format from database")

        except Exception as e:
            logger.error(f"Failed to store document chunk: {str(e)}")
            raise VectorStorageError(f"Failed to store chunk: {str(e)}") from e

    async def _search_all_documents(
        self,
        query_embedding: List[float],
        threshold: float,
        limit: int,
        user_id: str
    ) -> List[Dict[str, Any]]:
        """Search all accessible documents."""
        query = """
            SELECT * FROM match_document_sections($1, $2, $3)
        """

        result = await self.supabase_service.execute_query(
            query,
            query_embedding,
            threshold,
            limit,
            fetch_type='all'
        )

        # Ensure we return a list
        if isinstance(result, list):
            return result
        elif isinstance(result, dict):
            return [result]
        else:
            return []

    async def _search_by_project(
        self,
        query_embedding: List[float],
        project_id: str,
        threshold: float,
        limit: int,
        user_id: str
    ) -> List[Dict[str, Any]]:
        """Search documents within a specific project."""
        query = """
            SELECT * FROM match_document_sections_by_project($1, $2, $3, $4)
        """

        result = await self.supabase_service.execute_query(
            query,
            query_embedding,
            project_id,
            threshold,
            limit,
            fetch_type='all'
        )

        # Ensure we return a list
        if isinstance(result, list):
            return result
        elif isinstance(result, dict):
            return [result]
        else:
            return []

    async def _hybrid_search_database(
        self,
        query_text: str,
        query_embedding: List[float],
        threshold: float,
        vector_weight: float,
        text_weight: float,
        limit: int,
        user_id: str
    ) -> List[Dict[str, Any]]:
        """Perform hybrid search using database function."""
        query = """
            SELECT * FROM hybrid_search_documents($1, $2, $3, $4, $5, $6)
        """

        result = await self.supabase_service.execute_query(
            query,
            query_text,
            query_embedding,
            threshold,
            vector_weight,
            text_weight,
            limit,
            fetch_type='all'
        )

        # Ensure we return a list
        if isinstance(result, list):
            return result
        elif isinstance(result, dict):
            return [result]
        else:
            return []

    async def _verify_document_permission(
        self,
        document_id: str,
        user_id: str
    ) -> bool:
        """Verify user has permission to access document."""
        try:
            query = """
                SELECT COUNT(*) as count
                FROM documents d
                JOIN projects p ON d.project_id = p.id
                WHERE d.id = $1 AND p.owner_id = $2
            """

            result = await self.supabase_service.execute_query(
                query,
                document_id,
                user_id,
                fetch_type='one'
            )

            if isinstance(result, dict) and 'count' in result:
                return result['count'] > 0
            else:
                return False

        except Exception:
            return False

    async def _remove_document_chunks(
        self,
        document_id: str,
        user_id: str
    ) -> None:
        """Remove all chunks for a document."""
        try:
            query = """
                DELETE FROM document_sections
                WHERE document_id = $1
                AND document_id IN (
                    SELECT d.id FROM documents d
                    JOIN projects p ON d.project_id = p.id
                    WHERE p.owner_id = $2
                )
            """

            await self.supabase_service.execute_query(
                query,
                document_id,
                user_id,
                fetch_type='val'
            )

        except Exception as e:
            logger.error(f"Failed to remove document chunks: {str(e)}")

    # ==================================================================================
    # STATISTICS AND MANAGEMENT
    # ==================================================================================

    async def get_user_stats(self, user_id: str) -> DocumentStats:
        """Get document statistics for user."""
        try:
            query = """
                SELECT * FROM get_user_document_stats($1)
            """

            result = await self.supabase_service.execute_query(
                query,
                user_id,
                fetch_type='one'
            )

            if isinstance(result, dict):
                return DocumentStats(
                    total_documents=result.get('total_documents', 0),
                    total_chunks=result.get('total_sections', 0),
                    total_embeddings=result.get('total_embeddings', 0),
                    avg_chunks_per_document=float(result.get('avg_sections_per_doc', 0) or 0),
                    last_updated=datetime.utcnow()
                )
            else:
                return DocumentStats()

        except Exception as e:
            logger.error(f"Failed to get user stats: {str(e)}")
            return DocumentStats()

    async def refresh_vector_index(self) -> bool:
        """Refresh vector index for better performance."""
        try:
            query = "SELECT refresh_vector_index_stats()"
            await self.supabase_service.execute_query(query, fetch_type='val')
            return True
        except Exception as e:
            logger.error(f"Failed to refresh vector index: {str(e)}")
            return False


# Global service instance
_vector_service: Optional[VectorStorageService] = None


async def get_vector_service() -> VectorStorageService:
    """Get global vector storage service instance."""
    global _vector_service

    if _vector_service is None:
        from .supabase_service import get_supabase_service
        supabase_service = await get_supabase_service()
        _vector_service = VectorStorageService(supabase_service)
        await _vector_service.initialize()

    return _vector_service