"""
Project Repository for AI Coding Agent.

This module provides the data access layer for project management operations,
implementing the Repository pattern to ensure clean separation between
business logic and data persistence.

Key Components:
- ProjectRepository: Main repository class with CRUD operations
- File system operations for project management
- Git repository operations
- FastAPI dependency injection support
"""

import json
import logging
import asyncio
import shutil
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

# SQLAlchemy imports for persistence layer
from sqlalchemy.orm import Session

# Internal imports
from ..core.config import settings
from ..utils.auth import get_current_user
from ..models.user import User
from ..models.project import Project, user_project_association

# FastAPI imports
from fastapi import HTTPException, status

# Configure logging
logger = logging.getLogger(__name__)


class ProjectRepositoryError(Exception):
    """Base exception for project repository operations."""
    pass


class ProjectNotFoundError(ProjectRepositoryError):
    """Exception raised when a project is not found."""
    pass


class ProjectAlreadyExistsError(ProjectRepositoryError):
    """Exception raised when trying to create a project that already exists."""
    pass


class ProjectRepository:
    """
    Project repository implementing the Repository pattern for data access.

    This class provides a clean interface between the application logic and the
    file system operations, specifically designed to work with FastAPI's dependency
    injection system.

    Features:
    - File system operations for project management
    - Git repository operations
    - Project listing and deletion
    - Error handling with custom exceptions
    """

    def __init__(self):
        """
        Initialize the ProjectRepository.

        The repository is designed to be stateless and receive user context
        through method parameters.
        """
        logger.info("ProjectRepository initialized")

    async def get_user_workspace_path(self, user_id: str) -> Path:
        """
        Get the user's workspace path.

        Args:
            user_id: The user's ID

        Returns:
            Path: The user's workspace directory path
        """
        user_dir = settings.WORKSPACE_PATH / f"user_{user_id}"
        return user_dir

    async def get_workspace_info(self, user_id: str) -> Dict[str, Any]:
        """
        Get workspace information for a user.

        Args:
            user_id: The user's ID

        Returns:
            Dict containing workspace information
        """
        try:
            workspace_path = settings.WORKSPACE_PATH
            user_dir = await self.get_user_workspace_path(user_id)

            return {
                "workspace_path": str(workspace_path),
                "user_workspace": str(user_dir),
                "workspace_exists": workspace_path.exists(),
                "user_workspace_exists": user_dir.exists()
            }
        except Exception as e:
            logger.error(f"Failed to get workspace info: {e}")
            raise ProjectRepositoryError(f"Failed to get workspace info: {str(e)}")

    async def upload_project_files(self, user_id: str, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Upload project files to user workspace.

        Args:
            user_id: The user's ID
            files: List of file data dictionaries with name, path, and content

        Returns:
            Dict containing upload results
        """
        try:
            if not files:
                raise ProjectRepositoryError("No files provided")

            # Create user directory if it doesn't exist
            user_dir = await self.get_user_workspace_path(user_id)
            user_dir.mkdir(parents=True, exist_ok=True)

            files_processed = 0

            # Process each file
            for file_data in files:
                try:
                    # Create directory structure
                    file_path = user_dir / file_data["path"]
                    file_path.parent.mkdir(parents=True, exist_ok=True)

                    # Write file content asynchronously
                    import aiofiles
                    async with aiofiles.open(file_path, "w", encoding="utf-8") as f:
                        await f.write(file_data["content"])

                    files_processed += 1
                    logger.info(f"Successfully uploaded file: {file_path}")

                except Exception as file_error:
                    logger.error(f"Failed to upload file {file_data['path']}: {file_error}")
                    # Continue with other files instead of failing completely
                    continue

            if files_processed == 0:
                raise ProjectRepositoryError("Failed to upload any files")

            return {
                "message": f"Successfully uploaded {files_processed} files",
                "path": str(user_dir),
                "files_processed": files_processed
            }

        except Exception as e:
            logger.error(f"Project upload failed: {e}")
            raise ProjectRepositoryError(f"Failed to upload project: {str(e)}")

    async def clone_git_repository(
        self,
        user_id: str,
        repository_url: str,
        target_directory: Optional[str] = None,
        branch: Optional[str] = None,
        db: Optional[Session] = None,
        user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Clone a Git repository to user workspace, and optionally persist a Project record.

        Args:
            user_id: The user's ID
            repository_url: URL of the Git repository to clone
            target_directory: Optional target directory name
            branch: Optional branch to checkout

        Returns:
            Dict containing clone results
        """
        try:
            user_dir = await self.get_user_workspace_path(user_id)
            user_dir.mkdir(parents=True, exist_ok=True)

            # Determine target directory
            if target_directory:
                # Validate target directory path
                if '..' in target_directory or target_directory.startswith('/'):
                    raise ProjectRepositoryError("Invalid target directory: path traversal not allowed")
                target_path = user_dir / target_directory
            else:
                # Extract repository name from URL
                repo_name = repository_url.rstrip('/').split('/')[-1]
                if repo_name.endswith('.git'):
                    repo_name = repo_name[:-4]
                target_path = user_dir / repo_name

            # Ensure target directory doesn't exist
            if target_path.exists():
                raise ProjectAlreadyExistsError(f"Directory '{target_path.name}' already exists")

            # Build git clone command
            git_command = ["git", "clone", repository_url, str(target_path)]

            if branch:
                git_command.extend(["--branch", branch])

            # Execute git clone
            process = await asyncio.create_subprocess_exec(
                *git_command,
                cwd=str(user_dir),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_message = stderr.decode() if stderr else "Git clone failed"
                logger.error(f"Git clone failed: {error_message}")
                raise ProjectRepositoryError(f"Failed to clone repository: {error_message}")

            logger.info(f"Successfully cloned {repository_url} to {target_path}")

            # Optionally persist a Project record and relationship
            if db is not None and user is not None:
                try:
                    project = Project(name=target_path.name, description=None)
                    db.add(project)
                    db.commit()
                    db.refresh(project)

                    # Link user to project (owner role)
                    db.execute(
                        user_project_association.insert().values(
                            user_id=user.id,
                            project_id=project.id,
                            role="owner"
                        )
                    )
                    db.commit()
                except Exception:
                    db.rollback()
                    # We won't fail the clone if DB persistence fails; log and continue
                    logger.exception("Project cloned but DB persistence failed")

            return {
                "message": "Successfully cloned repository",
                "path": str(target_path),
                "repository_url": repository_url
            }

        except Exception as e:
            logger.error(f"Repository cloning failed: {e}")
            raise ProjectRepositoryError(f"Failed to clone repository: {str(e)}")

    async def list_user_projects(self, user_id: str) -> List[Dict[str, Any]]:
        """
        List all projects in user workspace.

        Args:
            user_id: The user's ID

        Returns:
            List of project information dictionaries
        """
        try:
            user_dir = await self.get_user_workspace_path(user_id)

            if not user_dir.exists():
                return []

            projects = []
            for item in user_dir.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    project_info = {
                        "name": item.name,
                        "path": str(item.relative_to(user_dir)),
                        "is_git_repo": (item / ".git").exists(),
                        "created": item.stat().st_ctime
                    }
                    projects.append(project_info)

            # Sort by creation time (newest first)
            projects.sort(key=lambda x: x["created"], reverse=True)

            return projects

        except Exception as e:
            logger.error(f"Failed to list projects: {e}")
            raise ProjectRepositoryError(f"Failed to list projects: {str(e)}")

    async def delete_project(self, user_id: str, project_name: str) -> Dict[str, str]:
        """
        Delete a project from user workspace.

        Args:
            user_id: The user's ID
            project_name: Name of the project to delete

        Returns:
            Dict containing deletion confirmation
        """
        try:
            # Validate project name
            if '..' in project_name or '/' in project_name:
                raise ProjectRepositoryError("Invalid project name")

            user_dir = await self.get_user_workspace_path(user_id)
            project_path = user_dir / project_name

            if not project_path.exists():
                raise ProjectNotFoundError("Project not found")

            if not project_path.is_dir():
                raise ProjectRepositoryError("Invalid project")

            # Remove the project directory
            shutil.rmtree(project_path)

            logger.info(f"Successfully deleted project: {project_path}")

            return {"message": f"Successfully deleted project '{project_name}'"}

        except Exception as e:
            logger.error(f"Failed to delete project: {e}")
            raise ProjectRepositoryError(f"Failed to delete project: {str(e)}")


    # --- New: Create project (filesystem + database) ---
    async def create_project(
        self,
        *,
        db: Session,
        user: User,
        project_name: str,
        description: Optional[str] = None
    ) -> Project:
        """
        Create a project directory and persist a Project record linked to the user.
        """
        try:
            if ".." in project_name or "/" in project_name:
                raise ProjectRepositoryError("Invalid project name")

            user_dir = await self.get_user_workspace_path(str(user.id))
            project_path = user_dir / project_name
            if project_path.exists():
                raise ProjectAlreadyExistsError(f"Directory '{project_name}' already exists")

            project_path.mkdir(parents=True, exist_ok=False)

            # Create DB record and link user as owner
            project = Project(name=project_name, description=description)
            db.add(project)
            db.commit()
            db.refresh(project)

            db.execute(
                user_project_association.insert().values(
                    user_id=user.id,
                    project_id=project.id,
                    role="owner"
                )
            )
            db.commit()

            return project
        except Exception:
            db.rollback()
            # Ensure filesystem cleanup if DB fails after dir creation
            try:
                if 'project_path' in locals() and project_path.exists():
                    shutil.rmtree(project_path)
            except Exception:
                logger.exception("Failed to cleanup project directory after DB error")
            raise

# Dependency function for FastAPI
async def get_project_repository() -> ProjectRepository:
    """
    FastAPI dependency for Project repository.

    Returns:
        ProjectRepository: The project repository instance
    """
    return ProjectRepository()