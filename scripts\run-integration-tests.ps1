# Integration Test Runner Script (PowerShell)
# Runs comprehensive integration tests for the AI Coding Agent system

[CmdletBinding()]
param(
  [ValidateSet("development", "production")]
  [string]$Environment = "development",

  [int]$Timeout = 120,

  [switch]$Verbose,

  [switch]$NoCleanup,

  [switch]$ServicesOnly,

  [switch]$PerformanceTests,

  [switch]$Help
)

# Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$TestDir = Join-Path $ProjectRoot "tests\integration"

# Colors for output
$Colors = @{
  Red    = "Red"
  Green  = "Green"
  Yellow = "Yellow"
  Blue   = "Blue"
  White  = "White"
}

# Function definitions
function Show-Usage {
  Write-Host ""
  Write-Host "Integration Test Runner for AI Coding Agent" -ForegroundColor $Colors.Blue
  Write-Host ""
  Write-Host "USAGE:"
  Write-Host "    .\run-integration-tests.ps1 [OPTIONS]" -ForegroundColor $Colors.White
  Write-Host ""
  Write-Host "PARAMETERS:"
  Write-Host "    -Environment          Environment to test (development|production) [default: development]"
  Write-Host "    -Timeout              Timeout for service startup in seconds [default: 120]"
  Write-Host "    -Verbose              Enable verbose test output"
  Write-Host "    -NoCleanup            Don't cleanup test data after running"
  Write-Host "    -ServicesOnly         Only start services, don't run tests"
  Write-Host "    -PerformanceTests     Include performance tests"
  Write-Host "    -Help                 Show this help message"
  Write-Host ""
  Write-Host "EXAMPLES:"
  Write-Host "    .\run-integration-tests.ps1                    # Run tests in development mode"
  Write-Host "    .\run-integration-tests.ps1 -Environment production  # Run tests against production build"
  Write-Host "    .\run-integration-tests.ps1 -Verbose           # Run with verbose output"
  Write-Host "    .\run-integration-tests.ps1 -ServicesOnly      # Just start services for manual testing"
  Write-Host ""
}

function Write-Log {
  param(
    [string]$Message,
    [ValidateSet("Info", "Success", "Warning", "Error")]
    [string]$Level = "Info"
  )

  $color = switch ($Level) {
    "Info" { $Colors.Blue }
    "Success" { $Colors.Green }
    "Warning" { $Colors.Yellow }
    "Error" { $Colors.Red }
  }

  $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
  Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Test-Dependencies {
  Write-Log "Checking dependencies..." -Level Info

  $missingDeps = @()

  # Check for required tools
  $requiredCommands = @("docker", "docker-compose", "python", "pip")

  foreach ($cmd in $requiredCommands) {
    if (!(Get-Command $cmd -ErrorAction SilentlyContinue)) {
      $missingDeps += $cmd
    }
  }

  if ($missingDeps.Count -gt 0) {
    Write-Log "Missing required dependencies: $($missingDeps -join ', ')" -Level Error
    Write-Log "Please install the missing dependencies and try again" -Level Error
    return $false
  }

  # Check Python packages
  try {
    python -c "import pytest, requests" 2>$null
    if ($LASTEXITCODE -ne 0) {
      Write-Log "Installing Python test dependencies..." -Level Warning
      pip install -r "$ProjectRoot\requirements-dev.txt"
      if ($LASTEXITCODE -ne 0) {
        Write-Log "Failed to install Python dependencies" -Level Error
        return $false
      }
    }
  }
  catch {
    Write-Log "Failed to check Python dependencies: $($_.Exception.Message)" -Level Error
    return $false
  }

  Write-Log "All dependencies are available" -Level Success
  return $true
}

function Initialize-Environment {
  Write-Log "Setting up test environment..." -Level Info

  Set-Location $ProjectRoot

  # Ensure .env file exists
  if (!(Test-Path ".env")) {
    if (Test-Path ".env.example") {
      Write-Log "Creating .env file from .env.example" -Level Info
      Copy-Item ".env.example" ".env"
    }
    else {
      Write-Log "No .env or .env.example file found" -Level Warning
    }
  }

  # Set test environment variables
  $env:PYTEST_CURRENT_TEST = "true"
  $env:TEST_API_BASE_URL = "http://localhost:8000"
  $env:TEST_FRONTEND_BASE_URL = "http://localhost:3000"
  $env:TEST_TIMEOUT = $Timeout.ToString()

  if ($Environment -eq "development") {
    $env:NODE_ENV = "development"
    $env:DEBUG = "true"
  }
  else {
    $env:NODE_ENV = "production"
  }

  Write-Log "Environment setup complete" -Level Success
}

function Start-Services {
  Write-Log "Starting Docker services for $Environment environment..." -Level Info

  $composeArgs = @("-f", "docker-compose.yml")

  if ($Environment -eq "development") {
    $composeArgs += @("-f", "docker-compose.dev.yml")
  }

  # Stop any existing services
  Write-Log "Stopping existing services..." -Level Info
  docker-compose @composeArgs down --remove-orphans 2>$null

  # Start services
  Write-Log "Building and starting services..." -Level Info
  docker-compose @composeArgs up -d --build

  if ($LASTEXITCODE -ne 0) {
    Write-Log "Failed to start Docker services" -Level Error
    return $false
  }

  # Wait for services to be healthy
  Write-Log "Waiting for services to be healthy (timeout: ${Timeout}s)..." -Level Info

  $startTime = Get-Date
  $timeoutTime = $startTime.AddSeconds($Timeout)

  while ((Get-Date) -lt $timeoutTime) {
    $allHealthy = $true

    # Check each service
    $services = @("postgresql", "redis", "ai-orchestrator", "admin-dashboard")

    foreach ($service in $services) {
      $status = docker-compose @composeArgs ps $service 2>$null
      if ($status -notmatch "healthy|Up") {
        $allHealthy = $false
        break
      }
    }

    if ($allHealthy) {
      Write-Log "All services are healthy" -Level Success
      return $true
    }

    Write-Log "Waiting for services to be ready..." -Level Info
    Start-Sleep -Seconds 5
  }

  Write-Log "Services failed to become healthy within $Timeout seconds" -Level Error
  Write-Log "Service status:" -Level Info
  docker-compose @composeArgs ps

  # Show logs for debugging
  Write-Log "Recent logs:" -Level Info
  docker-compose @composeArgs logs --tail=20

  return $false
}

function Invoke-Tests {
  Write-Log "Running integration tests..." -Level Info

  $pytestArgs = @(
    $TestDir,
    "-v",
    "--tb=short",
    "--strict-markers",
    "-m", "integration"
  )

  if ($Verbose) {
    $pytestArgs += @("-s", "--capture=no")
  }

  if ($PerformanceTests) {
    $pytestArgs += @("--runslow")
  }
  else {
    $pytestArgs += @("-m", "not performance")
  }

  if ($NoCleanup) {
    $env:TEST_CLEANUP_ON_FAILURE = "false"
  }

  # Run tests
  python -m pytest @pytestArgs
  $testExitCode = $LASTEXITCODE

  if ($testExitCode -eq 0) {
    Write-Log "All integration tests passed!" -Level Success
  }
  else {
    Write-Log "Some integration tests failed (exit code: $testExitCode)" -Level Error

    # Show recent logs for debugging
    Write-Log "Recent service logs for debugging:" -Level Info
    docker-compose -f "docker-compose.yml" logs --tail=50 ai-orchestrator admin-dashboard
  }

  return $testExitCode
}

function Stop-Services {
  if (!$NoCleanup) {
    Write-Log "Cleaning up Docker services..." -Level Info

    $composeArgs = @("-f", "docker-compose.yml")
    if ($Environment -eq "development") {
      $composeArgs += @("-f", "docker-compose.dev.yml")
    }

    docker-compose @composeArgs down --remove-orphans -v

    # Clean up any dangling images or containers
    docker system prune -f 2>$null

    Write-Log "Cleanup complete" -Level Success
  }
  else {
    Write-Log "Skipping cleanup (services left running for debugging)" -Level Info
  }
}

function New-TestReport {
  Write-Log "Generating test report..." -Level Info

  $reportFile = Join-Path $ProjectRoot "test-results\integration-test-report.html"
  $reportDir = Split-Path -Parent $reportFile

  if (!(Test-Path $reportDir)) {
    New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
  }

  # Run tests again with HTML report generation
  python -m pytest $TestDir `
    --html="$reportFile" `
    --self-contained-html `
    -m "integration and not performance" `
    --tb=short `
    -q 2>$null

  if (Test-Path $reportFile) {
    Write-Log "Test report generated: $reportFile" -Level Success
  }
}

# Main execution
function Main {
  if ($Help) {
    Show-Usage
    return 0
  }

  Write-Log "Starting integration tests for AI Coding Agent" -Level Info
  Write-Log "Environment: $Environment" -Level Info
  Write-Log "Timeout: ${Timeout}s" -Level Info

  # Check dependencies
  if (!(Test-Dependencies)) {
    return 1
  }

  # Initialize environment
  Initialize-Environment

  # Start services
  if (!(Start-Services)) {
    return 1
  }

  try {
    if ($ServicesOnly) {
      Write-Log "Services started successfully. Press Ctrl+C to stop." -Level Success
      Write-Log "API available at: http://localhost:8000" -Level Info
      Write-Log "Admin Dashboard available at: http://localhost:3000" -Level Info
      Write-Log "Run tests manually with: python -m pytest tests/integration/ -v" -Level Info

      # Keep services running
      Read-Host "Press Enter to stop services..."
    }
    else {
      $testExitCode = Invoke-Tests
      New-TestReport

      if ($testExitCode -eq 0) {
        Write-Log "Integration tests completed successfully!" -Level Success
      }
      else {
        Write-Log "Integration tests failed!" -Level Error
      }

      return $testExitCode
    }
  }
  finally {
    Stop-Services
  }

  return 0
}

# Execute main function
$exitCode = Main
exit $exitCode