#!/usr/bin/env python3
"""
Debug script for AI Orchestrator startup issues.
This script helps diagnose and fix common startup problems.
"""

import sys
import os
import logging
import traceback
from pathlib import Path

# Configure logging for debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/tmp/ai_orchestrator_debug.log', mode='w')
    ]
)
logger = logging.getLogger("debug_startup")

def check_environment():
    """Check environment variables and configuration."""
    logger.info("Checking environment configuration...")

    required_vars = [
        "REDIS_URL",
        "DATABASE_URL"
    ]

    optional_vars = [
        "OLLAMA_BASE_URL",
        "OPENROUTER_API_KEY",
        "OPENAI_API_KEY",
        "ANTHROPIC_API_KEY",
        "USE_SUPABASE"
    ]

    missing_required = []
    for var in required_vars:
        if not os.getenv(var):
            missing_required.append(var)
            logger.error(f"Missing required environment variable: {var}")
        else:
            logger.info(f"✓ {var} is set")

    for var in optional_vars:
        if os.getenv(var):
            logger.info(f"✓ {var} is set")
        else:
            logger.warning(f"Optional variable {var} is not set")

    return len(missing_required) == 0

def check_imports():
    """Check if all required modules can be imported."""
    logger.info("Checking module imports...")

    import_tests = [
        ("fastapi", "from fastapi import FastAPI"),
        ("redis", "import redis"),
        ("sqlalchemy", "from sqlalchemy import create_engine"),
        ("pydantic", "from pydantic import BaseModel"),
        ("uvicorn", "import uvicorn"),
    ]

    failed_imports = []

    for name, import_stmt in import_tests:
        try:
            exec(import_stmt)
            logger.info(f"✓ {name} import successful")
        except ImportError as e:
            logger.error(f"✗ {name} import failed: {e}")
            failed_imports.append(name)
        except Exception as e:
            logger.error(f"✗ {name} import error: {e}")
            failed_imports.append(name)

    return len(failed_imports) == 0

def check_local_imports():
    """Check if local modules can be imported."""
    logger.info("Checking local module imports...")

    # Add the src directory to Python path
    src_path = Path(__file__).parent / "src"
    if src_path.exists():
        sys.path.insert(0, str(src_path))
        logger.info(f"Added {src_path} to Python path")

    local_import_tests = [
        ("websocket_manager", "from services.websocket_manager import get_chat_manager"),
        ("llm_router", "from router.llm_router import initialize_llm_service"),
        ("role_management", "from router.role_management import initialize_role_configuration"),
        ("approval_manager", "from approval.approval_manager import ApprovalManager"),
        ("validation_models", "from models.validation_models import ValidationResult"),
    ]

    failed_imports = []

    for name, import_stmt in local_import_tests:
        try:
            exec(import_stmt)
            logger.info(f"✓ {name} import successful")
        except ImportError as e:
            logger.error(f"✗ {name} import failed: {e}")
            failed_imports.append(name)
            traceback.print_exc()
        except Exception as e:
            logger.error(f"✗ {name} import error: {e}")
            failed_imports.append(name)
            traceback.print_exc()

    return len(failed_imports) == 0

def test_basic_startup():
    """Test basic application startup."""
    logger.info("Testing basic FastAPI application startup...")

    try:
        # Add src to path if not already there
        src_path = Path(__file__).parent / "src"
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))

        from main import app
        logger.info("✓ FastAPI app created successfully")

        # Test health endpoint
        from fastapi.testclient import TestClient
        client = TestClient(app)
        response = client.get("/health")

        if response.status_code == 200:
            logger.info("✓ Health endpoint working")
            logger.info(f"Health response: {response.json()}")
        else:
            logger.error(f"✗ Health endpoint failed: {response.status_code}")
            return False

        return True

    except Exception as e:
        logger.error(f"✗ Application startup failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main debugging function."""
    logger.info("Starting AI Orchestrator startup diagnostics...")
    logger.info("=" * 60)

    success = True

    # Check environment
    logger.info("\n1. Environment Check")
    logger.info("-" * 30)
    if not check_environment():
        success = False

    # Check imports
    logger.info("\n2. External Dependencies Check")
    logger.info("-" * 40)
    if not check_imports():
        success = False

    # Check local imports
    logger.info("\n3. Local Modules Check")
    logger.info("-" * 30)
    if not check_local_imports():
        success = False

    # Test basic startup
    logger.info("\n4. Application Startup Test")
    logger.info("-" * 35)
    if not test_basic_startup():
        success = False

    # Summary
    logger.info("\n" + "=" * 60)
    if success:
        logger.info("🎉 All checks passed! Application should start successfully.")
        logger.info("If you're still experiencing issues, check:")
        logger.info("  - Docker container logs")
        logger.info("  - Network connectivity")
        logger.info("  - Resource availability")
        return 0
    else:
        logger.error("❌ Some checks failed. Please address the issues above.")
        logger.error("Common solutions:")
        logger.error("  - Install missing dependencies: pip install -r requirements.txt")
        logger.error("  - Set missing environment variables")
        logger.error("  - Check file permissions")
        logger.error("  - Verify Docker container configuration")
        return 1

if __name__ == "__main__":
    sys.exit(main())