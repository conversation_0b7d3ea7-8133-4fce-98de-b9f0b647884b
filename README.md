# AI Coding Agent

Container-first AI coding assistant with VS Code-like interface powered by code-server, featuring a comprehensive validation framework for reliable and safe code generation.

## Project Overview

This project implements an AI Coding Agent using a container-first development approach. The system integrates essential services into a minimal number of containers for efficiency, providing a complete development environment in the browser with enterprise-grade validation and safety features.

## ✨ Key Features

### 🔍 Comprehensive Validation Framework
- **Multi-layered Validation**: File existence, code syntax, functional testing, and integration validation
- **Intelligent Error Recovery**: Automatic detection and fixing of common issues with LLM assistance
- **Pipeline Execution**: Sequential execution with validation gates and dependency management
- **State Management**: Checkpoint creation, rollback capabilities, and transaction-like behavior
- **User Approval System**: Interactive approval workflows with risk assessment and audit trails
- **Real-time Monitoring**: WebSocket-based notifications and comprehensive metrics

### 🛡️ Safety and Reliability
- **Fail-Fast Validation**: Catch issues immediately at each step
- **Automatic Error Recovery**: Fix common problems without user intervention
- **Rollback Capability**: Undo changes if things go wrong
- **User Control**: Mandatory approvals at critical points
- **Production Ready**: Ensure proper configuration for deployment
- **Circuit Breaker Patterns**: Prevent cascading failures
- **Retry Strategies**: Intelligent retry with exponential backoff

### 🏗️ Core Architecture

- **code-server**: VS Code-like development environment in the browser
- **ai-orchestrator**: FastAPI backend with AI agents
- **postgresql**: Database with pgvector for vector storage
- **redis**: Cache and session management
- **ollama**: Local LLM model serving
- **admin-dashboard**: Administrative interface for LLM provider management

## Development Environments

### Ultra-Streamlined Development (4 containers)
For maximum efficiency with essential services only:

```bash
# Copy and configure environment variables
cp .env.example .env
# Edit .env with your configuration

# Option 1: Start ultra-streamlined development environment
docker-compose up -d

# Option 2: Use the convenience script
./scripts/start-dev.sh

# Access services:
# - code-server: http://localhost:8080
# - AI Orchestrator API: http://localhost:8000
# - Admin Dashboard: http://localhost:3000
```

### Development with Live Reloading (Watch Mode)
For enhanced development experience with live code reloading (requires Docker Desktop):

```bash
# Start development environment with watch functionality
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Start watch mode for live reloading
docker-compose watch

# Or use the convenience script
./scripts/start-dev.sh watch
```

### Full-Featured Environment (11 containers)
For debugging with monitoring and logging stack:

```bash
# Start full environment (streamlined + monitoring)
docker-compose -f docker-compose.dev.yml -f docker-compose.full.yml up -d

# Or use the convenience script
./scripts/start-dev.sh full

# Additional services:
# - Prometheus: http://localhost:9090
# - Grafana: http://localhost:3001
# - Elasticsearch: http://localhost:9200
# - Kibana: http://localhost:5601
```

## Quick Start

### Development Setup with Validation Framework
```bash
# Start development environment with validation framework
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# For Docker Compose v2.23+, you can also use:
docker-compose --profile dev up
```

### Using the Validation Framework

The validation framework is automatically integrated into the AI orchestrator. Here's a quick example:

```python
from validation.validation_rules import ValidationRuleEngine
from services.task_validator import TaskValidator
from approval.approval_manager import ApprovalManager

# Initialize validation components
validation_engine = ValidationRuleEngine("/workspace")
task_validator = TaskValidator("/workspace")
approval_manager = ApprovalManager("/workspace")
await approval_manager.initialize()

# Validate a completed task
validation_result = await task_validator.validate_task_completion(task, task_result)

if not validation_result.is_valid:
    # Handle validation failure with error recovery
    recovery_result = await error_recovery.handle_task_failure(task, Exception(validation_result.error))

    if recovery_result.success and recovery_result.retry_recommended:
        # Retry the task after recovery
        pass

# Request user approval for critical operations
approval_request = await approval_manager.create_approval_request(
    user_id="user123",
    approval_type=ApprovalType.PHASE_COMPLETION,
    title="Phase 1 Complete",
    description="All components created successfully",
    item_type="phase",
    item_id="phase_1"
)
```

### Validation Framework Features

- **✅ Task Validation**: Comprehensive validation of task completion
- **🔄 Error Recovery**: Automatic detection and fixing of common issues
- **⚡ Pipeline Execution**: Sequential execution with validation gates
- **💾 State Management**: Checkpoint creation and rollback capabilities
- **📝 Approval Workflows**: User approval with risk assessment
- **📈 Real-time Monitoring**: WebSocket notifications and metrics
- **🛡️ Circuit Breakers**: Prevent system failures
- **🔁 Retry Strategies**: Intelligent retry mechanisms

See [Validation Framework Documentation](docs/validation-framework.md) for detailed usage instructions.

See [Docker Compose Watch Documentation](docs/DOCKER_COMPOSE_WATCH.md) for detailed development workflow instructions.

### Production Deployment
```bash
# Deploy to production
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

1. **Prerequisites**
   - Docker and Docker Compose
   - Git

2. **Setup**
   ```bash
   # Clone the repository
   git clone <repository-url>
   cd ai-coding-agent

   # Copy and configure environment variables
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Choose Your Environment**
   - **Ultra-Streamlined Development**: `./scripts/start-dev.sh`
   - **Full-Featured Debugging**: `./scripts/start-dev.sh full`

4. **Access Services**
   - code-server: http://localhost:8080
   - AI Orchestrator API: http://localhost:8000
   - Admin Dashboard: http://localhost:3000

## Development Setup

### Install Development Dependencies
```bash
pip install -r requirements-dev.txt
```

### Set up Pre-commit Hooks
```bash
pre-commit install
```

### Running Tests

The validation framework includes comprehensive tests:

```bash
# Run all validation framework tests
pytest tests/unit/validation/ -v
pytest tests/unit/core/ -v
pytest tests/integration/test_validation_framework.py -v

# Run with coverage
pytest --cov=src/validation --cov=src/services --cov=src/execution --cov=src/state --cov=src/approval tests/ --cov-report=html

# Run specific test categories
pytest tests/unit/validation/test_validation_rules.py -v  # Validation rules
pytest tests/unit/core/test_error_recovery.py -v          # Error recovery
pytest tests/integration/ -v                             # Integration tests
```

### Validation Tools

This project includes several validation tools to ensure code quality and consistency:

- **File Naming Validation**: `python scripts/validate_naming.py`
- **Directory Structure Validation**: `python scripts/validate_structure.py`
- **Dockerfile Linting**: `./scripts/lint_dockerfiles.sh`
- **Validation Framework Health Check**: `curl http://localhost:8000/api/v1/health`

## Project Structure

```
ai-coding-agent/
├── containers/           # Container-specific code
│   ├── ai-orchestrator/ # FastAPI backend with AI agents
│   ├── code-server/     # VS Code environment
│   ├── admin-dashboard/ # Administrative interface
│   ├── postgresql/      # Database configuration
│   └── ollama/          # LLM model manifests
├── docs/                # Documentation
├── scripts/             # Utility scripts
├── volumes/             # Persistent data volumes
└── .env.example         # Environment variable template
```

## Documentation

### Core Documentation
- [Project Roadmap](docs/Projectroadmap.md) - Complete implementation plan
- [Cline Rules](docs/CLINE_RULES.md) - Project organization principles
- [Scripts Documentation](scripts/README.md) - Utility script usage

### Validation Framework Documentation
- [Validation Framework Overview](docs/validation-framework.md) - Complete framework documentation
- [API Documentation](docs/api/validation-endpoints.md) - REST API reference and examples
- [Deployment Configuration](docs/deployment/validation-config.md) - Configuration guide

### API References
- **Validation Endpoints**: `GET/POST /api/v1/validate/*` - Task and pipeline validation
- **Execution Control**: `GET/POST /api/v1/execution/*` - Pipeline execution management
- **Approval System**: `GET/POST /api/v1/approvals/*` - User approval workflows
- **State Management**: `GET/POST /api/v1/checkpoints/*` - Checkpoint and rollback
- **Health Monitoring**: `GET /api/v1/health` - System health and metrics

### Key Features Documentation
1. **Multi-layered Validation**: File, syntax, functional, and integration validation
2. **Error Recovery**: Automatic detection and LLM-assisted fixing
3. **Pipeline Execution**: Sequential execution with validation gates
4. **State Management**: Checkpoints, rollbacks, and state serialization
5. **Approval Workflows**: Risk-assessed user approvals with audit trails
6. **Real-time Monitoring**: WebSocket notifications and performance metrics

## Contributing

1. Follow the branching strategy defined in CLINE_RULES.md
2. Ensure all pre-commit hooks pass
3. Update documentation when adding new features
4. Write unit tests for new functionality

## License

[Specify your license here]
