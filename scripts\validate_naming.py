#!/usr/bin/env python3
"""
File naming convention validator for AI Coding Agent Project.
Ensures files follow the project's naming standards:
- Lowercase with hyphens for directories and files
- Underscores for Python module names
- Descriptive names that clearly indicate purpose
"""

import os
import re
import sys
from pathlib import Path

def validate_file_naming():
    """Validate file and directory naming conventions."""
    project_root = Path(__file__).parent.parent
    errors = []

    # Define naming patterns
    dir_file_pattern = re.compile(r'^[a-z0-9\-\._]+$')  # lowercase, numbers, hyphens, dots, underscores
    python_module_pattern = re.compile(r'^[a-z0-9_]+$')  # lowercase, numbers, underscores

    for root, dirs, files in os.walk(project_root):
        # Skip hidden directories, virtual environments, and node_modules
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['venv', '__pycache__', 'node_modules']]

        # Skip if we're inside node_modules
        if 'node_modules' in Path(root).parts:
            continue

        # Check directory names
        for directory in dirs:
            dir_path = Path(root) / directory
            # Skip certain directories that have special naming requirements
            if directory in ['__pycache__', '.git', 'node_modules']:
                continue

            # Python package directories should allow underscores
            if (dir_path / '__init__.py').exists():
                if not python_module_pattern.match(directory):
                    errors.append(f"Invalid Python package name: {dir_path}")
            else:
                # Regular directories should use hyphens
                if not dir_file_pattern.match(directory):
                    errors.append(f"Invalid directory name (should use hyphens): {dir_path}")

        # Check file names
        for file in files:
            if file.startswith('.'):
                continue

            file_path = Path(root) / file

            # Skip if we're inside node_modules
            if 'node_modules' in Path(root).parts:
                continue

            # Python modules (importable .py files) should use underscores
            if file.endswith('.py'):
                # Extract just the filename without extension
                filename = file[:-3]  # Remove .py extension
                # Check if this is in a Python package (has __init__.py in same dir or parent)
                is_module = (Path(root) / '__init__.py').exists() or any((Path(root).parent / p / '__init__.py').exists() for p in Path(root).parent.parts if p != '.')
                # Scripts (standalone .py files) can use hyphens, modules should use underscores
                if is_module and not python_module_pattern.match(filename):
                    errors.append(f"Invalid Python module name (should use underscores): {file_path}")
                elif not is_module and not dir_file_pattern.match(filename.replace('_', '-')):
                    # Allow either hyphens or underscores for scripts
                    pass  # Scripts are flexible
            else:
                # Other files should use hyphens (with exceptions)
                # Allow common file patterns and standard files
                standard_files = {'Dockerfile', 'README.md', 'package.json', 'package-lock.json',
                                'tsconfig.json', 'next-env.d.ts', '.env', '.env.example',
                                'docker-compose.yml', 'docker-compose.yaml'}
                # Allow React/TypeScript component files (PascalCase is standard)
                if (file.endswith('.tsx') or file.endswith('.ts') or file.endswith('.jsx') or file.endswith('.js')):
                    # React components typically use PascalCase
                    pass  # Allow any naming for TypeScript/JavaScript files
                elif file in standard_files:
                    pass  # Allow standard file names
                elif not dir_file_pattern.match(file):
                    errors.append(f"Invalid file name (should use hyphens): {file_path}")

    return errors

def main():
    """Main validation function."""
    print("Validating file naming conventions...")
    errors = validate_file_naming()

    if errors:
        print("❌ Naming convention violations found:")
        for error in errors:
            print(f"  - {error}")
        sys.exit(1)
    else:
        print("✅ All file names follow the naming conventions!")
        sys.exit(0)

if __name__ == "__main__":
    main()
