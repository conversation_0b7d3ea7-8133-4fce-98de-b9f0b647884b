_format_version: "3.0"
_transform: true

###
### Kong Gateway Configuration for Supabase Integration
### This configuration routes and secures all Supabase services
###

###
### Services Definition
###

services:
  # Supabase Auth Service
  - name: auth-v1
    url: http://supabase-auth:9999/
    tags:
      - supabase
      - auth
    routes:
      - name: auth-v1-all
        strip_path: true
        paths:
          - /auth/v1/
        methods:
          - GET
          - POST
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
    plugins:
      - name: cors
        config:
          origins:
            - http://localhost:3000
            - http://localhost:8080
            - http://localhost:8001
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - Authorization
            - X-Auth-Token
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local

  # Supabase REST API (PostgREST)
  - name: rest-v1
    url: http://supabase-rest:3000/
    tags:
      - supabase
      - rest-api
    routes:
      - name: rest-v1-all
        strip_path: true
        paths:
          - /rest/v1/
        methods:
          - GET
          - POST
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
    plugins:
      - name: cors
        config:
          origins:
            - http://localhost:3000
            - http://localhost:8080
            - http://localhost:8001
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - Authorization
            - apikey
            - Range
            - X-Client-Info
          exposed_headers:
            - Content-Range
          credentials: true
          max_age: 3600
      - name: key-auth
        config:
          key_names:
            - apikey
          hide_credentials: false
      - name: rate-limiting
        config:
          minute: 120
          hour: 2000
          policy: local

  # Supabase Realtime Service
  - name: realtime-v1
    url: http://supabase-realtime:4000/
    tags:
      - supabase
      - realtime
    routes:
      - name: realtime-v1-all
        strip_path: true
        paths:
          - /realtime/v1/
        methods:
          - GET
          - POST
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
    plugins:
      - name: cors
        config:
          origins:
            - http://localhost:3000
            - http://localhost:8080
            - http://localhost:8001
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - Authorization
            - apikey
            - Sec-WebSocket-Extensions
            - Sec-WebSocket-Key
            - Sec-WebSocket-Protocol
            - Sec-WebSocket-Version
            - X-Client-Info
          credentials: true
          max_age: 3600
      - name: key-auth
        config:
          key_names:
            - apikey
          hide_credentials: false
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local

  # AI Orchestrator Service (Custom API)
  - name: ai-orchestrator-v1
    url: http://ai-orchestrator:8000/
    tags:
      - ai-orchestrator
      - custom-api
    routes:
      - name: ai-orchestrator-v1-all
        strip_path: false
        paths:
          - /api/
        methods:
          - GET
          - POST
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
    plugins:
      - name: cors
        config:
          origins:
            - http://localhost:3000
            - http://localhost:8080
            - http://localhost:8001
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - Authorization
            - X-API-Key
          credentials: true
          max_age: 3600
      - name: rate-limiting
        config:
          minute: 100
          hour: 1500
          policy: local

###
### Consumers (API Key Management)
###

consumers:
  # Anonymous user (for public endpoints)
  - username: anon
    tags:
      - supabase
      - public
    keyauth_credentials:
      # This should match your ANON_KEY from environment
      - key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

  # Service role (for admin/system operations)
  - username: service_role
    tags:
      - supabase
      - admin
    keyauth_credentials:
      # This should match your SERVICE_ROLE_KEY from environment
      - key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

  # Authenticated user (for logged-in users)
  - username: authenticated
    tags:
      - supabase
      - authenticated

###
### Global Plugins
###

plugins:
  # Request/Response logging for debugging
  - name: file-log
    config:
      path: /tmp/kong.log
      reopen: true
    tags:
      - logging

  # Response transformation for consistent API responses
  - name: response-transformer
    config:
      add:
        headers:
          - "X-API-Gateway:Kong"
          - "X-Supabase-Integration:v1.0"
    tags:
      - transformation

  # Security headers
  - name: request-transformer
    config:
      add:
        headers:
          - "X-Forwarded-Host:$(headers.host)"
          - "X-Real-IP:$(headers.x-forwarded-for)"
    tags:
      - security

###
### ACLs (Access Control Lists)
###

acls:
  # Anonymous user permissions
  - consumer: anon
    group: anonymous
    tags:
      - permission

  # Service role permissions (full access)
  - consumer: service_role
    group: admin
    tags:
      - permission

  # Authenticated user permissions
  - consumer: authenticated
    group: users
    tags:
      - permission

###
### Rate Limiting Tiers
###

rate_limiting_tiers:
  # Free tier (anonymous)
  anonymous:
    minute: 30
    hour: 500
    day: 2000

  # Authenticated tier
  authenticated:
    minute: 120
    hour: 2000
    day: 10000

  # Admin tier (service role)
  admin:
    minute: 500
    hour: 10000
    day: 50000
