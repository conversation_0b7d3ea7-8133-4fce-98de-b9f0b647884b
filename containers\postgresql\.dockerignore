# Comprehensive .dockerignore for PostgreSQL
# Reduces build context size and prevents sensitive files from being copied

# Version control
.git
.gitignore
.gitattributes
.gitmodules

# Environment files (sensitive)
.env
.env.*
!.env.example

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
docs/
*.md
!README.md

# Secrets and certificates
secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Backup files
*.bak
*.backup
*.orig

# Database data files (these should be in volumes)
data/
pgdata/
*.db
*.sqlite
*.sqlite3

# PostgreSQL specific temporary files
postgresql.conf.backup
pg_hba.conf.backup