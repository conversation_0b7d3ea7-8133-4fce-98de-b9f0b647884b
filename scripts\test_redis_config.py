#!/usr/bin/env python3
"""
Redis Configuration Validation Script.

This script validates Redis configuration and tests all integration
features according to the Redis Integration Guide.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional

# Add the source directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'containers', 'ai-orchestrator', 'src'))

try:
    import redis.asyncio as redis
    from redis.asyncio import ConnectionPool
except ImportError:
    print("❌ Error: redis package not found. Install with: pip install redis")
    sys.exit(1)


async def test_redis_connection(redis_url: str) -> Dict[str, Any]:
    """Test basic Redis connection."""
    print("🔌 Testing Redis connection...")

    try:
        client = redis.from_url(redis_url, decode_responses=True)

        # Test ping
        pong = await client.ping()
        if pong:
            print("   ✅ Redis ping successful")

        # Get server info
        info = await client.info("server")
        memory_info = await client.info("memory")

        result = {
            "connected": True,
            "redis_version": info.get("redis_version"),
            "uptime_seconds": info.get("uptime_in_seconds"),
            "used_memory": memory_info.get("used_memory_human"),
            "connected_clients": info.get("connected_clients")
        }

        print(f"   📊 Redis version: {result['redis_version']}")
        print(f"   💾 Memory usage: {result['used_memory']}")
        print(f"   👥 Connected clients: {result['connected_clients']}")

        await client.close()
        return result

    except Exception as e:
        print(f"   ❌ Redis connection failed: {str(e)}")
        return {"connected": False, "error": str(e)}


async def test_redis_configuration(redis_url: str) -> Dict[str, Any]:
    """Test Redis configuration settings."""
    print("⚙️  Testing Redis configuration...")

    try:
        client = redis.from_url(redis_url, decode_responses=True)

        # Test configuration settings
        config_tests = {
            "maxmemory-policy": "allkeys-lru",
            "appendonly": "yes",
            "save": ["900 1", "300 10", "60 10000"]  # Expected save points
        }

        results = {}

        for setting, expected in config_tests.items():
            if setting == "save":
                # Handle save configuration specially
                config_value = await client.config_get("save")
                current_save = config_value.get("save", "").split()

                # Check if all expected save points are configured
                all_found = True
                for save_point in expected:
                    if save_point not in " ".join(current_save):
                        all_found = False
                        break

                results[setting] = {
                    "expected": expected,
                    "actual": current_save,
                    "correct": all_found
                }

                if all_found:
                    print(f"   ✅ {setting}: Properly configured")
                else:
                    print(f"   ⚠️  {setting}: Configuration differs from expected")
            else:
                config_value = await client.config_get(setting)
                actual = config_value.get(setting)
                correct = actual == expected

                results[setting] = {
                    "expected": expected,
                    "actual": actual,
                    "correct": correct
                }

                if correct:
                    print(f"   ✅ {setting}: {actual}")
                else:
                    print(f"   ⚠️  {setting}: Expected '{expected}', got '{actual}'")

        await client.close()
        return results

    except Exception as e:
        print(f"   ❌ Configuration test failed: {str(e)}")
        return {"error": str(e)}


async def test_caching_functionality(redis_url: str) -> Dict[str, Any]:
    """Test Redis caching functionality."""
    print("💾 Testing caching functionality...")

    try:
        client = redis.from_url(redis_url, decode_responses=True)

        # Test basic key operations
        test_key = "test:cache:validation"
        test_value = "redis_cache_test_value"

        # Set and get
        await client.set(test_key, test_value)
        retrieved_value = await client.get(test_key)

        if retrieved_value == test_value:
            print("   ✅ Basic SET/GET operations working")
        else:
            print(f"   ❌ SET/GET failed: expected '{test_value}', got '{retrieved_value}'")

        # Test TTL
        await client.setex(f"{test_key}:ttl", 5, "ttl_test")
        ttl = await client.ttl(f"{test_key}:ttl")

        if ttl > 0:
            print("   ✅ TTL (expiration) functionality working")
        else:
            print("   ❌ TTL functionality failed")

        # Test hash operations (for session data)
        hash_key = "test:hash:session"
        session_data = {
            "user_id": "test_user",
            "created_at": datetime.utcnow().isoformat(),
            "role": "admin"
        }

        await client.hset(hash_key, mapping=session_data)
        retrieved_hash = await client.hgetall(hash_key)

        if retrieved_hash == session_data:
            print("   ✅ Hash operations (sessions) working")
        else:
            print("   ❌ Hash operations failed")

        # Test list operations (for queues)
        list_key = "test:list:queue"
        await client.lpush(list_key, "task1", "task2", "task3")
        list_length = await client.llen(list_key)

        if list_length == 3:
            print("   ✅ List operations (queues) working")
        else:
            print(f"   ❌ List operations failed: expected length 3, got {list_length}")

        # Cleanup test keys
        await client.delete(test_key, f"{test_key}:ttl", hash_key, list_key)

        await client.close()
        return {"caching_tests_passed": True}

    except Exception as e:
        print(f"   ❌ Caching test failed: {str(e)}")
        return {"caching_tests_passed": False, "error": str(e)}


async def test_pubsub_functionality(redis_url: str) -> Dict[str, Any]:
    """Test Redis pub/sub functionality for real-time messaging."""
    print("📡 Testing pub/sub functionality...")

    try:
        # Publisher client
        pub_client = redis.from_url(redis_url, decode_responses=True)

        # Subscriber client
        sub_client = redis.from_url(redis_url, decode_responses=True)
        pubsub = sub_client.pubsub()

        # Subscribe to test channel
        test_channel = "test:realtime:validation"
        await pubsub.subscribe(test_channel)

        # Publish test message
        test_message = {"type": "test", "data": "pubsub_test", "timestamp": datetime.utcnow().isoformat()}
        await pub_client.publish(test_channel, json.dumps(test_message))

        # Try to receive message (with timeout)
        message_received = False
        try:
            # Get subscription confirmation
            await pubsub.get_message(timeout=1.0)

            # Get actual message
            message = await pubsub.get_message(timeout=2.0)

            if message and message["type"] == "message":
                received_data = json.loads(message["data"])
                if received_data["type"] == "test":
                    message_received = True
                    print("   ✅ Pub/Sub messaging working")
                else:
                    print("   ❌ Pub/Sub message content incorrect")
            else:
                print("   ❌ Pub/Sub message not received")

        except asyncio.TimeoutError:
            print("   ❌ Pub/Sub message receive timeout")

        await pubsub.close()
        await pub_client.close()
        await sub_client.close()

        return {"pubsub_working": message_received}

    except Exception as e:
        print(f"   ❌ Pub/Sub test failed: {str(e)}")
        return {"pubsub_working": False, "error": str(e)}


async def test_streams_functionality(redis_url: str) -> Dict[str, Any]:
    """Test Redis Streams functionality for task queues."""
    print("🌊 Testing Redis Streams functionality...")

    try:
        client = redis.from_url(redis_url, decode_responses=True)

        stream_name = "test:task:queue"
        group_name = "test_workers"
        consumer_name = "test_consumer"

        # Create consumer group
        try:
            await client.xgroup_create(stream_name, group_name, id="0", mkstream=True)
            print("   ✅ Consumer group created successfully")
        except redis.ResponseError as e:
            if "BUSYGROUP" in str(e):
                print("   ✅ Consumer group already exists")
            else:
                raise

        # Add test task to stream
        task_data = {
            "task_id": "test_task_123",
            "task_type": "validation",
            "data": json.dumps({"code": "print('hello')", "language": "python"})
        }

        stream_id = await client.xadd(stream_name, task_data)
        print(f"   ✅ Task added to stream: {stream_id}")

        # Read from stream as consumer
        messages = await client.xreadgroup(
            streams={stream_name: ">"},
            groupname=group_name,
            consumername=consumer_name,
            count=1
        )

        if messages and len(messages) > 0:
            stream, msgs = messages[0]
            if len(msgs) > 0:
                msg_id, fields = msgs[0]

                if fields.get("task_type") == "validation":
                    print("   ✅ Stream message read successfully")

                    # Acknowledge message
                    await client.xack(group_name, stream_name, msg_id)
                    print("   ✅ Message acknowledged")
                else:
                    print("   ❌ Stream message content incorrect")
            else:
                print("   ❌ No messages in stream")
        else:
            print("   ❌ Failed to read from stream")

        # Cleanup
        await client.delete(stream_name)

        await client.close()
        return {"streams_working": True}

    except Exception as e:
        print(f"   ❌ Streams test failed: {str(e)}")
        return {"streams_working": False, "error": str(e)}


async def test_performance_settings(redis_url: str) -> Dict[str, Any]:
    """Test Redis performance settings."""
    print("⚡ Testing performance settings...")

    try:
        client = redis.from_url(redis_url, decode_responses=True)

        # Test pipeline performance
        pipe = client.pipeline()

        # Add multiple operations to pipeline
        for i in range(100):
            pipe.set(f"perf:test:{i}", f"value_{i}")

        start_time = datetime.utcnow()
        await pipe.execute()
        end_time = datetime.utcnow()

        pipeline_time = (end_time - start_time).total_seconds() * 1000  # ms

        if pipeline_time < 100:  # Should complete in under 100ms
            print(f"   ✅ Pipeline performance good: {pipeline_time:.2f}ms for 100 operations")
        else:
            print(f"   ⚠️  Pipeline performance slow: {pipeline_time:.2f}ms for 100 operations")

        # Test memory usage
        memory_info = await client.info("memory")
        used_memory_mb = int(memory_info.get("used_memory", 0)) / 1024 / 1024

        print(f"   📊 Current memory usage: {used_memory_mb:.2f} MB")

        # Cleanup performance test keys
        keys_to_delete = [f"perf:test:{i}" for i in range(100)]
        await client.delete(*keys_to_delete)

        await client.close()

        return {
            "pipeline_time_ms": pipeline_time,
            "memory_usage_mb": used_memory_mb,
            "performance_good": pipeline_time < 100
        }

    except Exception as e:
        print(f"   ❌ Performance test failed: {str(e)}")
        return {"performance_good": False, "error": str(e)}


async def main():
    """Run all Redis configuration validation tests."""
    print("🚀 Redis Configuration Validation")
    print("=" * 50)

    # Get Redis URL from environment
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    print(f"📍 Testing Redis at: {redis_url}")
    print()

    # Run all tests
    tests = [
        ("Basic Connection", test_redis_connection),
        ("Configuration Settings", test_redis_configuration),
        ("Caching Functionality", test_caching_functionality),
        ("Pub/Sub Messaging", test_pubsub_functionality),
        ("Streams (Task Queues)", test_streams_functionality),
        ("Performance Settings", test_performance_settings)
    ]

    results = {}
    passed_tests = 0
    total_tests = len(tests)

    for test_name, test_func in tests:
        try:
            result = await test_func(redis_url)
            results[test_name] = result

            # Simple pass/fail logic
            if "error" not in result and result:
                passed_tests += 1

        except Exception as e:
            print(f"   ❌ Test {test_name} failed with exception: {str(e)}")
            results[test_name] = {"error": str(e)}

        print()  # Add spacing between tests

    # Summary
    print("📋 Test Summary")
    print("=" * 50)
    print(f"✅ Passed: {passed_tests}/{total_tests} tests")
    print(f"❌ Failed: {total_tests - passed_tests}/{total_tests} tests")

    if passed_tests == total_tests:
        print("\n🎉 All Redis tests passed! Configuration is optimal.")
        return 0
    else:
        print(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Review configuration.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)