{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "dist", "lib": ["ES2020", "DOM"], "sourceMap": true, "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "declaration": false, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "exclude": ["node_modules", ".vscode-test", "dist"]}