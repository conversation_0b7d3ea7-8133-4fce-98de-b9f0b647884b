# Supabase Environment Configuration
# Copy this to your .env file and update with your values

# =============================================================================
# SUPABASE CORE CONFIGURATION
# =============================================================================

# Database Configuration
POSTGRES_DB=ai_coding_agent
POSTGRES_USER=postgres
POSTGRES_PASSWORD=1F0fcz62b5EzLdEPzYR4Tru0VTp2lPyz8bBLWH4k98M

# JWT Configuration (CRITICAL: Generate secure keys!)
# Use: openssl rand -base64 32 to generate
JWT_SECRET=DOdY74d1-AfYO4DJEEzEZmWhL89NuwJNVhZ7V7RmBsQ
JWT_EXPIRY=3600
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Supabase API Keys (Generate these from Supabase Dashboard or using scripts)
# For development, you can use the keys below (CHANGE IN PRODUCTION!)
ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjIwNzEzODE0ODB9.wTftZt7OtXkGUC3-qki3KOA_acZbD8g2oQWZ8ywjX2M
SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MjA3MTM4MTQ4MH0.bR3C1qjoDHz4EzETIk7l2VJh1ZmOow-nnUiQA-_P1Ro

# =============================================================================
# SUPABASE SERVICE URLS
# =============================================================================

# Application URLs
SITE_URL=http://localhost:3000
API_EXTERNAL_URL=http://localhost:8000
SUPABASE_URL=http://localhost:8000

# URI Allow List for CORS and Auth
URI_ALLOW_LIST=http://localhost:3000,http://localhost:8000,http://localhost:8080,http://localhost:8001

# =============================================================================
# KONG GATEWAY CONFIGURATION
# =============================================================================

# Kong Gateway Ports
KONG_HTTP_PORT=8000
KONG_HTTPS_PORT=8443

# =============================================================================
# POSTGREST CONFIGURATION
# =============================================================================

# Database schemas to expose via REST API
PGRST_DB_SCHEMAS=public,auth,storage
PGRST_DB_ANON_ROLE=anon

# =============================================================================
# AUTH SERVICE CONFIGURATION
# =============================================================================

# Signup and Email Configuration
DISABLE_SIGNUP=false
MAILER_AUTOCONFIRM=true

# External Authentication Providers (Optional)
# GOTRUE_EXTERNAL_GOOGLE_ENABLED=false
# GOTRUE_EXTERNAL_GOOGLE_CLIENT_ID=your_google_client_id
# GOTRUE_EXTERNAL_GOOGLE_SECRET=your_google_client_secret

# GOTRUE_EXTERNAL_GITHUB_ENABLED=false
# GOTRUE_EXTERNAL_GITHUB_CLIENT_ID=your_github_client_id
# GOTRUE_EXTERNAL_GITHUB_SECRET=your_github_client_secret

# =============================================================================
# DATA STORAGE PATHS
# =============================================================================

# Volume mount paths for persistent data
SUPABASE_DATA_PATH=./volumes/supabase-data
POSTGRES_DATA_PATH=./volumes/postgres-supabase-data
REDIS_DATA_PATH=./volumes/redis-data

# =============================================================================
# AI SERVICE CONFIGURATION (Existing)
# =============================================================================

# OpenRouter Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Ollama Configuration
OLLAMA_BASE_URL=http://host.docker.internal:11434
DEFAULT_LOCAL_PROVIDER=ollama
DEFAULT_CLOUD_PROVIDER=openrouter
ENABLE_CLOUD_FALLBACK=true

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=info

# Feature Flags
USE_SUPABASE=true
ENABLE_REALTIME=true
ENABLE_VECTOR_SEARCH=true

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:8001

# Security Configuration
ALLOWED_HOSTS=localhost,127.0.0.1,ai-orchestrator,supabase-kong

# =============================================================================
# MONITORING CONFIGURATION (Optional)
# =============================================================================

# Prometheus and Grafana
PROMETHEUS_PORT=9091
GRAFANA_PORT=3001
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=ebubulbul1989
GRAFANA_SECRET_KEY=uqUNk6ClGrGCl09xy+/MLCvQx2yxmDabkIly7/pDNp8=

# =============================================================================
# DEVELOPMENT OVERRIDES
# =============================================================================

# Development Database (separate from production)
POSTGRES_DB_DEV=ai_coding_agent_dev
POSTGRES_PASSWORD_DEV=postgres_dev_password

# Development Node Environment
NODE_ENV=development

# =============================================================================
# PRODUCTION OVERRIDES (Use docker-compose.prod.yml)
# =============================================================================

# Production URLs (uncomment and modify for production)
# SITE_URL=https://your-domain.com
# API_EXTERNAL_URL=https://api.your-domain.com
# SUPABASE_URL=https://api.your-domain.com

# Production Security (uncomment for production)
# DEBUG=false
# ENVIRONMENT=production
# DISABLE_SIGNUP=true
# MAILER_AUTOCONFIRM=false

# =============================================================================
# KEY GENERATION COMMANDS
# =============================================================================

# To generate secure JWT secret:
# openssl rand -base64 32

# To generate Supabase API keys, use the provided script:
# ./scripts/generate-supabase-keys.sh

# To generate all secrets for production:
# ./scripts/generate-production-secrets.sh