"""
Redis Service Implementation for AI Coding Agent.

This module provides Redis integration following the documented Redis Integration Guide,
including caching strategies, session management, task queues, and real-time messaging.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import json
import hashlib
import logging
import os
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Callable, Awaitable
from uuid import uuid4
from functools import lru_cache

import redis.asyncio as redis
from redis.asyncio import ConnectionPool
from fastapi import Depends

# Configure logging
logger = logging.getLogger(__name__)


class RedisManager:
    """
    Redis connection manager with async support and connection pooling.

    Provides centralized Redis connection management with health checks
    and automatic reconnection capabilities.
    """

    def __init__(self, redis_url: str):
        """Initialize Redis manager with connection URL."""
        self.redis_url = redis_url
        self._pool: Optional[ConnectionPool] = None
        self._client: Optional[redis.Redis] = None

    async def initialize(self) -> None:
        """Initialize Redis connection pool and client."""
        try:
            # Create connection pool with proper type handling
            self._pool = ConnectionPool.from_url(
                self.redis_url,
                max_connections=20,
                encoding="utf-8",
                decode_responses=True
            )

            self._client = redis.Redis(connection_pool=self._pool)

            # Test connection
            await self._client.ping()
            logger.info("Redis connection initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {str(e)}")
            # Log more details for debugging
            logger.error(f"Redis URL: {self.redis_url}")
            logger.error(f"Error type: {type(e).__name__}")
            raise

    async def close(self) -> None:
        """Close Redis connections and cleanup resources."""
        try:
            if self._client:
                await self._client.close()
            if self._pool:
                await self._pool.disconnect()
            logger.info("Redis connections closed")
        except Exception as e:
            logger.error(f"Error closing Redis connections: {str(e)}")

    @property
    def client(self) -> redis.Redis:
        """Get Redis client instance."""
        if not self._client:
            raise RuntimeError("Redis client not initialized. Call initialize() first.")
        return self._client

    async def health_check(self) -> Dict[str, Any]:
        """Perform Redis health check and return status information."""
        try:
            # Test connectivity
            await self._client.ping()

            # Get server info
            info = await self._client.info("server")
            memory_info = await self._client.info("memory")

            return {
                "status": "healthy",
                "redis_version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
                "used_memory": memory_info.get("used_memory_human"),
                "used_memory_peak": memory_info.get("used_memory_peak_human"),
                "uptime_in_seconds": info.get("uptime_in_seconds"),
            }
        except Exception as e:
            logger.error(f"Redis health check failed: {str(e)}")
            return {"status": "unhealthy", "error": str(e)}


class LLMCache:
    """
    LLM response caching implementation.

    Provides intelligent caching of LLM responses with configurable TTL
    and cache key generation based on prompt, model, and parameters.
    """

    def __init__(self, redis_client: redis.Redis, ttl: int = 3600):
        """
        Initialize LLM cache.

        Args:
            redis_client: Redis client instance
            ttl: Time to live for cached responses in seconds (default: 1 hour)
        """
        self.redis = redis_client
        self.prefix = "llm:cache"
        self.ttl = ttl

    def _generate_key(self, prompt: str, model: str, params: Dict[str, Any]) -> str:
        """
        Generate cache key from prompt, model, and parameters.

        Args:
            prompt: Input prompt text
            model: Model name
            params: Model parameters

        Returns:
            Hashed cache key string
        """
        content = json.dumps({
            "prompt": prompt,
            "model": model,
            "params": params
        }, sort_keys=True)

        key_hash = hashlib.sha256(content.encode()).hexdigest()
        return f"{self.prefix}:{key_hash}"

    async def get(self, prompt: str, model: str, params: Dict[str, Any]) -> Optional[str]:
        """
        Retrieve cached LLM response.

        Args:
            prompt: Input prompt text
            model: Model name
            params: Model parameters

        Returns:
            Cached response or None if not found
        """
        try:
            key = self._generate_key(prompt, model, params)
            response = await self.redis.get(key)

            if response:
                logger.debug(f"Cache hit for LLM request: {key[:16]}...")
                return response

            logger.debug(f"Cache miss for LLM request: {key[:16]}...")
            return None

        except Exception as e:
            logger.error(f"Error retrieving from LLM cache: {str(e)}")
            return None

    async def set(self, prompt: str, model: str, params: Dict[str, Any], response: str) -> bool:
        """
        Store LLM response in cache.

        Args:
            prompt: Input prompt text
            model: Model name
            params: Model parameters
            response: LLM response to cache

        Returns:
            True if successfully cached, False otherwise
        """
        try:
            key = self._generate_key(prompt, model, params)
            await self.redis.setex(key, self.ttl, response)

            logger.debug(f"Cached LLM response: {key[:16]}...")
            return True

        except Exception as e:
            logger.error(f"Error caching LLM response: {str(e)}")
            return False

    async def invalidate_pattern(self, pattern: str) -> int:
        """
        Invalidate cache entries matching pattern.

        Args:
            pattern: Redis key pattern to match

        Returns:
            Number of keys deleted
        """
        try:
            keys = await self.redis.keys(f"{self.prefix}:{pattern}")
            if keys:
                deleted = await self.redis.delete(*keys)
                logger.info(f"Invalidated {deleted} cache entries matching pattern: {pattern}")
                return deleted
            return 0

        except Exception as e:
            logger.error(f"Error invalidating cache pattern {pattern}: {str(e)}")
            return 0


class ValidationCache:
    """
    Code validation result caching implementation.

    Caches validation results based on code content and language
    to avoid redundant validation operations.
    """

    def __init__(self, redis_client: redis.Redis, ttl: int = 1800):
        """
        Initialize validation cache.

        Args:
            redis_client: Redis client instance
            ttl: Time to live for cached results in seconds (default: 30 minutes)
        """
        self.redis = redis_client
        self.prefix = "validation"
        self.ttl = ttl

    def _generate_code_hash(self, code: str, language: str) -> str:
        """Generate hash for code content and language."""
        content = f"{language}:{code}"
        return hashlib.sha256(content.encode()).hexdigest()

    async def get_validation(self, code: str, language: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached validation result.

        Args:
            code: Source code to validate
            language: Programming language

        Returns:
            Cached validation result or None if not found
        """
        try:
            key = f"{self.prefix}:{self._generate_code_hash(code, language)}"
            result = await self.redis.get(key)

            if result:
                logger.debug(f"Validation cache hit for {language} code")
                return json.loads(result)

            logger.debug(f"Validation cache miss for {language} code")
            return None

        except Exception as e:
            logger.error(f"Error retrieving validation cache: {str(e)}")
            return None

    async def cache_validation(self, code: str, language: str, result: Dict[str, Any]) -> bool:
        """
        Cache validation result.

        Args:
            code: Source code that was validated
            language: Programming language
            result: Validation result to cache

        Returns:
            True if successfully cached, False otherwise
        """
        try:
            key = f"{self.prefix}:{self._generate_code_hash(code, language)}"

            # Add timestamp to result
            result_with_timestamp = {
                **result,
                "cached_at": datetime.now(timezone.utc).isoformat()
            }

            await self.redis.setex(key, self.ttl, json.dumps(result_with_timestamp))
            logger.debug(f"Cached validation result for {language} code")
            return True

        except Exception as e:
            logger.error(f"Error caching validation result: {str(e)}")
            return False


class SessionManager:
    """
    User session and approval workflow management.

    Handles user sessions, approval requests, and workflow state management
    using Redis for distributed session storage.
    """

    def __init__(self, redis_client: redis.Redis):
        """
        Initialize session manager.

        Args:
            redis_client: Redis client instance
        """
        self.redis = redis_client
        self.session_ttl = 86400  # 24 hours
        self.approval_ttl = 1800  # 30 minutes

    async def create_session(self, user_id: str, data: Dict[str, Any]) -> str:
        """
        Create new user session.

        Args:
            user_id: User identifier
            data: Session data

        Returns:
            Session ID
        """
        try:
            session_id = str(uuid4())
            session_data = {
                "user_id": user_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "last_activity": datetime.now(timezone.utc).isoformat(),
                **data
            }

            await self.redis.setex(
                f"session:{session_id}",
                self.session_ttl,
                json.dumps(session_data)
            )

            # Add to user's active sessions
            await self.redis.sadd(f"user_sessions:{user_id}", session_id)

            logger.info(f"Created session {session_id} for user {user_id}")
            return session_id

        except Exception as e:
            logger.error(f"Error creating session for user {user_id}: {str(e)}")
            raise

    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve session data.

        Args:
            session_id: Session identifier

        Returns:
            Session data or None if not found
        """
        try:
            data = await self.redis.get(f"session:{session_id}")
            if data:
                session_data = json.loads(data)

                # Update last activity
                session_data["last_activity"] = datetime.now(timezone.utc).isoformat()
                await self.redis.setex(
                    f"session:{session_id}",
                    self.session_ttl,
                    json.dumps(session_data)
                )

                return session_data

            return None

        except Exception as e:
            logger.error(f"Error retrieving session {session_id}: {str(e)}")
            return None

    async def delete_session(self, session_id: str) -> bool:
        """
        Delete user session.

        Args:
            session_id: Session identifier

        Returns:
            True if session was deleted, False otherwise
        """
        try:
            # Get session data to find user_id
            session_data = await self.get_session(session_id)
            if session_data:
                user_id = session_data.get("user_id")
                if user_id:
                    await self.redis.srem(f"user_sessions:{user_id}", session_id)

            # Delete session
            deleted = await self.redis.delete(f"session:{session_id}")

            logger.info(f"Deleted session {session_id}")
            return bool(deleted)

        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {str(e)}")
            return False

    async def create_approval(self, operation: str, context: Dict[str, Any], user_id: str) -> str:
        """
        Create approval request.

        Args:
            operation: Operation requiring approval
            context: Operation context data
            user_id: User ID requesting approval

        Returns:
            Approval ID
        """
        try:
            approval_id = str(uuid4())
            approval_data = {
                "approval_id": approval_id,
                "operation": operation,
                "context": context,
                "user_id": user_id,
                "status": "pending",
                "created_at": datetime.now(timezone.utc).isoformat()
            }

            await self.redis.setex(
                f"approval:{approval_id}",
                self.approval_ttl,
                json.dumps(approval_data)
            )

            # Add to pending list
            await self.redis.lpush(f"pending_approvals:{user_id}", approval_id)

            logger.info(f"Created approval {approval_id} for operation {operation}")
            return approval_id

        except Exception as e:
            logger.error(f"Error creating approval for operation {operation}: {str(e)}")
            raise

    async def process_approval(self, approval_id: str, approved: bool, admin_id: str) -> bool:
        """
        Process approval request.

        Args:
            approval_id: Approval identifier
            approved: Whether request was approved
            admin_id: ID of admin processing the approval

        Returns:
            True if successfully processed, False otherwise
        """
        try:
            key = f"approval:{approval_id}"
            data = await self.redis.get(key)

            if not data:
                logger.warning(f"Approval {approval_id} not found")
                return False

            approval = json.loads(data)
            approval.update({
                "status": "approved" if approved else "denied",
                "processed_at": datetime.now(timezone.utc).isoformat(),
                "processed_by": admin_id
            })

            # Extend TTL for audit trail
            await self.redis.setex(key, 86400, json.dumps(approval))  # Keep for 24 hours

            logger.info(f"Processed approval {approval_id}: {'approved' if approved else 'denied'}")
            return True

        except Exception as e:
            logger.error(f"Error processing approval {approval_id}: {str(e)}")
            return False


class TaskQueue:
    """
    Redis Streams-based task queue for background processing.

    Implements distributed task processing using Redis Streams with
    consumer groups for reliable message delivery.
    """

    def __init__(self, redis_client: redis.Redis, stream_name: str = "ai_tasks"):
        """
        Initialize task queue.

        Args:
            redis_client: Redis client instance
            stream_name: Redis stream name for tasks
        """
        self.redis = redis_client
        self.stream = stream_name
        self.group = "ai_workers"
        self.consumer = f"worker_{uuid4().hex[:8]}"

    async def initialize(self) -> None:
        """Initialize consumer group for task processing."""
        try:
            await self.redis.xgroup_create(
                self.stream,
                self.group,
                id="0",
                mkstream=True
            )
            logger.info(f"Initialized task queue consumer group: {self.group}")
        except redis.ResponseError as e:
            if "BUSYGROUP" in str(e):
                logger.debug(f"Consumer group {self.group} already exists")
            else:
                logger.error(f"Error creating consumer group: {str(e)}")
                raise

    async def add_task(self, task_type: str, data: Dict[str, Any], priority: int = 0) -> str:
        """
        Add task to queue.

        Args:
            task_type: Type of task to execute
            data: Task data and parameters
            priority: Task priority (higher = more urgent)

        Returns:
            Task ID
        """
        try:
            task_id = str(uuid4())
            payload = {
                "task_id": task_id,
                "task_type": task_type,
                "data": json.dumps(data),
                "priority": str(priority),
                "created_at": datetime.now(timezone.utc).isoformat()
            }

            stream_id = await self.redis.xadd(self.stream, payload)

            logger.info(f"Added task {task_id} of type {task_type} to queue")
            return task_id

        except Exception as e:
            logger.error(f"Error adding task to queue: {str(e)}")
            raise

    async def process_tasks(self, handlers: Dict[str, Callable]) -> None:
        """
        Process tasks from queue using provided handlers.

        Args:
            handlers: Dict mapping task types to handler functions
        """
        await self.initialize()

        logger.info(f"Starting task processing with consumer {self.consumer}")

        while True:
            try:
                messages = await self.redis.xreadgroup(
                    streams={self.stream: ">"},
                    groupname=self.group,
                    consumername=self.consumer,
                    count=1,
                    block=1000
                )

                for stream, msgs in messages:
                    for msg_id, fields in msgs:
                        await self._process_message(msg_id, fields, handlers)

            except Exception as e:
                logger.error(f"Task processing error: {str(e)}")
                await asyncio.sleep(5)

    async def _process_message(
        self,
        msg_id: str,
        fields: Dict[str, str],
        handlers: Dict[str, Callable]
    ) -> None:
        """Process individual task message."""
        task_type = fields.get("task_type")
        task_id = fields.get("task_id")

        try:
            if not task_type:
                logger.warning(f"Missing task_type in message {msg_id}")
                return

            handler = handlers.get(task_type)
            if handler:
                data = json.loads(fields.get("data", "{}"))
                await handler(task_id, data)

                # Acknowledge successful processing
                await self.redis.xack(self.group, self.stream, msg_id)
                logger.debug(f"Processed task {task_id} successfully")
            else:
                logger.warning(f"No handler found for task type: {task_type}")

        except Exception as e:
            logger.error(f"Failed to process task {task_id}: {str(e)}")
            # Task will remain in pending list for retry


class RealTimeMessaging:
    """
    Redis pub/sub implementation for real-time messaging.

    Provides WebSocket-compatible real-time messaging using Redis
    publish/subscribe for broadcasting events across services.
    """

    def __init__(self, redis_client: redis.Redis):
        """
        Initialize real-time messaging.

        Args:
            redis_client: Redis client instance
        """
        self.redis = redis_client
        self.channels = {
            "validation": "ai:validation:progress",
            "tasks": "ai:tasks:updates",
            "approvals": "ai:approvals:requests",
            "system": "ai:system:events"
        }

    async def publish_validation_progress(
        self,
        task_id: str,
        progress: int,
        message: str,
        user_id: Optional[str] = None
    ) -> None:
        """
        Publish validation progress event.

        Args:
            task_id: Task identifier
            progress: Progress percentage (0-100)
            message: Progress message
            user_id: Optional user ID for targeted messaging
        """
        event = {
            "type": "validation_progress",
            "task_id": task_id,
            "progress": progress,
            "message": message,
            "user_id": user_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        try:
            await self.redis.publish(self.channels["validation"], json.dumps(event))
            logger.debug(f"Published validation progress for task {task_id}: {progress}%")
        except Exception as e:
            logger.error(f"Error publishing validation progress: {str(e)}")

    async def publish_task_update(
        self,
        task_id: str,
        status: str,
        details: Dict[str, Any],
        user_id: Optional[str] = None
    ) -> None:
        """
        Publish task status update.

        Args:
            task_id: Task identifier
            status: Task status
            details: Additional task details
            user_id: Optional user ID for targeted messaging
        """
        event = {
            "type": "task_update",
            "task_id": task_id,
            "status": status,
            "details": details,
            "user_id": user_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        try:
            await self.redis.publish(self.channels["tasks"], json.dumps(event))
            logger.debug(f"Published task update for {task_id}: {status}")
        except Exception as e:
            logger.error(f"Error publishing task update: {str(e)}")

    async def publish_approval_request(
        self,
        approval_id: str,
        operation: str,
        context: Dict[str, Any],
        user_id: str
    ) -> None:
        """
        Publish approval request event.

        Args:
            approval_id: Approval identifier
            operation: Operation requiring approval
            context: Operation context
            user_id: User requesting approval
        """
        event = {
            "type": "approval_request",
            "approval_id": approval_id,
            "operation": operation,
            "context": context,
            "user_id": user_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        try:
            await self.redis.publish(self.channels["approvals"], json.dumps(event))
            logger.info(f"Published approval request {approval_id} for operation {operation}")
        except Exception as e:
            logger.error(f"Error publishing approval request: {str(e)}")

    async def subscribe_to_events(self, callback: Callable[[Dict[str, Any]], Awaitable[None]]) -> None:
        """
        Subscribe to all event channels and process messages.

        Args:
            callback: Function to call with received events
        """
        try:
            pubsub = self.redis.pubsub()

            # Subscribe to all channels
            for channel in self.channels.values():
                await pubsub.subscribe(channel)
                logger.info(f"Subscribed to channel: {channel}")

            while True:
                message = await pubsub.get_message()
                if message and message["type"] == "message":
                    try:
                        event_data = json.loads(message["data"])
                        await callback(event_data)
                    except Exception as e:
                        logger.error(f"Error processing message: {str(e)}")

        except Exception as e:
            logger.error(f"Error in event subscription: {str(e)}")


class RateLimiter:
    """
    Redis-based rate limiting implementation.

    Provides sliding window rate limiting using Redis for
    API endpoint protection and abuse prevention.
    """

    def __init__(self, redis_client: redis.Redis):
        """
        Initialize rate limiter.

        Args:
            redis_client: Redis client instance
        """
        self.redis = redis_client

    async def check_rate_limit(self, key: str, limit: int, window: int) -> Dict[str, Any]:
        """
        Check if request is within rate limit.

        Args:
            key: Rate limiting key (e.g., user ID, IP address)
            limit: Maximum requests allowed
            window: Time window in seconds

        Returns:
            Dict with allowed status and current count
        """
        try:
            current_time = int(datetime.now(timezone.utc).timestamp())
            window_start = current_time - window

            # Remove expired entries
            await self.redis.zremrangebyscore(key, 0, window_start)

            # Count current requests
            current_count = await self.redis.zcard(key)

            if current_count < limit:
                # Add current request
                await self.redis.zadd(key, {str(uuid4()): current_time})
                await self.redis.expire(key, window)

                return {
                    "allowed": True,
                    "count": current_count + 1,
                    "limit": limit,
                    "reset_time": current_time + window
                }
            else:
                return {
                    "allowed": False,
                    "count": current_count,
                    "limit": limit,
                    "reset_time": current_time + window
                }

        except Exception as e:
            logger.error(f"Error checking rate limit for {key}: {str(e)}")
            # Fail open - allow request if Redis is unavailable
            return {"allowed": True, "count": 0, "limit": limit, "error": str(e)}


# Global Redis manager instance
redis_url = os.getenv("REDIS_URL", "redis://redis:6379/0")
redis_manager = RedisManager(redis_url)


async def get_redis_client() -> redis.Redis:
    """
    FastAPI dependency for Redis client.

    Returns:
        Redis client instance
    """
    if not redis_manager._client:
        await redis_manager.initialize()
    return redis_manager.client


@lru_cache()
def get_redis_manager() -> RedisManager:
    """
    Get Redis manager singleton.

    Returns:
        RedisManager instance
    """
    return redis_manager


# Service factory functions
def get_llm_cache(redis_client: redis.Redis = Depends(get_redis_client)) -> LLMCache:
    """Get LLM cache service."""
    return LLMCache(redis_client)


def get_validation_cache(redis_client: redis.Redis = Depends(get_redis_client)) -> ValidationCache:
    """Get validation cache service."""
    return ValidationCache(redis_client)


def get_session_manager(redis_client: redis.Redis = Depends(get_redis_client)) -> SessionManager:
    """Get session manager service."""
    return SessionManager(redis_client)


def get_task_queue(redis_client: redis.Redis = Depends(get_redis_client)) -> TaskQueue:
    """Get task queue service."""
    return TaskQueue(redis_client)


def get_realtime_messaging(redis_client: redis.Redis = Depends(get_redis_client)) -> RealTimeMessaging:
    """Get real-time messaging service."""
    return RealTimeMessaging(redis_client)


def get_rate_limiter(redis_client: redis.Redis = Depends(get_redis_client)) -> RateLimiter:
    """Get rate limiter service."""
    return RateLimiter(redis_client)