# WebSocket Real-time Communication Guide for AI Coding Agent

## Overview

This guide covers WebSocket implementation for real-time agent status broadcasting and live updates in the AI Coding Agent system, enabling immediate feedback to users about validation progress, agent activities, and task completion.

## Table of Contents

- [WebSocket Real-time Communication Guide for AI Coding Agent](#websocket-real-time-communication-guide-for-ai-coding-agent)
  - [Overview](#overview)
  - [Table of Contents](#table-of-contents)
  - [WebSocket Architecture Overview](#websocket-architecture-overview)
    - [Real-time Communication Flow](#real-time-communication-flow)
  - [Connection Management](#connection-management)
    - [WebSocket Manager Implementation](#websocket-manager-implementation)
  - [Message Broadcasting System](#message-broadcasting-system)
    - [Event Broadcasting Service](#event-broadcasting-service)
  - [Agent Status Broadcasting](#agent-status-broadcasting)
    - [Agent Event Integration](#agent-event-integration)
  - [Client Integration Patterns](#client-integration-patterns)
    - [FastAPI WebSocket Endpoints](#fastapi-websocket-endpoints)
    - [JavaScript Client Integration](#javascript-client-integration)
  - [Authentication \& Security](#authentication--security)
    - [WebSocket Authentication](#websocket-authentication)
  - [Production Considerations](#production-considerations)
    - [Performance \& Scaling](#performance--scaling)
    - [Docker Configuration](#docker-configuration)
  - [Key Takeaways](#key-takeaways)

## WebSocket Architecture Overview

### Real-time Communication Flow

```
┌─────────────────────────────────────────────────────────────┐
│                    Client Connections                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ VS Code     │  │ Admin       │  │ Web         │        │
│  │ Extension   │  │ Dashboard   │  │ Interface   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│         ↓              ↓              ↓                   │
├─────────────────────────────────────────────────────────────┤
│                  WebSocket Manager                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Connection  │  │ Message     │  │ Room        │        │
│  │ Manager     │  │ Router      │  │ Manager     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                    Event Sources                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Agent       │  │ Validation  │  │ Task Queue  │        │
│  │ Processor   │  │ Pipeline    │  │ Manager     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│                   Redis Pub/Sub                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Agent       │  │ Task        │  │ System      │        │
│  │ Events      │  │ Events      │  │ Events      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## Connection Management

### WebSocket Manager Implementation

```python
# src/services/websocket_manager.py
import asyncio
import json
import logging
from typing import Dict, List, Set, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Manages WebSocket connections and message broadcasting."""

    def __init__(self):
        # Active connections by connection ID
        self.active_connections: Dict[str, WebSocket] = {}

        # Connections organized by rooms/channels
        self.rooms: Dict[str, Set[str]] = {}

        # User to connection mapping
        self.user_connections: Dict[str, Set[str]] = {}

        # Connection metadata
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}

    async def connect(self, websocket: WebSocket, user_id: str = None, room: str = "general") -> str:
        """Accept a new WebSocket connection."""

        await websocket.accept()

        # Generate unique connection ID
        connection_id = str(uuid.uuid4())

        # Store connection
        self.active_connections[connection_id] = websocket

        # Add to room
        if room not in self.rooms:
            self.rooms[room] = set()
        self.rooms[room].add(connection_id)

        # Associate with user
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = set()
            self.user_connections[user_id].add(connection_id)

        # Store metadata
        self.connection_metadata[connection_id] = {
            'user_id': user_id,
            'room': room,
            'connected_at': datetime.utcnow(),
            'last_ping': datetime.utcnow()
        }

        logger.info(f"WebSocket connected: {connection_id} (user: {user_id}, room: {room})")

        # Send welcome message
        await self.send_personal_message({
            'type': 'connection_established',
            'connection_id': connection_id,
            'room': room,
            'timestamp': datetime.utcnow().isoformat()
        }, connection_id)

        return connection_id

    async def disconnect(self, connection_id: str):
        """Remove a WebSocket connection."""

        if connection_id not in self.active_connections:
            return

        metadata = self.connection_metadata.get(connection_id, {})
        user_id = metadata.get('user_id')
        room = metadata.get('room')

        # Remove from active connections
        del self.active_connections[connection_id]

        # Remove from room
        if room and room in self.rooms:
            self.rooms[room].discard(connection_id)
            if not self.rooms[room]:
                del self.rooms[room]

        # Remove from user connections
        if user_id and user_id in self.user_connections:
            self.user_connections[user_id].discard(connection_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]

        # Remove metadata
        if connection_id in self.connection_metadata:
            del self.connection_metadata[connection_id]

        logger.info(f"WebSocket disconnected: {connection_id}")

    async def send_personal_message(self, message: Dict[str, Any], connection_id: str):
        """Send message to specific connection."""

        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to send message to {connection_id}: {e}")
                await self.disconnect(connection_id)

    async def send_to_user(self, message: Dict[str, Any], user_id: str):
        """Send message to all connections of a specific user."""

        user_connections = self.user_connections.get(user_id, set())

        for connection_id in user_connections.copy():
            await self.send_personal_message(message, connection_id)

    async def broadcast_to_room(self, message: Dict[str, Any], room: str):
        """Broadcast message to all connections in a room."""

        room_connections = self.rooms.get(room, set())

        for connection_id in room_connections.copy():
            await self.send_personal_message(message, connection_id)

    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Broadcast message to all active connections."""

        for connection_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, connection_id)

    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""

        return {
            'total_connections': len(self.active_connections),
            'rooms': {room: len(connections) for room, connections in self.rooms.items()},
            'connected_users': len(self.user_connections),
            'connection_details': [
                {
                    'connection_id': conn_id,
                    'user_id': metadata.get('user_id'),
                    'room': metadata.get('room'),
                    'connected_at': metadata.get('connected_at').isoformat()
                }
                for conn_id, metadata in self.connection_metadata.items()
            ]
        }

# Global connection manager
connection_manager = ConnectionManager()
```

## Message Broadcasting System

### Event Broadcasting Service

```python
# src/services/event_broadcaster.py
import asyncio
import json
from typing import Dict, Any, List
from datetime import datetime
from .websocket_manager import connection_manager
from .redis_service import get_redis_client

class EventBroadcaster:
    """Broadcasts events to WebSocket clients via Redis pub/sub."""

    def __init__(self):
        self.redis_client = None
        self.subscribers = {}
        self.is_listening = False

    async def initialize(self):
        """Initialize Redis connection and start listening."""
        self.redis_client = await get_redis_client()
        await self.start_listening()

    async def start_listening(self):
        """Start listening to Redis pub/sub channels."""

        if self.is_listening:
            return

        self.is_listening = True

        # Subscribe to event channels
        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe(
            'agent_events',
            'task_events',
            'validation_events',
            'system_events'
        )

        # Start background listener
        asyncio.create_task(self._listen_for_events(pubsub))

    async def _listen_for_events(self, pubsub):
        """Listen for Redis pub/sub events and broadcast to WebSocket clients."""

        try:
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    channel = message['channel'].decode('utf-8')
                    data = json.loads(message['data'].decode('utf-8'))

                    # Route message based on channel
                    await self._route_message(channel, data)

        except Exception as e:
            logger.error(f"Error in event listener: {e}")
            self.is_listening = False

    async def _route_message(self, channel: str, data: Dict[str, Any]):
        """Route messages to appropriate WebSocket rooms."""

        # Add metadata
        event = {
            'channel': channel,
            'timestamp': datetime.utcnow().isoformat(),
            **data
        }

        if channel == 'agent_events':
            await self._broadcast_agent_event(event)
        elif channel == 'task_events':
            await self._broadcast_task_event(event)
        elif channel == 'validation_events':
            await self._broadcast_validation_event(event)
        elif channel == 'system_events':
            await self._broadcast_system_event(event)

    async def _broadcast_agent_event(self, event: Dict[str, Any]):
        """Broadcast agent-related events."""

        event_type = event.get('type')
        user_id = event.get('user_id')

        if event_type in ['agent_started', 'agent_progress', 'agent_completed', 'agent_failed']:
            # Send to specific user if specified
            if user_id:
                await connection_manager.send_to_user(event, user_id)

            # Also broadcast to admin room
            await connection_manager.broadcast_to_room(event, 'admin')

    async def _broadcast_task_event(self, event: Dict[str, Any]):
        """Broadcast task-related events."""

        # Broadcast to general room and admin room
        await connection_manager.broadcast_to_room(event, 'general')
        await connection_manager.broadcast_to_room(event, 'admin')

    async def _broadcast_validation_event(self, event: Dict[str, Any]):
        """Broadcast validation events."""

        user_id = event.get('user_id')

        # Send to specific user
        if user_id:
            await connection_manager.send_to_user(event, user_id)

    async def _broadcast_system_event(self, event: Dict[str, Any]):
        """Broadcast system-wide events."""

        # Send to all connected clients
        await connection_manager.broadcast_to_all(event)

    # Public methods for publishing events
    async def publish_agent_event(self, event_type: str, data: Dict[str, Any]):
        """Publish agent event to Redis."""

        event = {
            'type': event_type,
            'timestamp': datetime.utcnow().isoformat(),
            **data
        }

        await self.redis_client.publish('agent_events', json.dumps(event))

    async def publish_task_event(self, event_type: str, data: Dict[str, Any]):
        """Publish task event to Redis."""

        event = {
            'type': event_type,
            'timestamp': datetime.utcnow().isoformat(),
            **data
        }

        await self.redis_client.publish('task_events', json.dumps(event))

    async def publish_validation_event(self, event_type: str, data: Dict[str, Any]):
        """Publish validation event to Redis."""

        event = {
            'type': event_type,
            'timestamp': datetime.utcnow().isoformat(),
            **data
        }

        await self.redis_client.publish('validation_events', json.dumps(event))

# Global event broadcaster
event_broadcaster = EventBroadcaster()
```

## Agent Status Broadcasting

### Agent Event Integration

```python
# src/agents/event_mixin.py
from typing import Dict, Any
from ..services.event_broadcaster import event_broadcaster

class AgentEventMixin:
    """Mixin for agents to broadcast status updates."""

    async def broadcast_agent_started(self, task_id: str, user_id: str = None):
        """Broadcast agent started event."""

        await event_broadcaster.publish_agent_event('agent_started', {
            'agent_type': self.agent_type,
            'task_id': task_id,
            'user_id': user_id,
            'message': f'{self.agent_type} agent started processing task'
        })

    async def broadcast_agent_progress(self, task_id: str, progress: float, message: str, user_id: str = None):
        """Broadcast agent progress update."""

        await event_broadcaster.publish_agent_event('agent_progress', {
            'agent_type': self.agent_type,
            'task_id': task_id,
            'user_id': user_id,
            'progress': progress,
            'message': message
        })

    async def broadcast_agent_completed(self, task_id: str, result: Dict[str, Any], user_id: str = None):
        """Broadcast agent completion."""

        await event_broadcaster.publish_agent_event('agent_completed', {
            'agent_type': self.agent_type,
            'task_id': task_id,
            'user_id': user_id,
            'result': result,
            'message': f'{self.agent_type} agent completed task successfully'
        })

    async def broadcast_agent_failed(self, task_id: str, error: str, user_id: str = None):
        """Broadcast agent failure."""

        await event_broadcaster.publish_agent_event('agent_failed', {
            'agent_type': self.agent_type,
            'task_id': task_id,
            'user_id': user_id,
            'error': error,
            'message': f'{self.agent_type} agent failed: {error}'
        })

# Updated base agent with events
class BaseAgent(AgentEventMixin):
    """Base agent class with event broadcasting."""

    async def execute_task(self, payload: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task with event broadcasting."""

        task_id = context.get('task_id')
        user_id = context.get('user_id')

        try:
            # Broadcast start
            await self.broadcast_agent_started(task_id, user_id)

            # Progress updates during execution
            await self.broadcast_agent_progress(task_id, 0.1, "Initializing...", user_id)

            # Execute actual task
            result = await self._execute_task_implementation(payload, context)

            # Broadcast completion
            await self.broadcast_agent_completed(task_id, result, user_id)

            return result

        except Exception as e:
            # Broadcast failure
            await self.broadcast_agent_failed(task_id, str(e), user_id)
            raise
```

## Client Integration Patterns

### FastAPI WebSocket Endpoints

```python
# src/api/websocket_endpoints.py
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from typing import Optional
from ..services.websocket_manager import connection_manager
from ..services.auth_service import get_current_user_websocket

router = APIRouter()

@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    room: str = Query(default="general"),
    token: Optional[str] = Query(default=None)
):
    """Main WebSocket endpoint for real-time updates."""

    user_id = None

    # Authenticate if token provided
    if token:
        try:
            user_id = await get_current_user_websocket(token)
        except Exception:
            await websocket.close(code=1008, reason="Invalid authentication")
            return

    # Connect to WebSocket manager
    connection_id = await connection_manager.connect(websocket, user_id, room)

    try:
        while True:
            # Handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)

            # Process message based on type
            await handle_websocket_message(message, connection_id, user_id)

    except WebSocketDisconnect:
        await connection_manager.disconnect(connection_id)

async def handle_websocket_message(message: Dict[str, Any], connection_id: str, user_id: str):
    """Handle incoming WebSocket messages."""

    message_type = message.get('type')

    if message_type == 'ping':
        # Respond to ping
        await connection_manager.send_personal_message({
            'type': 'pong',
            'timestamp': datetime.utcnow().isoformat()
        }, connection_id)

    elif message_type == 'subscribe_task':
        # Subscribe to specific task updates
        task_id = message.get('task_id')
        # Implementation for task-specific subscriptions

    elif message_type == 'get_status':
        # Send current system status
        stats = await connection_manager.get_connection_stats()
        await connection_manager.send_personal_message({
            'type': 'status_response',
            'data': stats
        }, connection_id)

@router.get("/ws/stats")
async def get_websocket_stats():
    """Get WebSocket connection statistics."""
    return await connection_manager.get_connection_stats()
```

### JavaScript Client Integration

```javascript
// VS Code Extension WebSocket Client
class AgentStatusClient {
    constructor(baseUrl, authToken) {
        this.baseUrl = baseUrl;
        this.authToken = authToken;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.listeners = new Map();
    }

    connect() {
        const wsUrl = `${this.baseUrl}/ws?room=general&token=${this.authToken}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.reconnectAttempts = 0;
            this.emit('connected');
        };

        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };

        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.emit('disconnected');
            this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.emit('error', error);
        };
    }

    handleMessage(message) {
        const { type, channel } = message;

        // Emit specific events based on message type
        this.emit(type, message);

        if (channel === 'agent_events') {
            this.handleAgentEvent(message);
        } else if (channel === 'validation_events') {
            this.handleValidationEvent(message);
        }
    }

    handleAgentEvent(event) {
        const { type, agent_type, progress, message: msg } = event;

        if (type === 'agent_progress') {
            this.emit('agentProgress', {
                agentType: agent_type,
                progress: progress,
                message: msg
            });
        }
    }

    handleValidationEvent(event) {
        const { type, stage, passed, issues } = event;

        this.emit('validationUpdate', {
            type,
            stage,
            passed,
            issues
        });
    }

    subscribe(eventType, callback) {
        if (!this.listeners.has(eventType)) {
            this.listeners.set(eventType, []);
        }
        this.listeners.get(eventType).push(callback);
    }

    emit(eventType, data) {
        const callbacks = this.listeners.get(eventType) || [];
        callbacks.forEach(callback => callback(data));
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = Math.pow(2, this.reconnectAttempts) * 1000;

            setTimeout(() => {
                console.log(`Reconnecting... (attempt ${this.reconnectAttempts})`);
                this.connect();
            }, delay);
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
}

// Usage in VS Code Extension
const statusClient = new AgentStatusClient('ws://localhost:8000', authToken);

statusClient.subscribe('agentProgress', (data) => {
    // Update progress indicator in VS Code
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: `${data.agentType} Agent`,
        cancellable: false
    }, (progress) => {
        progress.report({
            increment: data.progress * 100,
            message: data.message
        });
    });
});

statusClient.connect();
```

## Authentication & Security

### WebSocket Authentication

```python
# src/services/auth_service.py
async def get_current_user_websocket(token: str) -> str:
    """Authenticate WebSocket connection using JWT token."""

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        user_id = payload.get("sub")

        if user_id is None:
            raise WebSocketException(code=1008, reason="Invalid token")

        return user_id

    except JWTError:
        raise WebSocketException(code=1008, reason="Token validation failed")

# Rate limiting for WebSocket connections
class WebSocketRateLimiter:
    def __init__(self, max_connections_per_user: int = 5):
        self.max_connections_per_user = max_connections_per_user
        self.user_connection_count = {}

    async def check_rate_limit(self, user_id: str) -> bool:
        current_count = self.user_connection_count.get(user_id, 0)
        return current_count < self.max_connections_per_user

    async def increment_connection(self, user_id: str):
        self.user_connection_count[user_id] = self.user_connection_count.get(user_id, 0) + 1

    async def decrement_connection(self, user_id: str):
        if user_id in self.user_connection_count:
            self.user_connection_count[user_id] = max(0, self.user_connection_count[user_id] - 1)
```

## Production Considerations

### Performance & Scaling

```python
# Connection pooling and load balancing considerations
class ProductionWebSocketManager:
    """Production-ready WebSocket manager with Redis backing."""

    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.local_connections = {}
        self.server_id = str(uuid.uuid4())

    async def connect(self, websocket: WebSocket, user_id: str, room: str) -> str:
        connection_id = await super().connect(websocket, user_id, room)

        # Store connection info in Redis for multi-server setup
        await self.redis_client.hset(
            f"ws_connection:{connection_id}",
            mapping={
                'server_id': self.server_id,
                'user_id': user_id or '',
                'room': room,
                'connected_at': datetime.utcnow().isoformat()
            }
        )

        return connection_id

    async def broadcast_across_servers(self, message: Dict[str, Any], room: str):
        """Broadcast message across multiple server instances."""

        # Publish to Redis channel for other servers
        await self.redis_client.publish(
            f"ws_broadcast:{room}",
            json.dumps({
                'source_server': self.server_id,
                'message': message
            })
        )
```

### Docker Configuration

```yaml
# docker-compose.yml additions
services:
  ai-orchestrator:
    # ... existing config
    environment:
      - WEBSOCKET_MAX_CONNECTIONS=1000
      - WEBSOCKET_PING_INTERVAL=30
      - WEBSOCKET_PONG_TIMEOUT=10
    deploy:
      resources:
        limits:
          memory: 2G
```

## Key Takeaways

1. **Real-time Updates**: WebSocket connections provide immediate feedback on agent status and task progress
2. **Room-based Broadcasting**: Messages routed to specific user groups or rooms for targeted communication
3. **Redis Integration**: Pub/sub pattern enables scalable event distribution across multiple services
4. **Agent Integration**: Agents broadcast status updates throughout their execution lifecycle
5. **Client Libraries**: JavaScript/TypeScript clients for VS Code extension and web interfaces
6. **Authentication**: JWT-based authentication for secure WebSocket connections
7. **Rate Limiting**: Protection against connection abuse and resource exhaustion
8. **Production Ready**: Designed for horizontal scaling with Redis backing
9. **Error Recovery**: Automatic reconnection with exponential backoff
10. **Multi-channel**: Separate channels for different event types (agents, tasks, validation)

This WebSocket implementation provides the real-time communication foundation needed for responsive user feedback in the AI Coding Agent system.