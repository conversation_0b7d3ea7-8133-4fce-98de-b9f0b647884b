# PostgreSQL configuration for AI Coding Agent
# Optimized for containerized AI workloads

# Memory Settings (adjust based on container memory limits)
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 16MB
maintenance_work_mem = 128MB

# Logging Settings for debugging
log_statement = 'all'
log_destination = 'stderr'
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_min_messages = info
log_min_error_statement = error
log_min_duration_statement = 1000

# Connection Settings
max_connections = 100
listen_addresses = '*'

# Performance Settings for AI workloads
random_page_cost = 1.1
effective_io_concurrency = 200
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Vector Extension Settings (for pgvector)
shared_preload_libraries = 'vector'
max_locks_per_transaction = 64

# Security Settings
ssl = off
password_encryption = scram-sha-256