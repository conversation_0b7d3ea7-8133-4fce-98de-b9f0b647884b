# Project: AI Coding Agent
# Purpose: Production-ready utility functions for the AI Orchestrator

import os
import sys
import time
import hashlib
import logging
import uuid
import re
import platform
from typing import Any, Dict, Optional, Union, List
from pathlib import Path
import json
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

def generate_request_id(user_id: Optional[str] = None, content: Optional[str] = None) -> str:
    """
    Generate a unique request ID for tracking and correlation.

    This function generates a UUID4-based request ID that can be used for:
    - Request tracing across services
    - Log correlation
    - Error tracking
    - Performance monitoring

    Args:
        user_id: Optional user identifier (for backward compatibility)
        content: Optional content preview (for backward compatibility)

    Returns:
        str: Unique request ID in format 'req-{uuid4}'

    Examples:
        >>> generate_request_id()
        'req-a1b2c3d4-e5f6-7890-abcd-ef1234567890'
        >>> generate_request_id("user123", "some content")
        'req-f9e8d7c6-b5a4-3210-9876-543210fedcba'
    """
    # Generate a UUID4 for guaranteed uniqueness
    request_uuid = uuid.uuid4()

    # Format as request ID with prefix for easy identification
    request_id = f"req-{request_uuid}"

    # Log the generation for debugging (without sensitive content)
    logger.debug(f"Generated request ID: {request_id}")

    return request_id

def sanitize_filename(filename: str, max_length: int = 255, replacement_char: str = '_') -> str:
    """
    Sanitize filename for safe file operations with comprehensive security checks.

    This function removes or replaces dangerous characters and patterns that could
    be used for directory traversal attacks, command injection, or other security
    vulnerabilities.

    Args:
        filename: The filename to sanitize
        max_length: Maximum allowed filename length (default: 255)
        replacement_char: Character to replace unsafe characters with

    Returns:
        str: Sanitized filename safe for file operations

    Examples:
        >>> sanitize_filename("../../../etc/passwd")
        'etc_passwd'
        >>> sanitize_filename("file<>name.txt")
        'file__name.txt'
        >>> sanitize_filename("CON.txt")  # Windows reserved name
        'CON_.txt'
    """
    if not filename or not isinstance(filename, str):
        return "unnamed_file"

    # Convert to string and strip whitespace
    filename = str(filename).strip()

    if not filename:
        return "unnamed_file"

    # Remove directory traversal patterns
    filename = re.sub(r'\.\.+[/\\]', '', filename)  # Remove ../ and ..\
    filename = re.sub(r'^\.\.+', '', filename)      # Remove leading dots

    # Remove or replace unsafe characters
    # Windows: < > : " | ? * / \
    # Unix: / (null byte handled separately)
    unsafe_chars = r'[<>:"|?*\\/\x00-\x1f\x7f-\x9f]'
    filename = re.sub(unsafe_chars, replacement_char, filename)

    # Remove control characters and non-printable characters
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', replacement_char, filename)

    # Handle Windows reserved names (CON, PRN, AUX, NUL, COM1-9, LPT1-9)
    windows_reserved = [
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    ]

    # Check if filename (without extension) is a reserved name
    name_part = filename.split('.')[0].upper()
    if name_part in windows_reserved:
        filename = f"{filename}{replacement_char}"

    # Remove leading/trailing dots and spaces (Windows doesn't allow these)
    filename = filename.strip('. ')

    # Ensure filename is not empty after sanitization
    if not filename:
        filename = "sanitized_file"

    # Truncate to maximum length while preserving extension if possible
    if len(filename) > max_length:
        if '.' in filename:
            name, ext = filename.rsplit('.', 1)
            max_name_length = max_length - len(ext) - 1
            if max_name_length > 0:
                filename = f"{name[:max_name_length]}.{ext}"
            else:
                filename = filename[:max_length]
        else:
            filename = filename[:max_length]

    # Final validation - ensure no empty filename
    if not filename or filename in ['.', '..']:
        filename = "safe_filename"

    logger.debug(f"Sanitized filename: '{filename}'")
    return filename

def ensure_directory(path: Union[str, Path]) -> Path:
    """Ensure directory exists, create if it doesn't."""
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path

def safe_json_loads(json_str: str, default: Any = None, max_size: int = 10_000_000) -> Any:
    """
    Safely parse JSON string with comprehensive error handling and security checks.

    Args:
        json_str: JSON string to parse
        default: Default value to return on failure
        max_size: Maximum allowed JSON string size (default: 10MB)

    Returns:
        Any: Parsed JSON data or default value
    """
    if not json_str:
        return default

    # Security check: prevent extremely large JSON strings
    if len(json_str) > max_size:
        logger.warning(f"JSON string too large: {len(json_str)} bytes (max: {max_size})")
        return default

    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.warning(f"Failed to parse JSON: {e} (position: {e.pos if hasattr(e, 'pos') else 'unknown'})")
        return default
    except (TypeError, ValueError) as e:
        logger.warning(f"Failed to parse JSON: {e}")
        return default
    except Exception as e:
        logger.error(f"Unexpected error parsing JSON: {e}")
        return default


def safe_json_dumps(data: Any, default: str = "{}", indent: Optional[int] = None,
                   ensure_ascii: bool = False) -> str:
    """
    Safely serialize data to JSON with comprehensive error handling.

    Args:
        data: Data to serialize
        default: Default JSON string to return on failure
        indent: JSON indentation for pretty printing
        ensure_ascii: Whether to escape non-ASCII characters

    Returns:
        str: JSON string or default value
    """
    if data is None:
        return "null"

    try:
        return json.dumps(
            data,
            default=str,
            ensure_ascii=ensure_ascii,
            indent=indent,
            separators=(',', ':') if indent is None else None
        )
    except (TypeError, ValueError) as e:
        logger.warning(f"Failed to serialize JSON: {e}")
        return default
    except Exception as e:
        logger.error(f"Unexpected error serializing JSON: {e}")
        return default

def get_environment_info() -> Dict[str, Any]:
    """
    Get comprehensive environment information for debugging and monitoring.

    This function collects system information while being careful not to
    expose sensitive data like API keys, passwords, or tokens.

    Returns:
        Dict[str, Any]: Environment information dictionary
    """
    try:
        return {
            "python_version": sys.version,
            "python_executable": sys.executable,
            "platform": {
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "architecture": platform.architecture()[0]
            },
            "paths": {
                "cwd": os.getcwd(),
                "python_path": sys.path[:3],  # First 3 entries only
            },
            "process": {
                "pid": os.getpid(),
                "uid": getattr(os, 'getuid', lambda: 'N/A')(),
                "gid": getattr(os, 'getgid', lambda: 'N/A')(),
            },
            "environment_variables": {
                key: value for key, value in os.environ.items()
                if not any(sensitive in key.upper() for sensitive in [
                    'KEY', 'SECRET', 'PASSWORD', 'TOKEN', 'AUTH', 'CREDENTIAL',
                    'PRIVATE', 'CERT', 'SSL', 'TLS', 'API_KEY'
                ])
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.warning(f"Failed to collect complete environment info: {e}")
        return {
            "python_version": sys.version,
            "platform": os.name,
            "cwd": os.getcwd(),
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

def format_bytes(bytes_count: int) -> str:
    """Format bytes into human readable format."""
    size = float(bytes_count)
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} PB"

def is_valid_email(email: str, strict: bool = True) -> bool:
    """
    Validate email address format with comprehensive checks.

    This function performs robust email validation following RFC 5322 guidelines
    with additional security checks to prevent common attack vectors.

    Args:
        email: Email address to validate
        strict: If True, applies stricter validation rules

    Returns:
        bool: True if email is valid, False otherwise

    Examples:
        >>> is_valid_email("<EMAIL>")
        True
        >>> is_valid_email("invalid.email")
        False
        >>> is_valid_email("<EMAIL>")
        True
        >>> is_valid_email("user@localhost", strict=False)
        True
    """
    if not email or not isinstance(email, str):
        return False

    # Basic length check (RFC 5321 limits)
    if len(email) > 254:  # Maximum email length
        return False

    # Must contain exactly one @ symbol
    if email.count('@') != 1:
        return False

    try:
        local_part, domain_part = email.rsplit('@', 1)
    except ValueError:
        return False

    # Validate local part (before @)
    if not _validate_email_local_part(local_part):
        return False

    # Validate domain part (after @)
    if not _validate_email_domain_part(domain_part, strict):
        return False

    return True


def _validate_email_local_part(local_part: str) -> bool:
    """
    Validate the local part of an email address (before @).

    Args:
        local_part: The local part of the email

    Returns:
        bool: True if valid, False otherwise
    """
    if not local_part or len(local_part) > 64:  # RFC 5321 limit
        return False

    # Cannot start or end with a dot
    if local_part.startswith('.') or local_part.endswith('.'):
        return False

    # Cannot have consecutive dots
    if '..' in local_part:
        return False

    # Allowed characters in local part
    # Letters, digits, and these special characters: . _ % + -
    allowed_pattern = r'^[a-zA-Z0-9._+%-]+$'
    if not re.match(allowed_pattern, local_part):
        return False

    return True


def _validate_email_domain_part(domain_part: str, strict: bool = True) -> bool:
    """
    Validate the domain part of an email address (after @).

    Args:
        domain_part: The domain part of the email
        strict: If True, requires valid TLD

    Returns:
        bool: True if valid, False otherwise
    """
    if not domain_part or len(domain_part) > 253:  # RFC 1035 limit
        return False

    # Cannot start or end with a dot or hyphen
    if domain_part.startswith('.') or domain_part.endswith('.'):
        return False
    if domain_part.startswith('-') or domain_part.endswith('-'):
        return False

    # Split into labels (parts separated by dots)
    labels = domain_part.split('.')

    if len(labels) < 2 and strict:  # Must have at least domain.tld in strict mode
        return False

    for label in labels:
        if not label:  # Empty label (consecutive dots)
            return False
        if len(label) > 63:  # RFC 1035 limit for each label
            return False

        # Each label must start and end with alphanumeric
        if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?$', label):
            return False

    # In strict mode, check that TLD (last label) is valid
    if strict and len(labels) >= 2:
        tld = labels[-1].lower()
        # TLD must be at least 2 characters and contain only letters
        if len(tld) < 2 or not re.match(r'^[a-zA-Z]+$', tld):
            return False

    return True

def truncate_string(text: str, max_length: int = 100, suffix: str = "...",
                   word_boundary: bool = True) -> str:
    """
    Truncate string to specified length with intelligent word boundary handling.

    Args:
        text: Text to truncate
        max_length: Maximum length including suffix
        suffix: Suffix to append when truncating
        word_boundary: If True, try to break at word boundaries

    Returns:
        str: Truncated string

    Examples:
        >>> truncate_string("This is a long sentence", 15)
        'This is a...'
        >>> truncate_string("This is a long sentence", 15, word_boundary=False)
        'This is a lo...'
    """
    if not text or not isinstance(text, str):
        return ""

    if len(text) <= max_length:
        return text

    if max_length <= len(suffix):
        return suffix[:max_length]

    truncate_at = max_length - len(suffix)

    if word_boundary:
        # Try to find a word boundary
        truncated = text[:truncate_at]
        last_space = truncated.rfind(' ')
        if last_space > max_length // 2:  # Only use word boundary if it's not too short
            truncated = truncated[:last_space]
        return truncated + suffix
    else:
        return text[:truncate_at] + suffix


def merge_dicts(*dicts: Dict[str, Any], deep: bool = False) -> Dict[str, Any]:
    """
    Merge multiple dictionaries with comprehensive handling.

    Args:
        *dicts: Variable number of dictionaries to merge
        deep: If True, perform deep merge for nested dictionaries

    Returns:
        Dict[str, Any]: Merged dictionary with later ones taking precedence

    Examples:
        >>> merge_dicts({"a": 1}, {"b": 2}, {"a": 3})
        {"a": 3, "b": 2}
        >>> merge_dicts({"a": {"x": 1}}, {"a": {"y": 2}}, deep=True)
        {"a": {"x": 1, "y": 2}}
    """
    if not dicts:
        return {}

    result = {}

    for d in dicts:
        if not isinstance(d, dict):
            continue

        if deep:
            result = _deep_merge_dicts(result, d)
        else:
            result.update(d)

    return result


def _deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Recursively merge two dictionaries.

    Args:
        dict1: First dictionary
        dict2: Second dictionary (takes precedence)

    Returns:
        Dict[str, Any]: Deep merged dictionary
    """
    result = dict1.copy()

    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = _deep_merge_dicts(result[key], value)
        else:
            result[key] = value

    return result
