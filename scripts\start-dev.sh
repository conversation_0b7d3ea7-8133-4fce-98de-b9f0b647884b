#!/bin/bash
# Start streamlined development environment
# Usage: ./scripts/start-dev.sh [full]

set -e

echo "🚀 Starting AI Coding Agent Development Environment"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "✅ Created .env file. Please edit it with your configuration."
fi

# Check for environment flags
if [ "$1" = "full" ]; then
    echo "🔧 Starting full-featured environment (11 containers)..."
    docker-compose -f docker-compose.dev.yml -f docker-compose.full.yml up -d
    echo "📊 Full environment started!"
    echo "   - Development services: http://localhost:8080 (code-server)"
    echo "   - Monitoring services:"
    echo "     * Prometheus: http://localhost:9090"
    echo "     * Grafana: http://localhost:3001"
    echo "     * Elasticsearch: http://localhost:9200"
    echo "     * Kibana: http://localhost:5601"
elif [ "$1" = "watch" ]; then
    echo "👁️  Starting development environment with watch mode..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    echo "🔄 Starting watch mode for live code reloading..."
    docker-compose watch
else
    echo "⚡ Starting ultra-streamlined development environment (4 containers)..."
    docker-compose up -d
    echo "✅ Ultra-streamlined environment started!"
    echo "   - code-server: http://localhost:8080"
    echo "   - AI Orchestrator API: http://localhost:8000"
    echo "   - Admin Dashboard: http://localhost:3000"
fi

echo ""
echo "📋 To access the development container:"
if [ "$1" = "full" ]; then
    echo "   docker-compose -f docker-compose.dev.yml -f docker-compose.full.yml exec ai-orchestrator bash"
else
    echo "   docker-compose exec ai-orchestrator bash"
fi

echo ""
echo "🔥 Hot-Reload Development:"
echo "   - Code-Server: http://localhost:8080 (Edit Python files)"
echo "   - API Docs: http://localhost:8000/docs (View API changes)"
echo "   - Test Hot-Reload: ./scripts/test-hot-reload.sh"
echo "   - Watch Logs: docker-compose logs -f ai-orchestrator"

echo ""
echo "🛑 To stop the environment:"
if [ "$1" = "full" ]; then
    echo "   docker-compose -f docker-compose.dev.yml -f docker-compose.full.yml down"
else
    echo "   docker-compose down"
fi
