#!/bin/bash
# Ollama container entrypoint script
# Handles model preloading and service startup

set -e

echo "Starting Ollama container..."
echo "Ollama host: $OLLAMA_HOST"
echo "Ollama port: $OLLAMA_PORT"

# Start Ollama service in background
echo "Starting Ollama service..."
ollama serve &
OLLAMA_PID=$!

# Wait for <PERSON>lla<PERSON> to be ready
echo "Waiting for Ollama to start..."
for i in {1..30}; do
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        echo "Ollama is ready!"
        break
    fi
    echo "Waiting for Ollama... ($i/30)"
    sleep 2
done

# Check if model manifests exist and preload models
if [ -f "/opt/ollama/models/models.json" ]; then
    echo "Found model manifest, preloading models..."

    # Parse models.json and pull each model
    if command -v jq > /dev/null 2>&1; then
        models=$(jq -r '.models[].name' /opt/ollama/models/models.json)
        for model in $models; do
            echo "Pulling model: $model"
            ollama pull "$model" || echo "Failed to pull $model, continuing..."
        done
    else
        echo "jq not available, skipping automatic model loading"
        echo "To load models manually, use: ollama pull <model-name>"
    fi
else
    echo "No model manifest found at /opt/ollama/models/models.json"
    echo "Models can be loaded manually with: ollama pull <model-name>"
fi

echo "Ollama initialization complete!"
echo "Available at: http://localhost:11434"

# Keep the service running
wait $OLLAMA_PID